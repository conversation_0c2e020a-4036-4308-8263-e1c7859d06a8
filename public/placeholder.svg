<svg id="Group_5056" data-name="Group 5056" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="121.635" height="155.689" viewBox="0 0 121.635 155.689">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_4258" data-name="Rectangle 4258" width="12.837" height="18.441" fill="#888faa"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <rect id="Rectangle_4259" data-name="Rectangle 4259" width="21.831" height="68.597" fill="#cfd2de"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <rect id="Rectangle_4260" data-name="Rectangle 4260" width="79.891" height="124.586" fill="#cfd2de"/>
    </clipPath>
    <clipPath id="clip-path-4">
      <rect id="Rectangle_4261" data-name="Rectangle 4261" width="20.208" height="27.134" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-5">
      <rect id="Rectangle_4262" data-name="Rectangle 4262" width="11.534" height="10.362" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-6">
      <rect id="Rectangle_4263" data-name="Rectangle 4263" width="8.924" height="5.403" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-7">
      <rect id="Rectangle_4264" data-name="Rectangle 4264" width="7.238" height="3.82" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-8">
      <rect id="Rectangle_4265" data-name="Rectangle 4265" width="5.313" height="12.994" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-9">
      <rect id="Rectangle_4266" data-name="Rectangle 4266" width="40.245" height="57.815" fill="#888faa"/>
    </clipPath>
  </defs>
  <g id="Group_5057" data-name="Group 5057" transform="translate(0)">
    <g id="Group_5054" data-name="Group 5054">
      <g id="Group_5058" data-name="Group 5058">
        <path id="Path_10221" data-name="Path 10221" d="M153.85,71.5s7.35-.424,7.35-7.464V15.7a19.432,19.432,0,0,1,4.808-12.8h-37.73a13.357,13.357,0,0,0-13.357,13.357V32.222S118.424,71.5,133.5,71.5" transform="translate(-44.374 -1.121)" fill="#f4f5f6"/>
        <g id="Group_5028" data-name="Group 5028" transform="translate(83.311 14.765)" opacity="0.26">
          <g id="Group_5027" data-name="Group 5027">
            <g id="Group_5026" data-name="Group 5026" clip-path="url(#clip-path)">
              <path id="Path_10222" data-name="Path 10222" d="M138.96,36.185a.3.3,0,0,1-.069-.228l.06-.62a3.5,3.5,0,0,1,.55-1.656,3.979,3.979,0,0,1,1.042-1.074q.587-.4,1.522-.91a8.154,8.154,0,0,0,1.539-.948,1.548,1.548,0,0,0,.559-1.068,1.3,1.3,0,0,0-.438-1.151,2.4,2.4,0,0,0-1.409-.539,2.5,2.5,0,0,0-1.607.3,1.429,1.429,0,0,0-.694,1.12l-.027.285q-.028.285-.338.254l-3.656-.506a.282.282,0,0,1-.209-.125.341.341,0,0,1-.068-.241l.007-.077a5.134,5.134,0,0,1,1.1-2.8,5.515,5.515,0,0,1,2.5-1.73,8.715,8.715,0,0,1,6.7.59,5.02,5.02,0,0,1,2,2.03,4.93,4.93,0,0,1,.5,2.8,4.344,4.344,0,0,1-.6,1.937,4.477,4.477,0,0,1-1.122,1.236,13.1,13.1,0,0,1-1.593.969,8.279,8.279,0,0,0-1.444.918,1.523,1.523,0,0,0-.545,1.056l-.035.362a.31.31,0,0,1-.339.281l-3.67-.352a.3.3,0,0,1-.211-.112m-.215,5.51a2.247,2.247,0,0,1,1.96-3.712,2.223,2.223,0,0,1,1.556.775,2.272,2.272,0,0,1-1.987,3.723,2.166,2.166,0,0,1-1.529-.786" transform="translate(-135.714 -24.052)" fill="#888faa"/>
            </g>
          </g>
        </g>
        <g id="Group_5031" data-name="Group 5031" transform="translate(70.547 1.781)" opacity="0.33">
          <g id="Group_5030" data-name="Group 5030">
            <g id="Group_5029" data-name="Group 5029" clip-path="url(#clip-path-2)">
              <path id="Path_10223" data-name="Path 10223" d="M118.171,32.222V16.259A13.356,13.356,0,0,1,131.528,2.9h-3.25a13.357,13.357,0,0,0-13.357,13.357V32.222S118.424,71.5,133.5,71.5h3.25c-15.077,0-18.58-39.277-18.58-39.277" transform="translate(-114.921 -2.902)" fill="#cfd2de"/>
            </g>
          </g>
        </g>
        <path id="Path_10224" data-name="Path 10224" d="M142.2,89.942H162.55s-8.243,0-8.243-7.341V57.243a6.741,6.741,0,0,0-2.3-5.161,5.891,5.891,0,0,0-2.589-1.293c-3.606-.772-7.212,2.319-7.212,2.319l-4.765,8.242Z" transform="translate(-53.067 -19.563)" fill="#cfd2de"/>
        <path id="Path_10225" data-name="Path 10225" d="M105.1,50.668s-6.354-.343-6.354,7.126V166.053a9.133,9.133,0,0,1-8.622,9.188c-.171.007-.345.011-.522.011h-64.4V68.2A17.538,17.538,0,0,1,42.75,50.667Z" transform="translate(-9.735 -19.564)" fill="#f4f5f6"/>
        <g id="Group_5034" data-name="Group 5034" transform="translate(15.477 31.102)" opacity="0.33">
          <g id="Group_5033" data-name="Group 5033">
            <g id="Group_5032" data-name="Group 5032" clip-path="url(#clip-path-3)">
              <path id="Path_10226" data-name="Path 10226" d="M155.183,50.668c-.012,0-.109,0-.272,0Z" transform="translate(-75.292 -50.666)" fill="#cfd2de"/>
              <path id="Path_10227" data-name="Path 10227" d="M46.6,50.667H42.75A17.537,17.537,0,0,0,25.212,68.2V175.253h3.859V68.2A17.538,17.538,0,0,1,46.6,50.667" transform="translate(-25.212 -50.666)" fill="#cfd2de"/>
            </g>
          </g>
        </g>
        <path id="Path_10228" data-name="Path 10228" d="M80.407,243.875s-6.1-1.116-6.1-6.611v-8.876H.385v5.227a10.26,10.26,0,0,0,10.26,10.26Z" transform="translate(-0.149 -88.187)" fill="#cfd2de"/>
        <path id="Path_10229" data-name="Path 10229" d="M162.106,200.429c-3.253,2.511-8.316,1.083-10.661-3l-2.888-5.031-2.017-3.518-5.569-9.711-.454-.791,2.691-2.079,3.023-2.335,4.013,3.945,4.2,4.127.008,0,3.624,3.56,3.823,3.754c3.362,3.3,3.462,8.564.208,11.076" transform="translate(-54.258 -67.171)" fill="#888faa"/>
        <g id="Group_5037" data-name="Group 5037" transform="translate(90.018 106.791)" opacity="0.2" style="mix-blend-mode: multiply;isolation: isolate">
          <g id="Group_5036" data-name="Group 5036">
            <g id="Group_5035" data-name="Group 5035" clip-path="url(#clip-path-4)">
              <path id="Path_10230" data-name="Path 10230" d="M164.47,200.43a5.746,5.746,0,0,1-1.088.667c2.325-2.711,1.942-7.269-1.085-10.241l-15.657-15.382,1.956-1.511,15.665,15.391c3.363,3.3,3.462,8.564.209,11.075" transform="translate(-146.64 -173.963)" fill="#596291"/>
            </g>
          </g>
        </g>
        <g id="Group_5040" data-name="Group 5040" transform="translate(92.284 114.863)" style="mix-blend-mode: overlay;isolation: isolate">
          <g id="Group_5039" data-name="Group 5039">
            <g id="Group_5038" data-name="Group 5038" clip-path="url(#clip-path-5)">
              <path id="Path_10231" data-name="Path 10231" d="M161.864,190.676a12.674,12.674,0,0,1-9.517,6.8l-2.017-3.518s6.444-1.528,7.9-6.845l.008,0Z" transform="translate(-150.33 -187.111)" fill="#b8bde0"/>
            </g>
          </g>
        </g>
        <g id="Group_5043" data-name="Group 5043" transform="translate(86.714 106.791)" opacity="0.17" style="mix-blend-mode: multiply;isolation: isolate">
          <g id="Group_5042" data-name="Group 5042">
            <g id="Group_5041" data-name="Group 5041" clip-path="url(#clip-path-6)">
              <path id="Path_10232" data-name="Path 10232" d="M150.181,177.564c-5.387,2.636-8.924,1.6-8.924,1.6l2.238-2.87,3.022-2.335,3.633,3.573Z" transform="translate(-141.257 -173.963)" fill="#596291"/>
            </g>
          </g>
        </g>
        <path id="Path_10233" data-name="Path 10233" d="M109.262,116.388a21.433,21.433,0,1,0,24.368,18.026,21.433,21.433,0,0,0-24.368-18.026m5.931,39.646a18.654,18.654,0,1,1,15.689-21.209,18.654,18.654,0,0,1-15.689,21.209" transform="translate(-35.136 -44.848)" fill="#888faa"/>
        <path id="Path_10234" data-name="Path 10234" d="M131.879,138.192a16.571,16.571,0,1,1-18.84-13.937,16.571,16.571,0,0,1,18.84,13.937" transform="translate(-38.195 -47.907)" fill="#eaebf0"/>
        <g id="Group_5046" data-name="Group 5046" transform="translate(68.876 78.35)" opacity="0.52">
          <g id="Group_5045" data-name="Group 5045">
            <g id="Group_5044" data-name="Group 5044" clip-path="url(#clip-path-7)">
              <path id="Path_10235" data-name="Path 10235" d="M113.167,131.443a.843.843,0,0,1-.592-1.537,14.41,14.41,0,0,1,5.893-2.265.844.844,0,0,1,.25,1.669,12.736,12.736,0,0,0-5.209,2,.832.832,0,0,1-.342.132" transform="translate(-112.199 -127.633)" fill="#fff"/>
            </g>
          </g>
        </g>
        <g id="Group_5049" data-name="Group 5049" transform="translate(62.759 82.611)" opacity="0.52">
          <g id="Group_5048" data-name="Group 5048">
            <g id="Group_5047" data-name="Group 5047" clip-path="url(#clip-path-8)">
              <path id="Path_10236" data-name="Path 10236" d="M103.354,147.56a.843.843,0,0,1-.959-.71,14.52,14.52,0,0,1,3.688-12,.843.843,0,1,1,1.241,1.142,12.833,12.833,0,0,0-3.261,10.611.844.844,0,0,1-.71.959" transform="translate(-102.234 -134.574)" fill="#fff"/>
            </g>
          </g>
        </g>
        <g id="Group_5052" data-name="Group 5052" transform="translate(32.848 60.676)" opacity="0.14">
          <g id="Group_5051" data-name="Group 5051">
            <g id="Group_5050" data-name="Group 5050" clip-path="url(#clip-path-9)">
              <path id="Path_10237" data-name="Path 10237" d="M63.687,136.882a.937.937,0,0,1-.218-.716l.187-1.945a10.977,10.977,0,0,1,1.724-5.191,12.463,12.463,0,0,1,3.266-3.367q1.837-1.253,4.771-2.855a25.6,25.6,0,0,0,4.824-2.972,4.85,4.85,0,0,0,1.751-3.349,4.07,4.07,0,0,0-1.371-3.607A7.511,7.511,0,0,0,74.2,111.19a7.817,7.817,0,0,0-5.038.948,4.479,4.479,0,0,0-2.177,3.512l-.085.891q-.086.893-1.058.8l-11.46-1.589a.883.883,0,0,1-.657-.39,1.077,1.077,0,0,1-.214-.757l.024-.243A16.1,16.1,0,0,1,57,105.575a17.3,17.3,0,0,1,7.839-5.423A24.907,24.907,0,0,1,75.7,98.985,24.69,24.69,0,0,1,85.838,102a15.728,15.728,0,0,1,6.259,6.365,15.445,15.445,0,0,1,1.571,8.778,13.637,13.637,0,0,1-1.89,6.075,14.013,14.013,0,0,1-3.52,3.874,40.861,40.861,0,0,1-4.993,3.038,25.97,25.97,0,0,0-4.528,2.878,4.78,4.78,0,0,0-1.707,3.312l-.109,1.134a.973.973,0,0,1-1.066.879l-11.506-1.1a.937.937,0,0,1-.661-.35m-.674,17.272a7.044,7.044,0,0,1,6.144-11.637,6.964,6.964,0,0,1,4.878,2.43,6.857,6.857,0,0,1,1.5,5.295,6.928,6.928,0,0,1-2.467,4.834,7.329,7.329,0,0,1-10.052-.923" transform="translate(-53.51 -98.842)" fill="#888faa"/>
            </g>
          </g>
        </g>
        <path id="Path_10238" data-name="Path 10238" d="M169.147,131.479a.063.063,0,0,1-.063-.063,4.834,4.834,0,0,0-4.828-4.829.063.063,0,0,1,0-.126,4.833,4.833,0,0,0,4.828-4.828.063.063,0,0,1,.126,0,4.834,4.834,0,0,0,4.829,4.828.063.063,0,0,1,0,.126,4.834,4.834,0,0,0-4.829,4.829.063.063,0,0,1-.063.063m-4.1-4.955a4.967,4.967,0,0,1,4.1,4.1,4.968,4.968,0,0,1,4.1-4.1,4.967,4.967,0,0,1-4.1-4.1,4.966,4.966,0,0,1-4.1,4.1" transform="translate(-63.399 -46.941)" fill="#767d9b"/>
        <path id="Path_10239" data-name="Path 10239" d="M172.224,118.347a.063.063,0,0,1-.063-.063v-1.214a.063.063,0,1,1,.126,0v1.214a.063.063,0,0,1-.063.063" transform="translate(-66.476 -45.179)" fill="#767d9b"/>
        <path id="Path_10240" data-name="Path 10240" d="M159.787,129.668a.064.064,0,0,1-.063-.063.063.063,0,0,1,.063-.063l1.215-.008h0a.063.063,0,0,1,.063.063.064.064,0,0,1-.063.064l-1.214.007Z" transform="translate(-61.674 -50.017)" fill="#767d9b"/>
        <path id="Path_10241" data-name="Path 10241" d="M172.4,141.268a.063.063,0,0,1-.063-.062l-.015-1.215a.063.063,0,0,1,.062-.064h0a.063.063,0,0,1,.063.063l.015,1.214a.064.064,0,0,1-.063.064Z" transform="translate(-66.537 -54.03)" fill="#767d9b"/>
        <path id="Path_10242" data-name="Path 10242" d="M182.707,129.361a.063.063,0,0,1,0-.126l1.214-.023a.063.063,0,1,1,0,.126l-1.214.023Z" transform="translate(-70.524 -49.892)" fill="#767d9b"/>
        <path id="Path_10243" data-name="Path 10243" d="M14.426,53.605a.1.1,0,0,1-.1-.1,7.4,7.4,0,0,0-7.392-7.393.1.1,0,1,1,0-.193,7.4,7.4,0,0,0,7.392-7.392.1.1,0,1,1,.193,0,7.4,7.4,0,0,0,7.393,7.392.1.1,0,0,1,0,.193,7.4,7.4,0,0,0-7.393,7.393.1.1,0,0,1-.1.1M8.148,46.019A7.6,7.6,0,0,1,14.426,52.3a7.6,7.6,0,0,1,6.279-6.279,7.6,7.6,0,0,1-6.279-6.278,7.6,7.6,0,0,1-6.278,6.278" transform="translate(-2.642 -14.841)" fill="#767d9b"/>
        <path id="Path_10244" data-name="Path 10244" d="M19.137,33.5a.1.1,0,0,1-.1-.1V31.544a.1.1,0,0,1,.193,0V33.4a.1.1,0,0,1-.1.1" transform="translate(-7.352 -12.143)" fill="#767d9b"/>
        <path id="Path_10245" data-name="Path 10245" d="M.1,50.833a.1.1,0,0,1,0-.193l1.859-.012h0a.1.1,0,0,1,0,.193L.1,50.833Z" transform="translate(0 -19.549)" fill="#767d9b"/>
        <path id="Path_10246" data-name="Path 10246" d="M19.4,68.592a.1.1,0,0,1-.1-.1l-.023-1.859a.1.1,0,0,1,.1-.1h0a.1.1,0,0,1,.1.1l.023,1.859a.1.1,0,0,1-.1.1Z" transform="translate(-7.444 -25.693)" fill="#767d9b"/>
        <path id="Path_10247" data-name="Path 10247" d="M35.186,50.362a.1.1,0,0,1,0-.193l1.859-.035a.1.1,0,1,1,0,.193l-1.859.034Z" transform="translate(-13.549 -19.358)" fill="#767d9b"/>
        <path id="Path_10248" data-name="Path 10248" d="M29.5,22.156a.1.1,0,0,1-.1-.1,7.4,7.4,0,0,0-7.392-7.393.1.1,0,1,1,0-.193,7.4,7.4,0,0,0,7.392-7.392.1.1,0,1,1,.193,0,7.4,7.4,0,0,0,7.393,7.392.1.1,0,1,1,0,.193A7.4,7.4,0,0,0,29.6,22.06a.1.1,0,0,1-.1.1M23.225,14.57A7.6,7.6,0,0,1,29.5,20.849a7.6,7.6,0,0,1,6.279-6.279A7.6,7.6,0,0,1,29.5,8.292a7.6,7.6,0,0,1-6.278,6.278" transform="translate(-8.464 -2.697)" fill="#767d9b"/>
        <path id="Path_10249" data-name="Path 10249" d="M34.214,2.052a.1.1,0,0,1-.1-.1V.1a.1.1,0,0,1,.193,0V1.956a.1.1,0,0,1-.1.1" transform="translate(-13.174)" fill="#767d9b"/>
        <path id="Path_10250" data-name="Path 10250" d="M15.173,19.384a.1.1,0,0,1,0-.193l1.859-.012h0a.1.1,0,0,1,0,.193l-1.859.012Z" transform="translate(-5.822 -7.406)" fill="#767d9b"/>
        <path id="Path_10251" data-name="Path 10251" d="M34.477,37.143a.1.1,0,0,1-.1-.1l-.023-1.859a.1.1,0,0,1,.1-.1h0a.1.1,0,0,1,.1.1l.023,1.859a.1.1,0,0,1-.1.1Z" transform="translate(-13.266 -13.55)" fill="#767d9b"/>
        <path id="Path_10252" data-name="Path 10252" d="M50.263,18.913a.1.1,0,0,1,0-.193l1.859-.035a.1.1,0,0,1,0,.193l-1.859.034Z" transform="translate(-19.371 -7.215)" fill="#767d9b"/>
        <path id="Path_10253" data-name="Path 10253" d="M187.65,122.59a.034.034,0,0,1-.034-.034,2.567,2.567,0,0,0-2.565-2.565.034.034,0,0,1,0-.068,2.567,2.567,0,0,0,2.565-2.565.034.034,0,0,1,.034-.033.033.033,0,0,1,.033.033,2.568,2.568,0,0,0,2.565,2.565.034.034,0,1,1,0,.068,2.568,2.568,0,0,0-2.565,2.565.033.033,0,0,1-.033.034m-2.179-2.632a2.64,2.64,0,0,1,2.179,2.179,2.64,2.64,0,0,1,2.179-2.179,2.64,2.64,0,0,1-2.179-2.179,2.64,2.64,0,0,1-2.179,2.179" transform="translate(-71.44 -45.303)" fill="#767d9b"/>
        <path id="Path_10254" data-name="Path 10254" d="M189.285,115.613a.034.034,0,0,1-.034-.034v-.645a.034.034,0,0,1,.068,0v.645a.034.034,0,0,1-.034.034" transform="translate(-73.075 -44.366)" fill="#767d9b"/>
        <path id="Path_10255" data-name="Path 10255" d="M182.678,121.627a.033.033,0,1,1,0-.067l.645,0a.034.034,0,0,1,.034.034.033.033,0,0,1-.034.033Z" transform="translate(-70.524 -46.936)" fill="#767d9b"/>
        <path id="Path_10256" data-name="Path 10256" d="M189.376,127.79a.034.034,0,0,1-.034-.033l-.008-.645a.034.034,0,0,1,.033-.034h0a.034.034,0,0,1,.034.033l.008.645a.034.034,0,0,1-.033.034Z" transform="translate(-73.107 -49.068)" fill="#767d9b"/>
        <path id="Path_10257" data-name="Path 10257" d="M194.854,121.464a.033.033,0,0,1,0-.067l.645-.012a.034.034,0,0,1,.034.033.033.033,0,0,1-.033.034l-.645.012Z" transform="translate(-75.226 -46.87)" fill="#767d9b"/>
      </g>
    </g>
  </g>
</svg>
