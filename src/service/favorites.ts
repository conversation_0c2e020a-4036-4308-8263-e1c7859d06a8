import { http } from "./methods";
import { BookmarkItem, GetAllFavoritesResponse } from "./types/types";

export async function getAllFavorites(): Promise<BookmarkItem[]> {
  const response = await http.get<GetAllFavoritesResponse>("/projects/bookmark");
  return response.data.bookmark;
}

export async function addFavorite(projectId: string) {
  const response = await http.post("/projects/bookmark", { projectId });
  return response.data;
}

export async function removeFavorite(projectId: string) {
  const response = await http.delete(`/projects/bookmark/${projectId}`);
  return response.data;
}
