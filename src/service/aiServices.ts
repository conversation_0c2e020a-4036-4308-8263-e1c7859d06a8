import { http } from "./methods";
import { AIBoxModel, AIProject } from "./types/types";
import { AIBoxDetailsModel } from "./types/types_ai";

export type getAiBoxResponse = {
  data: AIProject[];
  pagination: {
    total: number;
    limit: number;
    page: number;
    pages: number;
  };
};

export type updateConfigBody = {
  formData: {};
  id: string;
};

type getAiBoxDetailsResponse = {
  aiProject: AIBoxModel;
};

export const getAiBoxes = async (
  searchQuery?: string
): Promise<getAiBoxResponse> => {
  try {
    const queryParam = searchQuery
      ? `?name=${encodeURIComponent(searchQuery)}`
      : "";
    const res = await http.get<getAiBoxResponse>(`/ai-projects${queryParam}`);
    return res.data;
  } catch (error: any) {
    throw error;
  }
};

export const getAiDetails = async (id: string): Promise<AIBoxModel> => {
  try {
    const res = await http.get<AIBoxModel>(`/ai-projects/${id}`);
    return res.data;
  } catch (error: any) {
    throw error;
  }
};

export const installAI = async (id: string): Promise<any> => {
  try {
    const res = await http.post<any>(`/ai-box`, { id: id });
    return res.data;
  } catch (error: any) {
    throw error;
  }
};

export const updateConfig = async (data: updateConfigBody): Promise<any> => {
  try {
    const res = await http.put<any>(`/ai-box/${data.id}`, data.formData);
    // console.log("res", res);
    return res.data;
  } catch (error: any) {
    throw error;
  }
};
