import { http } from "./methods"
import { PlatformConnectionResponse } from "./types/types"


export async function getPlatformConnection(): Promise<PlatformConnectionResponse> {
  try {
    const response = await http.get<PlatformConnectionResponse>("/datasets/platform-connection")
    return response.data
  } catch (error: any) {
    console.error("Error fetching platform connection:", error?.message || "Unknown error")
    throw error
  }
}
export type { PlatformConnectionResponse }

