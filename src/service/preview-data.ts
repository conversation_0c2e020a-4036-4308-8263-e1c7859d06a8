import { AxiosError } from "axios";
import { http } from "./methods";

export interface PreviewDataResponse {
  data: {
    data: Array<Record<string, any>>;
  };
  status: number;
}

const previewData = async (path: string): Promise<PreviewDataResponse> => {
  try {
    const response = await http.get(`/files/preview/${path}`);
    // console.log("response", response.data);

    return {
      data: response.data as { data: Array<Record<string, any>> },
      status: response.status,
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    throw {
      data: axiosError.response?.data,
      status: axiosError.response?.status || 500,
      message: axiosError.message,
    };
  }
};

export default previewData;
