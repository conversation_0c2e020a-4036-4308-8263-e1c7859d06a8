import { ApiResponse, http } from "./methods";

export const DatasageService = {
  getDatasageData: async (): Promise<ApiResponse> => {
    const res = await http.get<ApiResponse>(`/datasage/unstructured-data-stats`);
    return res;
  },
  getDatasageConnections: async (): Promise<ApiResponse> => {
    const res = await http.get<ApiResponse>(`/datasage/databases?status=all`);
    return res;
  },
};
