import {
  Collaborator,
  GetDataAssetsResponse,
  Project,
  User,
} from "./types/types";
import { http, ApiResponse } from "./methods";
import { FormDataProject } from "@/app/(root)/projects/add/page";
import { Dataset } from "@/types/dataset";

export type ProjectQuery = {
  page?: number;
  limit?: number;
  query?: string;
};
export const getProjects = async (
  prop?: ProjectQuery
): Promise<{
  filter: any;
  projects: Project[];
}> => {
  try {
    const { page, limit, query } = prop || {};
    const params = new URLSearchParams();

    if (query) params.append("search", query);
    if (page !== undefined) params.append("page", String(page));
    if (limit !== undefined) params.append("limit", String(limit));

    const url = `/projects${params.toString() ? `?${params.toString()}` : ""}`;
    const response: any = await http.get<{ projects: Project[] }>(url);
    return {
      filter: {}, // Add appropriate filter data here
      projects: response.data,
    };
  } catch (error: any) {
    console.error(
      "Error fetching projects:",
      error?.message || "Unknown error"
    );
    throw error;
  }
};

export const getProjectDetails = async (id: string): Promise<ApiResponse> => {
  try {
    const res = await http.get<ApiResponse>(`/projects/${id}`);
    return res;
  } catch (error: any) {
    throw error;
  }
};

export const getProjectURL = async (id: string): Promise<ApiResponse> => {
  try {
    const res = await http.get<ApiResponse>(`/projects/deployed/${id}`);
    return res;
  } catch (error: any) {
    throw error;
  }
};

export const createProject = async (
  projectData: FormDataProject
): Promise<ApiResponse> => {
  // console.log(projectData);
  try {
    const res = await http.post<ApiResponse>("/projects", projectData);
    // console.log(res);
    return res;
  } catch (error: any) {
    throw error;
  }
};

export const updateProject = async (
  projectData: any,
  projectId: string
): Promise<ApiResponse> => {
  // console.log(projectData);
  try {
    const res = await http.put<ApiResponse>(
      `/projects/${projectId}`,
      projectData
    );
    // console.log(res);
    return res;
  } catch (error: any) {
    throw error;
  }
};

export const deleteProject = async (
  projectId: string
): Promise<ApiResponse> => {
  try {
    const res = await http.delete<ApiResponse>(`/projects/${projectId}`);
    // console.log(res);
    return res;
  } catch (error: any) {
    throw error;
  }
};
type getNonCollabratorsResponse = {
  nonCollaborators: User[];
};

export const getNonCollabrators = async (
  id: string
): Promise<getNonCollabratorsResponse> => {
  try {
    const res = await http.get<getNonCollabratorsResponse>(
      `/projects/${id}/non-collaborators`
    );
    return res.data;
  } catch (error: any) {
    throw error;
  }
};

type newCollaborator = {
  userId: string;
  userRole: string;
};

type userCollab = {
  projectId: string;
  collaborators: newCollaborator[];
};

type getCollabratorsResponse = {
  collaborators: Collaborator[];
};

export const getCollabrators = async (
  id: string
): Promise<getCollabratorsResponse> => {
  try {
    const res = await http.get<getCollabratorsResponse>(
      `/projects/collaborators?projectId=${id}`
    );
    return res.data;
  } catch (error: any) {
    throw error;
  }
};

export const AddCollabrators = async (
  user: userCollab
): Promise<ApiResponse> => {
  try {
    const res = await http.post<ApiResponse>(`/projects/collaborators`, user);
    return res;
  } catch (error: any) {
    throw error;
  }
};

export const removeCollabrator = async (
  projectId: string,
  userId: string
): Promise<ApiResponse> => {
  try {
    const res = await http.delete<ApiResponse>(
      `/projects/${projectId}/collaborators/${userId}`
    );
    return res;
  } catch (error: any) {
    throw error;
  }
};

export const getProjectIdeUrl = async (id: string): Promise<ApiResponse> => {
  try {
    const res = await http.get<ApiResponse>(`/projects/${id}/ide-url`);
    return res;
  } catch (error: any) {
    throw error;
  }
};

export const getDataSageUrl = async (): Promise<ApiResponse> => {
  try {
    const res = await http.post<ApiResponse>(`/users/proxy-datasage-login`);
    return res;
  } catch (error: any) {
    throw error;
  }
};

export const createProjectComment = async (
  projectId: string,
  comment: string
): Promise<ApiResponse> => {
  try {
    const res = await http.post<ApiResponse>(`/projects/comment`, {
      projectId,
      comment,
    });
    return res;
  } catch (error: any) {
    console.error("Error creating comment:", error?.message || "Unknown error");
    throw error;
  }
};

export const deleteProjectComment = async (
  commentId: string
): Promise<ApiResponse> => {
  try {
    const res = await http.delete<ApiResponse>(
      `/projects/${commentId}/comments`
    );
    return res;
  } catch (error: any) {
    console.error("Error deleting comment:", error?.message || "Unknown error");
    throw error;
  }
};

export const getProjectComment = async (id: string): Promise<ApiResponse> => {
  try {
    // console.log(id);
    const res = await http.get<ApiResponse>(
      `/projects/${id}/comments?page=1&limit=100`
    );
    return res;
  } catch (error: any) {
    throw error;
  }
};

export const addDataAssets = async (props: {
  projectId: string;
  datasetId: string;
}): Promise<ApiResponse> => {
  try {
    const res = await http.post<ApiResponse>(`/projects/add/data`, props);
    return res;
  } catch (error: any) {
    throw error;
  }
};

export const getDataAssets = async (query: {
  projectId: string;
  page?: number;
  limit?: number;
  search?: string;
}): Promise<GetDataAssetsResponse> => {
  const { page = 1, limit = 10, search = "", projectId = "" } = query;
  const params = new URLSearchParams();
  if (projectId) params.append("projectId", projectId);
  if (search) params.append("search", search);
  if (page) params.append("page", String(page));
  if (limit) params.append("limit", String(limit));
  try {
    //const url = `projects/${projectId}/data-assets?${params.toString()}`;
    const url = `projects/get/data?${params.toString()}`;

    const response = await http.get<GetDataAssetsResponse>(url);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getProjectReadMeText = async (
  id: string
): Promise<ApiResponse> => {
  try {
    const res = await http.get<ApiResponse>(
      `/projects/collaborator-notes?projectId=${id}`
    );
    return res;
  } catch (error: any) {
    throw error;
  }
};

export const updateProjectReadMeText = async (
  projectData: any
): Promise<ApiResponse> => {
  // console.log(projectData);
  try {
    const res = await http.put<ApiResponse>(
      `/projects/collaborator-notes`,
      projectData
    );
    // console.log(res);
    return res;
  } catch (error: any) {
    throw error;
  }
};

export const getProjectUrl = async (id: string): Promise<ApiResponse> => {
  try {
    const res = await http.get<ApiResponse>(`/projects/url/${id}`);
    return res;
  } catch (error: any) {
    throw error;
  }
};
