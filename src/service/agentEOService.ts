import { ApiResponse, http } from "./methods";

interface Entity {
  entity: string;
  entity_description: string;
  matched_entity_attributes: any[]; // Replace `any` with a more specific type if known
}

interface Item {
  _id: string;
  unique_id: string;
  status: string;
  name: string;
  description: string;
  score: number;
  reason: string;
  entities: Entity[];
  completion_score: number;
  employee_size: number;
  location: string;
  domain: string;
  turnover_in_millions: number;
  company_segment: string;
  company_description: string;
  appUrl?: string;
  app_url?: string;
}

interface Opportunity {
  items: Item[];
  page: number;
  page_size: number;
  total_items: number;
  total_pages: number;
}

export interface oppResponse {
  statusCode: number;
  success: boolean;
  data: Opportunity;
}

export const getOpportunies = async (
  page: number = 1,
  page_size: number = 25,
  search: string = "",
  sortBy: string = "",
  sortOrder: string = "",
  preference?: string 
): Promise<oppResponse> => {
  try {
    let url = `agent-po/explore-opportunities?page=${page}&page_size=${page_size}`;
    if (search && search.trim() !== "") {
      url += `&search=${encodeURIComponent(search)}`;
    }
    if (sortBy && sortOrder) {
      url += `&sort_by=${sortBy}&sort_order=${sortOrder}`;
    }
    if (preference && preference !== "All") {
      url += `&preferences=${encodeURIComponent(preference)}`;
    }
    const response = await http.get<oppResponse>(url);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const refreshOpportunies = async (
  page: number = 1,
  page_size: number = 10
): Promise<oppResponse> => {
  try {
    const url = `agent-po/explore-opportunities?page=${page}&page_size=${page_size}`;
    const response = await http.post<oppResponse>(url, {
      timeout: 0,
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// API: GET /projects/recent-projects?page=1&limit=10
export interface RecentProject {
  _id: string;
  name: string;
  description?: string;
  app_url: string;
  icon_url?: string;
  // Add more fields as needed from the API response
}

export interface RecentProjectsResponse {
  statusCode: number;
  success: boolean;
  data: {
    projects: RecentProject[];
    page?: number;
    page_size?: number;
    total_items?: number;
    total_pages?: number;
  };
}

export const getRecentProjects = async (
  page: number = 1,
  limit: number = 10
): Promise<RecentProjectsResponse> => {
  try {
    const url = `/projects/recent-projects?page=${page}&limit=${limit}`;
    const response = await http.get<RecentProjectsResponse>(url, { timeout: 0 });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const submitOpportunityAction = async ({
  mongo_id,
  add_to_project,
}: {
  mongo_id: string;
  add_to_project: string;
}) => {
  // Use your http.post utility if available, otherwise use axios directly
  return await http.post("agent-po/explore-opportunities/usecases/actions", {
    mongo_id,
    view: 0,
    add_to_project,
    liked: true,
  });
};
