import { useState, useEffect } from "react";
import { io, type Socket } from "socket.io-client";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";

export const useAIInstall = () => {
  const token = useSelector((state: RootState) => state.user.token);
  const [latestMessage, setLatestMessage] = useState<Record<string, any>>({
    message: "Initializing...",
  });
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    let socket: Socket | null = null;

    if (!isComplete) {
      // console.log("Initializing WebSocket...");

      socket = io(
        process.env.NEXT_PUBLIC_SOCKET_BASE_URL || "http://18.234.247.164:5004",
        {
          auth: { token },
          transports: ["websocket"],
        }
      );

      socket.on("connect", () => console.log("✅ WebSocket connected"));

      socket.on("sendMessage", (data) => {
        // console.log("📩 Message received:", data);
        setLatestMessage(data);

        if (data.message === "Deployment completed successfully.") {
          // console.log("✅ Deployment complete. Closing socket...");
          setIsComplete(true);
          socket?.disconnect();
        }
      });

      socket.on("error", (error) => {
        setLatestMessage({ text: "Error: " + error });
        socket?.disconnect();
        setIsComplete(true);
      });

      socket.on("disconnect", (reason) => console.log(""));
    }

    return () => {
      // console.log("🧹 Cleaning up WebSocket...");
      socket?.disconnect();
    };
  }, [token, isComplete]);

  return latestMessage;
};
