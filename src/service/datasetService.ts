import { ApiResponse, http } from "./methods";

export type ExploreDataQuery = {
  page?: number;
  limit?: number;
  search?: string;
  type?: "tag" | "license"; // Ensure only valid types are allowed
};

export const datasetService = {
  getDepartmentList: async (prop: ExploreDataQuery): Promise<ApiResponse> => {
    try {
      const { page = 1, limit = 10, search = "" } = prop;
      const params = new URLSearchParams();

      if (search) params.append("search", search);
      params.append("page", String(page));
      params.append("limit", String(limit));

      const url = `/departments?${params.toString()}`;
      const response = await http.get<ApiResponse>(url);

      return response;
    } catch (error) {
      console.error("Error fetching department list:", error);
      throw error; // Rethrow to handle errors at the caller level
    }
  },

  getTagList: async (prop: ExploreDataQuery): Promise<ApiResponse> => {
    try {
      const { page = 1, limit = 10, search = "", type = "tag" } = prop;
      const params = new URLSearchParams();

      if (search) params.append("search", search);
      params.append("page", String(page));
      params.append("limit", String(limit));
      params.append("type", type); // Ensures "tag" or "license" is always included

      const url = `/tags-and-licenses?${params.toString()}`;
      const response = await http.get<ApiResponse>(url);

      return response;
    } catch (error) {
      console.error("Error fetching tag list:", error);
      throw error;
    }
  },
  createDataset: async (data: any) => {
    try {
      const url = `/datasets`;
      const response = await http.post<ApiResponse>(url, data);
      return response;
    } catch (error) {
      console.error("Error creating dataset:", error);
      throw error;
    }
  },
  updateDataset: async (id: string, data: any) => {
    try {
      const url = `/datasets/${id}`;
      const response = await http.put<ApiResponse>(url, data);
      return response;
    } catch (error) {
      console.error("Error updating dataset:", error);
      throw error;
    }
  },
  checkDatasetTitle: async (title: string) => {
    try {
      const url = `/datasets/check-title?search=${title}`;
      const response = await http.get<ApiResponse>(url);
      return response;
    } catch (error) {
      console.error("Error checking dataset title:", error);
      throw error;
    }
  },
  getFileTypesStatistics: async (): Promise<ApiResponse> => {
    try {
      const url = `/datasets/file-types/statistics`;
      const response = await http.get<ApiResponse>(url);
      return response;
    } catch (error) {
      console.error("Error fetching dataset statistics:", error);
      throw error;
    }
  }
};
