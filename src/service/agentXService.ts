import { Dataset } from "@/types/dataset";
import { ApiResponse, http } from "./methods";

export type MatchedEntityAttribute = {
  type: string;
  attribute: string;
  entity_attribute_description: string;
  reason: string;
  confidence_score: number;
  matched_dataset_attribute: any;
  dataset: string;
  isAdded: boolean;
  database: string;
};

export type Entity = {
  entity: string;
  entity_description: string;
  matched_entity_attributes: MatchedEntityAttribute[];
};

export type MatchedDatasetWithEntity = {
  entities: Entity;
  completion_score: number;
};

export type DatasetRecommendationsResponse = {
  message: string;
  data: {
    status: string;
    matched_dataset_with_entity: MatchedDatasetWithEntity;
  };
  status: boolean;
};

export const getDatasetRecommendations = async (props: {
  project_id: string;
  refresh?: boolean;
}): Promise<DatasetRecommendationsResponse> => {
  try {
    const res = await http.post<DatasetRecommendationsResponse>(
      `/agent-x/{project-id}`,
      props,
      { timeout: 0 }
    );
    return res.data;
  } catch (error: any) {
    throw error;
  }
};

export type UseCaseRecommendationsResponse = {
  message: string;
  data: {
    status: string;
    use_cases: UseCases[];
  };
  status: boolean;
};

export type UseCases = {
  Usecase: string;
  Expected_Example_Output?: ExpectedExampleOutput[] | {};
  Semantic_Score: number;
};

export type ExpectedExampleOutput = {
  name: string;
  description: string;
  entity_attributes: EntityAttributes[];
};

export type EntityAttributes = {
  name: string;
  description: string;
  entity_type: string;
};

export const getUseCaseRecommendations = async (
  query: string
): Promise<UseCaseRecommendationsResponse> => {
  try {
    const params = new URLSearchParams();
    // Add parameters **only if they are not empty**
    if (query) params.append("usecase_heading", query);
    // Build the final API URL
    const url = `/agent-x/usecases-recommendation?${params.toString()}`;
    const response = await http.get<UseCaseRecommendationsResponse>(url, {
      timeout: 0,
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const addAgentXToProject = async (body: {
  projectId: string;
  title: string;
  description: string;
  type?: string;
  url?: string;
  source?: string;
  tags?: string[];
}): Promise<ApiResponse> => {
  try {
    const url = `/projects/add/data`;
    const response = await http.post<ApiResponse>(url, body);
    return response;
  } catch (error) {
    throw error;
  }
};
