import { Dataset, Pagination } from "@/types/dataset";

export interface Project {
  _id: string;
  name: string;
  description: string;
  isAiBox: boolean;
  projectMetaData: boolean;
  creator: {
    _id: string;
    name: string;
    email: string;
    isDeleted: boolean;
    department: {
      departmentId: string;
      departmentRole: string;
    }[];
    createdAt: string;
    updatedAt: string;
    thumbnail?: string;
    username?: string;
    apiKey?: string;
    isSysAdmin?: boolean;
    about?: string;
  };
  status: string;
  experimentalProject?: boolean;
  aiAvatar?: boolean;
  sensitive?: boolean;
  isDeleted: boolean;
  isDeployed: boolean;
  appType?: string;
  orgArea: string[];
  tags: string[];
  department: string[];
  createdOn: string;
  updatedOn: string;
  createdAt: string;
  updatedAt: string;
  codeCommitUrl?: string;
  appUrl?: string;
  expectedReturn?: string;
  install?: {
    instanceId: string;
    clientName: string;
    instanceName: string;
    isCreateIntanceSetup: boolean;
    isCloneRepositorySetup: boolean;
    isDeploySetup: boolean;
    isDomainSetup: boolean;
    _id: string;
    readme: string;
  };
  collaborators: {
    _id: string;
    projectId: string;
    userId: {
      _id: string;
      name: string;
      email: string;
      isDeleted: boolean;
      department: {
        departmentId: string;
        departmentRole: string;
      }[];
      createdAt: string;
      updatedAt: string;
      thumbnail?: string;
      username?: string;
      apiKey?: string;
      isSysAdmin?: boolean;
      about?: string;
    };
    userRole: string;
    assigneeUserId: string;
    updatedOn: string;
  }[];
  currentUserRole?: CurrentUser;
}

export interface ConfigurationSection {
  [key: string]: {
    type: string;
    required: boolean;
    value?: string;
    description: string;
    allowed_values?: string[]; // Only for specific sections like "LOG_LEVEL"
    items?: string; // Only for array-like fields such as "ALLOWED_EXTENSIONS"
  };
}

export interface ProjectDetails {
  _id: string;
  name: string;
  description: string;
  isAiBox: boolean;
  projectMetaData: boolean;
  creator: {
    _id: string;
    name: string;
    email: string;
    isDeleted: boolean;
    department: {
      departmentId: string;
      departmentRole: string;
    }[];
    createdAt: string;
    updatedAt: string;
    thumbnail?: string;
    username?: string;
    apiKey?: string;
    isSysAdmin?: boolean;
    about?: string;
  };
  status: string;
  experimentalProject?: boolean;
  aiAvatar?: boolean;
  sensitive?: boolean;
  isDeleted: boolean;
  isDeployed: boolean;
  appType?: string;
  orgArea: string[];
  tags: string[];
  department: string[];
  createdOn: string;
  updatedOn: string;
  createdAt: string;
  updatedAt: string;
  codeCommitUrl?: string;
  appUrl?: string;
  expectedReturn?: string;
  install?: {
    instanceId: string;
    clientName: string;
    instanceName: string;
    isCreateIntanceSetup: boolean;
    isCloneRepositorySetup: boolean;
    isDeploySetup: boolean;
    isDomainSetup: boolean;
    _id: string;
    readme: string;
    configuration: {
      [sectionName: string]: ConfigurationSection; // Dynamic sections
    };
  };
  collaborators: {
    _id: string;
    projectId: string;
    userId: {
      _id: string;
      name: string;
      email: string;
      isDeleted: boolean;
      department: {
        departmentId: string;
        departmentRole: string;
      }[];
      createdAt: string;
      updatedAt: string;
      thumbnail?: string;
      username?: string;
      apiKey?: string;
      isSysAdmin?: boolean;
      about?: string;
    };
    userRole: string;
    assigneeUserId: string;
    updatedOn: string;
  }[];
  currentUserRole?: CurrentUser;
}
export interface Collaborator {
  _id: string;
  projectId: string;
  userId: User;
  userRole: string;
  assigneeUserId: string;
  updatedOn: string;
  __v: number;
  user: User;
}

export interface User {
  user: any;
  _id: string;
  name: string;
  email: string;
  isDeleted: boolean;
  department: UserDepartment[];
  createdAt: string;
  thumbnail: string;
  updatedAt: string;
  __v: number;
  isSysAdmin?: boolean;
  password?: string;
}

export interface UserDepartment {
  departmentId: string;
  departmentRole: string;
}

export interface Tunnel {
  tunnel_name: string;
  tunnel_status: string;
  last_status_change: string;
}

export interface VpnConnection {
  state: string;
  tunnels: Tunnel[];
}

export interface AwsData {
  success: boolean;
  status: number;
  vpnConnections: VpnConnection[];
}

export interface CompanyData {
  DB: string;
  HOST: string;
  PORT: number;
  Instance: string;
  DBMS: string;
  Sync: boolean;
  company?: string;
  system?: string;
  Category?: string;
}

export interface PlatformConnectionResponse {
  awsData: AwsData;
  companyData: CompanyData[];
}

export interface CurrentUser {
  _id: string;
  projectId: string;
  userId: string;
  userRole: string;
  assigneeUserId: string;
  updatedOn: string;
  __v: number;
}

export type GetDataAssetsResponse = {
  assets: DataAssetsResponse[];
  totalPages: number;
  total: number;
  page: number;
  limit: number;
};

export type DataAssetsResponse = {
  _id: string;
  projectId: string;
  title: string;
  description: string;
  isDataset: boolean;
  datasetId: string;
  type: string;
  source: string;
  tags: string[];
  userId: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
};

export interface NotificationItem {
  icon: string;
  id: string;
  textParts: (string | { desc: string; link: string })[];
  timestamp: string;
  unread: boolean;
}

export interface NotificationGroup {
  date: string;
  items: NotificationItem[];
}

export interface NotificationsResponse {
  notifications: NotificationGroup[];
}
export interface GetAllFavoritesResponse {
  bookmark: BookmarkItem[];
}

export interface BookmarkItem {
  _id: string;
  userId: string;
  projectId: ProjectData;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface ProjectData {
  _id: string;
  name: string;
  description?: string;
  appType?: string;
  performance?: string;
  isAiBox: boolean;
  appUrl: string;
}

export interface FavoriteItem {
  id: string;
  name: string;
  href: string;
  isAiBox?: boolean;
  appUrl?: string;
}

export interface FavoriteToggleProject {
  id: string;
  name: string;
  appType: string;
  performance: string;
  isFav: boolean;
  appUrl: string;
  isAiBox: boolean;
}

export interface FavoritesState {
  items: FavoriteItem[];
  status: "idle" | "loading" | "succeeded" | "failed";
  error: string | null;
}

export interface Notification {
  _id: string;
  title: string;
  message: string;
  type: string;
  relatedId?: string;
  relatedType?: string;
  relatedName?: string;
  metadata?: Record<string, any>;
  isRead: boolean;
  createdAt: string;
  readAt?: string;
}

export interface NotificationResponse {
  notifications: Notification[];
  pagination: Pagination;
}
export interface NotificationCountResponse {
  count: number;
}

interface InstanceDetails {
  endpoint: string;
  healthCheck: string;
}

interface Tag {
  _id: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface Configuration {
  inputSize: number;
  confidenceThreshold: number;
}

interface Issue {
  _id: string;
  title: string;
  description: string;
  status: string;
}

interface ChangeLog {
  _id: string;
  timestamp: string;
  message: string;
  level: string;
}

export interface AIProject {
  instanceDetails: InstanceDetails;
  _id: string;
  name: string;
  version: string;
  description: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  repository: string;
  thumbnailUrl: string;
  tags: Tag[];
  dependencies: string[];
  configuration: Configuration;
  readme: string;
  issues: Issue[];
  changeLogs: ChangeLog[];
}

export interface AIBoxModel {
  aiProject: AIProject;
  install: string | Record<string, any>;
}
