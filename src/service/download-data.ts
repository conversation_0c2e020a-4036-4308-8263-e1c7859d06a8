import { AxiosError } from "axios";
import { ApiResponse, http } from "./methods";

const downloadData = async (path: string): Promise<ApiResponse<Blob>> => {
    try {
        const response = await http.get<Blob>(`/files/download/${path}`);
        return response;
    } catch (error) {
        const axiosError = error as AxiosError;
        throw {
            data: axiosError.response?.data,
            status: axiosError.response?.status || 500,
            message: axiosError.message,
        };
    }
};

export default downloadData;