import { http } from "./methods";

export interface WhatsNewItem {
  _id: string;
  icon: string;
  title: string;
  header: string;
  description: string;
  category: "Feature" | "Notifications" | "Opportunity"; // Use union if categories are fixed
  createdAt: string; // ISO Date string
  updatedAt: string; // ISO Date string
  __v: number;
}

export const getWhatsNew = async (): Promise<[WhatsNewItem]> => {
  try {
    const res = await http.get<[WhatsNewItem]>(`/value-analytics/whatsnew`);
    return res.data;
  } catch (error: any) {
    throw error;
  }
};
