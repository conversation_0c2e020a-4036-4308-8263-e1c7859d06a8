// endppoints for the api
// like --> getUsers, getPosts, getComments, etc

import { http, setToken, clearToken, ApiResponse } from "./methods";

// Auth Service
export const authService = {
  login: async (credentials: LoginCredentials) => {
    const res = await http.post<AuthResponse>("users/signin", credentials);
    setToken(res.data.token);
    return res;
  },

  logout: () => {
    clearToken();
  },

  sendResetLink: async ({ usernameOrEmail }: { usernameOrEmail: string }) => {
    // Use the correct endpoint and payload as per API docs
    return await http.post<ApiResponse>("/users/forgot-password", {
      usernameOrEmail,
    });
  },

  resetPasswordWithToken: async ({
    token,
    newPassword,
    confirmPassword,
  }: {
    token: string;
    newPassword: string;
    confirmPassword: string;
  }) => {
    return await http.post<ApiResponse>("/users/reset-password-with-token", {
      token,
      newPassword,
      confirmPassword,
    });
  },
};

export const userService = {
  getUser: async () => {
    const res = await http.get<ApiResponse>("users");
    localStorage.setItem("user", JSON.stringify(res.data));
    return res;
  },

  updateUserInfo: async (userData: UpdateUserInfo) => {
    return await http.put<ApiResponse>("users/update-info", userData);
  },

  resetPassword: async (data: ResetPasswordData) => {
    return await http.post<ApiResponse>("users/reset-password", data);
  },
};

export type LoginCredentials = {
  email: string;
  password: string;
};

export type AuthResponse = {
  token: string;
};

export const fileService = {
  uploadFile: async (
    file: File,
    mock = false
  ): Promise<ApiResponse<FileResponse>> => {
    if (mock) {
      return {
        data: {
          id: "mock-file-id",
          url: "https://example.com/mock-file.jpg",
          filename: file.name,
          size: file.size,
        },
        status: 200,
      };
    }

    const formData = new FormData();
    formData.append("file", file);

    return http.upload<FileResponse>("/files", formData);
  },

  uploadMultiple: async (
    files: File[],
    mock = false
  ): Promise<ApiResponse<FileResponse[]>> => {
    if (mock) {
      return {
        data: files.map((file) => ({
          id: `mock-${file.name}`,
          url: `https://example.com/mock-${file.name}`,
          filename: file.name,
          size: file.size,
        })),
        status: 200,
      };
    }

    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files-${index}`, file);
    });

    return http.upload<FileResponse[]>("/files/upload-multiple", formData);
  },
};

// Add to existing types
export type FileResponse = {
  id: string;
  url: string;
  filename: string;
  size: number;
  mimeType?: string;
};

// Add these types
export type UpdateUserInfo = {
  name?: string;
  email?: string;
  about?: string;
  thumbnail?: string;
  department?: Array<{
    departmentId: string;
    departmentRole: string;
  }>;
};

// Add this type
export type ResetPasswordData = {
  email?: string;
  oldPassword: string;
  newPassword: string;
};

export const valueAnalyticsService = {
  getValuePillars: async () => {
    return await http.get<ApiResponse>("/value-analytics/value-pillars");
  },

  getValuePillarById: async (pillarId: string) => {
    return await http.get<ApiResponse>(
      `/value-analytics/value-pillar/${pillarId}`
    );
  },
};
