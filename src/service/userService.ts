import { AxiosError } from "axios";
import { ApiResponse, http } from "./methods";

export interface DepartmentData {
  _id: string;
  name: string;
  description: string;
  thumbnailUrl: string;
  createdBy: string;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  __v: number;
}
interface Department {
  departmentId: DepartmentData;
  departmentRole: string;
  _id: string;
}

export interface User {
  _id: string;
  name: string;
  email: string;
  isDeleted: boolean;
  department: Department[];
  username: string;
  about: string;
  isSysAdmin: boolean;
  createdAt?: string;
  updatedAt?: string;
  __v: number;
  thumbnail?: string;
  apiKey?: string;
}

export interface UsersResponse {
  users: User[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
}

const getAllUsers = async (): Promise<UsersResponse> => {
  try {
    const response = await http.get(`/users/get-all-users?page=1&limit=100`);

    return response.data as UsersResponse;
  } catch (error) {
    const axiosError = error as AxiosError;
    throw {
      data: axiosError.response?.data,
      status: axiosError.response?.status || 500,
      message: axiosError.message,
    };
  }
};

export const createUser = async (data: any) => {
  const response = await http.post<any>("/users/signup", data);
  return response;
};

export const updateUser = async (data: any, id: string) => {
  const response = await http.put<any>(`/users/update-user-info/${id}`, data);
  return response;
};

export const activateUser = async (id: string) => {
  const response = await http.put<any>(`/users/set-user-active`, {
    userId: id,
  });
  return response;
};
export const deActivateUser = async (id: string) => {
  const response = await http.put<any>(`/users/set-user-deactive`, {
    userId: id,
  });
  return response;
};

export default getAllUsers;
