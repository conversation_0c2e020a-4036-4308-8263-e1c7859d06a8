import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Check } from "lucide-react";

export default function ProgressBarTabs({
  currentStep,
  setCurrentStep,
  totalSteps,
  isOpen,
}: {
  currentStep: number;
  setCurrentStep: React.Dispatch<React.SetStateAction<number>>;
  totalSteps: number;
  isOpen: boolean;
}) {
  const tabs = new Array(totalSteps + 1).fill("default");
  return (
    <div className="flex gap-3 justify-center items-center w-full max-w-xs">
      {/* Back Button */}
      {isOpen && (
        <div className=" flex justify-center">
          <button
            onClick={() => setCurrentStep((prev) => prev - 1)}
            disabled={currentStep === 0}
          >
            <div className="flex gap-1">
              <ArrowLeft
                className={`w-4 ${
                  currentStep == 0 ? "text-gray-400" : "text-black"
                }`}
              />
              <span
                className={`${
                  currentStep == 0 ? "text-gray-400" : "text-black"
                }`}
              >
                Back
              </span>
            </div>
          </button>
        </div>
      )}

      {/* Progress Bar */}
      <div className="w-full flex items-center justify-between relative">
        {tabs.map((tab, index) => (
          <div key={index} className="flex items-center w-full relative">
            {/* Progress Line */}
            {index >= 0 && (
              <div
                className={`h-1.5 w-full ${
                  currentStep >= index ? "bg-teal-500" : "bg-gray-100"
                } rounded-e-full`}
              />
            )}

            {/* Dot on the Line */}
            <div className="absolute  transform ">
              <div
                className={`w-3 h-3 flex items-center justify-center rounded-full text-white font-bold transition-all duration-300 ${
                  currentStep >= index ? "bg-teal-500" : "bg-gray-300"
                }`}
              ></div>
            </div>
          </div>
        ))}
      </div>

      {/* Next Button */}
      {isOpen ? (
        <div className=" flex justify-center">
          <button
            onClick={() => setCurrentStep((prev) => prev + 1)}
            disabled={currentStep === totalSteps}
            className={`${
              currentStep === totalSteps ? "text-teal-500" : "text-black"
            }`}
          >
            {currentStep === totalSteps ? (
              <div className="flex gap-1">
                <span>Complete</span>
                <Check />
              </div>
            ) : (
              <div className="flex gap-1">
                <ArrowRight className="w-4" />
                <span>Next</span>
              </div>
            )}
          </button>
        </div>
      ) : (
        <div>
          {currentStep === totalSteps ? (
            <div className="flex gap-1 text-teal-500">
              <span>Complete</span>
              <Check />
            </div>
          ) : (
            <div className="flex gap-1">
              <span>In</span>
              <span>Progress</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
