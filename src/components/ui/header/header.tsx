"use client";
import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { useRouter, usePathname } from "next/navigation";
import { MagnifyingGlassIcon, PlusIcon, ChevronDownIcon } from "@heroicons/react/24/solid";
import { useSidebar } from "@/context/sidebar-context";
import { useDispatch, useSelector } from "react-redux";
import { logout } from "@/lib/store/features/user/userSlice";
import AddDataset from "@/components/pages/AddDataset/AddDataset";
import Notification from "@/components/pages/Notification/Notification";
import { resetFavorites } from "@/lib/store/features/favorites/favoritesSlice";

const Header = () => {
  const { toggleSidebar } = useSidebar();
  const pathname = usePathname();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const dialogRef = useRef<HTMLDivElement>(null);
  const [query, setQuery] = useState("");
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [title, setTitle] = useState("");
  const [isWorldDataset, setIsWorldDataset] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const toggleDialog = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDialogOpen((prev) => !prev);
  };

  // useEffect(() => {
  //   setQuery("");
  // }, [pathname]);

  const toggleDropdown = () => {
    setIsDropdownOpen((prev) => !prev);
  };

  const handleLogoClick = () => {
    router.push("/");
  };

  const dispatch = useDispatch();
  const handleLogout = () => {
    localStorage.removeItem("authToken");
    localStorage.removeItem('returnUrl');
    sessionStorage.clear();
    dispatch(resetFavorites());
    dispatch(logout());
    router.push("/login");
  };

  const handleSubmit = (searchQuery: string) => {
    setShowSuggestions(false);
    if (searchQuery.trim()) {
      const storedQueries: string[] = JSON.parse(localStorage.getItem("searchQueries") || "[]");
      if (!storedQueries.includes(searchQuery)) {
        storedQueries.push(searchQuery);
        localStorage.setItem("searchQueries", JSON.stringify(storedQueries));
      }
      router.push(`/search?query=${encodeURIComponent(searchQuery)}`);
    }
    setQuery("");
  };

  const userInfo = useSelector((state: any) => state.user.userInfo);

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setShowSuggestions(false);
    handleSubmit(query);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    handleSubmit(suggestion);
    setShowSuggestions(false);
  };

  useEffect(() => {
    if (query) {
      const storedQueries: string[] = JSON.parse(localStorage.getItem("searchQueries") || "[]");
      const filteredSuggestions = storedQueries
        .filter((item) => item.toLowerCase().includes(query.toLowerCase()))
        .slice(0, 5);
      setSuggestions(filteredSuggestions);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [query]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const buttonElement = document.querySelector("[data-dropdown-button]");
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonElement &&
        !buttonElement.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
      if (
        dialogRef.current &&
        !dialogRef.current.contains(event.target as Node) &&
        buttonElement &&
        !buttonElement.contains(event.target as Node)
      ) {
        setIsDialogOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const imgBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

  return (
    <>
      <header className="flex items-center justify-between bg-[#3B4154] px-4 h-12 sticky top-0 z-10 border-b border-gray-400">
        <div className="flex items-center space-x-5 hover:cursor-pointer">
          <Image src="/assets/menu_icon.png" alt="Menu" width={20} height={20} onClick={toggleSidebar} />
          <img
            src={process.env.NEXT_PUBLIC_USER_LOGO || ""}
            alt="Logo"
            width="80"
            height="44"
            onClick={handleLogoClick}
            style={{ cursor: "pointer" }}
          />
          <div className="border-l border-[#888faa] h-[20px]" />
          <Image src="/assets/sutra_logo_latest.svg" alt="Logo" width={110} height={34} onClick={handleLogoClick} />
        </div>

        <div
          ref={containerRef}
          className="bg-[#5A5E76] h-full items-center px-4 flex flex-col"
          style={{ width: "480px" }}
        >
          <form ref={formRef} onSubmit={handleFormSubmit} className="w-full mt-2 px-3">
            <div className="flex items-center w-full gap-2">
              <MagnifyingGlassIcon className="text-white w-5 h-5" />
              <input
                type="text"
                ref={inputRef}
                placeholder="Search for Data Assets, Projects, and Documents..."
                className="inputBox w-full focus:outline-none h-full py-2 placeholder-white"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
              />
            </div>
          </form>
          {showSuggestions && suggestions.length > 0 && (
            <div className="bg-white border rounded-sm shadow-lg" style={{ width: "480px" }}>
              <ul>
                {suggestions.map((suggestion, index) => (
                  <li
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="flex gap-2 p-2 cursor-pointer hover:bg-gray-200"
                  >
                    <MagnifyingGlassIcon className="text-gray-500 w-5 h-5" />
                    {suggestion}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-4 relative">
          <div className="relative">
            <div
              className="flex items-center space-x-2 text-white px-3 py-2 rounded-lg cursor-pointer"
              onClick={toggleDialog}
              data-dropdown-button
            >
              <PlusIcon className="w-5 h-5" />
              <span className="text-md">Add Data Asset</span>
              <ChevronDownIcon className="w-5 h-5" />
            </div>
            {isDialogOpen && (
              <div
                ref={dialogRef}
                className="absolute left-0 mt-3 top-7 w-40 bg-white shadow-lg rounded-sm py-2 text-black z-10"
              >
                <div
                  className="flex items-center gap-2 px-4 py-2 text-[#3B4154] hover:bg-gray-200 rounded cursor-pointer"
                  onClick={() => {
                    setIsOpen(true);
                    setTitle("Add World Data");
                    setIsWorldDataset(true);
                  }}
                >
                  <PlusIcon className="w-5 h-5 text-[#888FAA]" />
                  <span className="text-sm">World Data</span>
                </div>
                <div
                  className="flex items-center gap-2 px-4 py-2 text-[#3B4154] hover:bg-gray-200 rounded cursor-pointer"
                  onClick={() => {
                    setIsOpen(true);
                    setTitle("Add Company Data");
                  }}
                >
                  <PlusIcon className="w-5 h-5 text-[#888FAA]" />
                  <span className="text-sm">Company Data</span>
                </div>
                <div
                  className="flex items-center gap-2 px-4 py-2 text-[#3B4154] hover:bg-gray-200 rounded cursor-pointer"
                  onClick={() => {
                    setIsOpen(true);
                    setTitle("Add Document Data");
                  }}
                >
                  <PlusIcon className="w-5 h-5 text-[#888FAA]" />
                  <span className="text-sm">Documents</span>
                </div>
              </div>
            )}
          </div>

          <Notification />

          <div className="relative">
            <div className="flex items-center space-x-2 cursor-pointer text-white" onClick={toggleDropdown}>
              <div className="w-8 h-8 rounded-full bg-gray-500 flex items-center justify-center">
                <img
                  src={
                    imgBaseUrl?.replace("/api", "") + userInfo?.user?.thumbnail ||
                    "/person_FILL0_wght300_GRAD0_opsz24 (2).svg"
                  }
                  alt="User Avatar"
                  className="w-8 h-8 rounded-full"
                />
              </div>
              <span className="text-sm">{userInfo?.user?.name}</span>
            </div>
            {isDropdownOpen && (
              <div ref={dropdownRef} className="absolute right-0 mt-2 w-48 bg-white shadow-md rounded-md py-2 text-sm">
                <a href="/profile" className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100">
                  <Image src="/person_FILL0_wght300_GRAD0_opsz24 (2).svg" alt="Profile Icon" width={20} height={20} />
                  View Profile
                </a>
                {/* <a
                  href="/admin/settings"
                  className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100"
                >
                  <Image
                    src="/settings_FILL0_wght300_GRAD0_opsz24.svg"
                    alt="Settings Icon"
                    width={20}
                    height={20}
                  />
                  SysAdmin Setting
                </a> */}
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-2 w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
                >
                  <Image src="/logout_FILL0_wght300_GRAD0_opsz24.svg" alt="Logout Icon" width={20} height={20} />
                  Logout
                </button>
              </div>
            )}
          </div>
        </div>
      </header>

      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity z-50"
          onClick={() => setIsOpen(false)}
        />
      )}
      <AddDataset
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        title={title}
        isWorldDataset={isWorldDataset}
        className="z-50 relative"
      />
    </>
  );
};

export default Header;
