import React from "react";

interface CheckboxProps {
  id: string;
  label: string;
  checked: boolean;
  onChange: (e: any) => void;

}

const Checkbox: React.FC<CheckboxProps> = ({ id, label, checked, onChange }) => {
  return (
    <div className="flex items-center mt-2">
      <input
        type="checkbox"
        id={id}
        checked={checked}
        onChange={onChange}
        className="h-5 w-5 mr-2 appearance-none border-2 border-[#00B2A1] rounded-sm checked:border-[#00B2A1] 
                   checked:bg-white checked:after:content-[''] checked:after:border-b-2 checked:after:border-r-2 
                   checked:after:border-[#00B2A1] checked:after:w-1.5 checked:after:h-3.5 checked:after:block 
                   checked:after:rotate-45 checked:after:translate-x-[5px] checked:after:translate-y-[-1px] 
                   focus:outline-none focus:ring-0 relative"
      />
      <label htmlFor={id} className="ml-1 text-[#3B4154] text-[15px]">
        {label}
      </label>
    </div>
  );
};

export default Checkbox;
