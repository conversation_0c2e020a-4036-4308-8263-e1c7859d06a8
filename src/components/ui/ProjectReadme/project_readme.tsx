// components/Readme.tsx
import { getProjectReadMeText, updateProjectReadMeText } from "@/service/projects";
import React, { useState, useEffect, useRef } from "react";
import { Toast } from "@/components/ui/toast/toast";
import Loader from "@/components/common/loader/loader";

interface ReadmeProps {
  projectId: string;
  currentUserReadme: string | null;
}

const Readme: React.FC<ReadmeProps> = ({ projectId, currentUserReadme }) => {
  const [readmeText, setReadmeText] = useState<string>(currentUserReadme || "");
  const [isReadmeTextLoading, setIsReadneTextLoading] = useState<Boolean>(true);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const saveIconRef = useRef<HTMLSpanElement>(null);
  const [toast, setToast] = useState<{ message: string; type: "success" | "error" } | null>(null);

  useEffect(() => {
    const fetchReadMeText = async () => {
      try {
        const response = await getProjectReadMeText(projectId || "");
        if (response.data.collaborator && response.data.collaborator.length > 0) {
          const notesText = response.data.collaborator[0].notes;
          setReadmeText(notesText);
        }
      } catch (error) {
        console.error("Failed to fetch comments:", error);
      } finally {
        setIsReadneTextLoading(false);
      }
    };
    if (projectId) {
      fetchReadMeText();
    }
  }, [projectId]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setReadmeText(e.target.value);
    if (e.target.value) {
      setIsEditing(true);
    }
  };

  const handleSave = async () => {
    if (!isEditing || isSaving) return;

    setIsSaving(true);
    try {
      const response = await updateProjectReadMeText({ projectId: projectId, notes: readmeText });
      if (response.status) {
        setIsSaving(false);
        setIsEditing(false);
        setToast({ message: response.message || "Updated successfully!", type: "success" });
      } else {
        setToast({ message: response.message || "error", type: "error" });
        setIsSaving(false);
        setIsEditing(false);
      }
    } catch (error: any) {
      // console.log(error);
      setToast({ message: error.data.message || "error", type: "error" });
      setIsSaving(false);
      setIsEditing(false);
    }
  };

  if (isReadmeTextLoading) {
    return (
      <div className="flex justify-center items-center w-[350px] h-[200px] border-1 border-white rounded-lg">
        <div className="w-10 h-10 border-4 border-t-4 border-white rounded-full animate-spin border-t-teal-500"></div>
      </div>
    );
  }

  return (
    <div className="w-[350px] h-[150px] bg-white">
      <div className="flex justify-between items-center border-b border-[#CFD2DE] p-[6px] mb-[10px]">
        <span className="text-[16px] ml-[5px]">Readme</span>
        <span
          className="cursor-pointer"
          //   ref={saveIconRef}
          onClick={handleSave}
          style={{
            pointerEvents: isSaving || !isEditing ? "none" : "auto",
            cursor: isSaving || !isEditing ? "not-allowed" : "pointer",
          }}
        >
          {isSaving ? (
            <div className="w-5 h-5 border border-[#00B2A1] border-t-transparent rounded-full animate-spin"></div>
          ) : (
            <img src={!isEditing ? "/assets/save_icon_dis.svg" : "/assets/save_icon_act.svg"} alt="Save Icon" />
          )}
        </span>
      </div>
      <textarea
        className="w-full h-[150px] border border-transparent focus:border-[#00B2A1]  outline-none  resize-none text-[14px] p-[0px_11px] font-inherit"
        placeholder="Type project notes, goals, reminders, or instructions."
        value={readmeText}
        onChange={handleChange}
      // readOnly={!isEditing}
      />
      {toast && <Toast message={toast.message} type={toast.type} onClose={() => setToast(null)} />}
    </div>
  );
};

export default Readme;
