import { NavItem } from "@/types";

export const navItems: NavItem[] = [
  {
    key: "home",
    label: "Home",
    href: "/",
    icon: "dashboard",
    iconSelected: "dashboard-selected",
  },
  {
    key: "value-analytics",
    label: "Value Analytics",
    href: "/value",
    icon: "value-analytics",
    iconSelected: "value-analytics-selected",
    children: [
      { key: "operational-efficiency", label: "Operational Efficiency", href: "/operational-efficiency" },
      { key: "cost-reduction", label: "Cost Reduction", href: "/cost-reduction" },
      { key: "revenue-growth", label: "Revenue Growth", href: "/revenue-growth" },
      { key: "customer-experience", label: "Customer Experience", href: "/customer-experience" },
    ],
  },
  {
    key: "explore-opportunities",
    label: "Explore Opportunities",
    href: "/explore-opportunities",
    icon: "explore-opportunities",
    iconSelected: "explore-opportunities-selected",
  },
  {
    key: "data",
    label: "Data",
    href: "/data",
    icon: "data",
    iconSelected: "data-selected",
    children: [
      { key: "world-data", label: "World Data", href: "/data/world-data" },
      {
        key: "company-data",
        label: "Company Data",
        href: "/data/company-data",
      },
      {
        key: "platform-connection",
        label: "Platform Connection",
        href: "/data/platform-connection",
      },
    ],
  },
  {
    key: "projects",
    label: "Projects",
    href: "/projects",
    icon: "projects",
    iconSelected: "projects-selected",
  },
  {
    key: "operations",
    label: "Operations",
    href: "/apps/deployed-projects",
    icon: "operations",
    iconSelected: "operations-selected",
    children: [
      {
        key: "deployed-projects",
        label: "Deployed Projects",
        href: "/apps/deployed-projects",
      },
      {
        key: "governance",
        label: "Governance",
        href: "/operations/governance",
      },
    ],
  },
];
