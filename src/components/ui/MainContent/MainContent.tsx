"use client";

import { usePathname } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import Breadcrumb from "../breadcrumb/Breadcrumb";

export default function MainContent({ children }: { children: React.ReactNode }) {
  const isSlimExpanded = useSelector((state: any) => state.sidebar.isSlimExpanded);
  const pathname = usePathname();
  const mainRef = useRef<HTMLElement | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  
  useEffect(() => {
    if (mainRef.current) {
      mainRef.current.scrollTo(0, 0);
    }
  }, [pathname]);

  useEffect(() => {
    // Function to check if viewport is mobile
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768); // 768px is a common breakpoint for mobile
    };

    // Check on initial load
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener("resize", checkIfMobile);

    // Cleanup event listener on component unmount
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  return (
    <main
      ref={mainRef}
      style={{ height: "calc(100vh - 64px)", overflowY: "auto" }}
      className={`flex-1 transition-all duration-300 bg-white mt-16 border-none ${
        // SlimSidebar width: 220px expanded + 40px gap = 280px, 80px collapsed + 40px gap = 120px
        isMobile ? "ml-0" : (isSlimExpanded ? "ml-[220px]" : "ml-[80px]")
        }`}
    >
      {/* Only show Breadcrumb on desktop */}
      {!isMobile && <Breadcrumb />}
      {children}
    </main>
  );
}
