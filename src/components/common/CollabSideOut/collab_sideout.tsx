import { <PERSON><PERSON> } from "@/components/ui/button/button";
import React, { useEffect, useState } from "react";
import { IoClose } from "react-icons/io5";
import { Divide, Loader2, X } from "lucide-react";
import Input from "@/components/ui/search-input/search-input";
import { AddCollabrators, getNonCollabrators } from "@/service/projects";
import { Collaborator, User } from "@/service/types/types";
import { MdPerson } from "react-icons/md";

interface SlideOutPanelProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  projectId: string | null;
  setToast: React.Dispatch<React.SetStateAction<ToastState>>;
  nonCollaborators: User[] | null;
  fetchCollaborators: () => void;
  fetchNonCollaborators: () => void;
  fetchProjectDetails: () => void;
}

interface SelectedUser extends User {
  role: string;
}

type ToastState = {
  message: string;
  type: "success" | "error";
} | null;

const SlideOutPanel: React.FC<SlideOutPanelProps> = ({
  isOpen,
  onClose,
  title,
  projectId,
  setToast,
  nonCollaborators,
  fetchCollaborators,
  fetchNonCollaborators,
  fetchProjectDetails,
}) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"; // Prevent background scrolling
    } else {
      document.body.style.overflow = ""; // Restore background scrolling
    }
    return () => {
      document.body.style.overflow = ""; // Cleanup on unmount
    };
  }, [isOpen]);

  // const [users, setUsers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [selectedUsers, setSelectedUsers] = useState<{
    [key: string]: SelectedUser;
  }>({});
  const imgBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "";


  const [addBtnLoading, setAddBtnLoading] = useState<boolean>(false);

  // Filter users based on search query
  const filteredUsers = nonCollaborators?.filter((user) => user.name.toLowerCase().includes(searchQuery.toLowerCase()));

  // Handle role selection
  const handleRoleSelect = (user: User, role: string) => {
    setSelectedUsers((prev) => ({
      ...prev,
      [user._id]: { ...user, role },
    }));
  };

  //Add collaboratos
  const handleApply = async () => {
    setAddBtnLoading(true);
    const payload = {
      projectId: projectId ? projectId : "",
      collaborators: Object.values(selectedUsers).map((user) => ({
        userId: user._id,
        userRole: user.role,
      })),
    };
    try {
      const res = await AddCollabrators(payload);
      if (res.status == 201) {
        setToast({
          message: "Collaborators added successfully",
          type: "success",
        });
        fetchCollaborators();
        fetchNonCollaborators();
        fetchProjectDetails();
        setAddBtnLoading(false);
      } else {
        setToast({ message: "Something went wrong!", type: "error" });
        setAddBtnLoading(false);
      }
    } catch (error: any) {
      setToast({ message: error.message, type: "error" });
      onClose();
      setAddBtnLoading(false);
    } finally {
      onClose();
      setSearchQuery("");
      setSelectedUsers({});
      setAddBtnLoading(false);
    }
  };

  const handleRemoveUser = (userId: string) => {
    setSelectedUsers((prev) => {
      const updatedUsers = { ...prev };
      delete updatedUsers[userId];
      return updatedUsers;
    });
  };

  const handleCancel = () => {
    setSelectedUsers({});
    setSearchQuery("");
    onClose();
  };

  return (
    <div
      className={`z-50 fixed top-0 right-0 h-screen w-[calc(100%-210px)] bg-white shadow-lg transform ${isOpen ? "translate-x-0" : "translate-x-full"
        } transition-transform duration-300 ease-in-out flex flex-col`}
    >
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 flex h-14 items-center justify-between bg-[#3B4154] px-6">
        <h2 className="text-lg font-medium text-white">Add users as collaborators</h2>
        <Button variant="ghost" size="icon" className="text-white hover:bg-white/20" onClick={handleCancel}>
          <X className="h-5 w-5" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex overflow-y-auto h-full">
        {/* Search Users */}
        <div className="flex flex-col gap-5 w-3/4 h-full bg-white p-5">
          <input
            type="text"
            className="flex h-10 w-full rounded-md border border-input bg-dataPreviewTabsSelectorBg px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
            placeholder="Search Username"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <div>
            {searchQuery === "" ? (
              <div className="flex flex-col gap-2 justify-center items-center w-full mt-10">
                <img src="/placeholder.svg" alt="img" width={200} height={200} className="text-gray-700" />
                <h1 className="text-sm text-gray-600">Start typing in the search field to get a list of users</h1>
              </div>
            ) : filteredUsers?.length === 0 ? (
              <div className="flex flex-col gap-2 justify-center items-center w-full mt-10">
                <img src="/placeholder.svg" alt="img" width={200} height={200} className="text-gray-700" />
                <h1 className="text-sm text-gray-600">No user found</h1>
              </div>
            ) : (
              filteredUsers?.map((user) => (
                <div
                  key={user._id}
                  className="font-[Lato] w-full flex gap-2 items-center border-b p-2 border-[#CFD2DE]"
                >
                  <div className="flex justify-between items-center w-full">
                    <div className="flex gap-2 items-center">
                      <div className="w-10 h-8 rounded-full bg-gray-500 flex items-center justify-center">
                        {user.thumbnail ? (
                          <img
                            src={imgBaseUrl?.replace("/api", "") + user.thumbnail}
                            alt="User Avatar"
                            className="w-full h-full rounded-full object-cover"
                          />
                        ) : (
                          <MdPerson className="w-full h-full rounded-full object-cover" />
                        )}
                      </div>
                      <div className="flex flex-col w-full justify-start">
                        <p>{user.name}</p>
                        <p className="text-sm">{user.email}</p>
                      </div>
                    </div>
                    <div className="">
                      <select
                        className="outline-none p-2 rounded text-teal-500"
                        value={selectedUsers[user._id]?.role || ""}
                        onChange={(e) => handleRoleSelect(user, e.target.value)}
                      >
                        <option value="" disabled className="hover:bg-white">
                          Add role as
                        </option>
                        <option value="Manager" className="hover:bg-white">
                          Manager
                        </option>
                        <option value="Executive" className="hover:bg-white">
                          Executive
                        </option>
                        <option value="Participant" className="hover:bg-white">
                          Participant
                        </option>
                      </select>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Selected  Users */}
        <div className="flex flex-col gap-2 w-1/4 h-full bg-[#F4F5F6] p-5">
          <h2 className="text-md font-semibold mb-2">Selected Users</h2>
          <div>
            {Object.values(selectedUsers).map((user) => (
              <div
                key={user._id}
                className="font-[Lato] w-full flex gap-2 items-center p-2 border-[#888FAA] border-b-[0.5px] mb-2 justify-between"
              >
                <div className="flex gap-2 items-start w-full">
                  <div className="w-10 h-8 rounded-full mt-2 bg-gray-500 flex items-center justify-center">
                    {user.thumbnail ? (
                      <img src={imgBaseUrl?.replace("/api", "") + user.thumbnail} alt="User Avatar" className="w-full h-full rounded-full object-cover" />
                    ) : (
                      <MdPerson className="w-full h-full rounded-full object-cover" />
                    )}
                  </div>
                  <div className="flex flex-col w-full justify-start">
                    <p className="text-[#3B4154]">{user.name}</p>
                    <p className="text-[12px] text-[#666F8F]">{user.email}</p>
                    <p className="text-[#666F8F] text-[12px] pt-2 ">Role: {user.role}</p>
                  </div>
                </div>
                <button className="ml-4 text-[#666F8F] font-bold" onClick={() => handleRemoveUser(user._id)}>
                  ✕
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-2 mr-2 p-2 border-t-2">
        <button className="px-8 py-2 border-2 border-teal-500 text-teal-500 rounded-md" onClick={handleCancel}>
          Cancel
        </button>
        <button className="px-8 py-2 rounded-md  bg-teal-500 text-white flex items-center gap-2" onClick={handleApply}>
          {addBtnLoading && <Loader2 className="w-4 h-4 animate-spin" />}
          {!addBtnLoading && "Add"}
        </button>
      </div>
    </div>
  );
};

export default SlideOutPanel;
