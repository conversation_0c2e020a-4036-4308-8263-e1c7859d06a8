import { useState } from "react";

const InputWithTooltip = () => {
    const [showTooltip, setShowTooltip] = useState(false);

    return (
        <div className="flex flex-col space-y-1">
            <div className="flex items-center space-x-1 relative">
                <span className="text-gray-700 font-medium">
                    Name <span className="text-red-500">*</span>
                </span>
                <div
                    className="relative flex items-center cursor-pointer"
                    onMouseEnter={() => setShowTooltip(true)}
                    onMouseLeave={() => setShowTooltip(false)}
                >
                    <span className="text-gray-500 hover:text-gray-700">ℹ️</span>
                    {showTooltip && (
                        <div className="absolute left-5 top-6 bg-gray-800 text-white text-xs px-2 py-1 rounded-md shadow-lg">
                            This is a required field.
                        </div>
                    )}
                </div>
            </div>
            <input
                type="text"
                placeholder="Eg. Sales Prediction"
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700 placeholder-gray-400 w-full"
            />
        </div>
    );
};

export default InputWithTooltip;
