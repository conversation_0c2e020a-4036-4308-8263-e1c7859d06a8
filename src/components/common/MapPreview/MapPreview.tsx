"use client";
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import { Icon } from "leaflet";

// Fix for default marker icon in Next.js
const redPinIcon = new Icon({
  iconUrl: "/red-marker-icon.png", // Ensure this file exists or use a valid URL
  iconRetinaUrl: "/red-marker-icon-2x.png",
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41],
});

interface MapPreviewProps {
  filename: string;
  previewDataResponse: {
    data: any;
    status: number;
  };
  headers: string[];
  parsedRows: Record<string, any>[];
}

const MapUpdater: React.FC<{ center: [number, number] }> = ({ center }) => {
  const map: any = useMap();
  useEffect(() => {
    map.setView(center, 11, { animate: true }); // Reduced zoom level
  }, [center, map]);
  return null;
};

const MapPreview: React.FC<MapPreviewProps> = ({
  filename,
  previewDataResponse,
  headers,
  parsedRows,
}) => {
  const [selectedLat, setSelectedLat] = useState<any>("");
  const [selectedLng, setSelectedLng] = useState<any>("");
  const [mapCenter, setMapCenter] = useState<[number, number] | null>(null);

  useEffect(() => {
    // Reset selections when file changes
    setSelectedLat("");
    setSelectedLng("");
  }, [filename]);

  useEffect(() => {
    if (headers.length > 0) {
      // Try to automatically detect latitude/longitude fields
      const latFields = headers.filter(
        (h: any) =>
          h.toLowerCase().includes("lat") ||
          h.toLowerCase().includes("latitude")
      );
      const lngFields = headers.filter(
        (h: any) =>
          h.toLowerCase().includes("lng") ||
          h.toLowerCase().includes("lon") ||
          h.toLowerCase().includes("longitude")
      );
      setSelectedLat(latFields[0] || headers[0]);
      setSelectedLng(lngFields[0] || headers[1] || headers[0]);
    }
  }, [headers]);

  // Filter valid coordinates and prepare marker data
  const markerData = parsedRows
    .map((row: any) => {
      const lat = parseFloat(row[selectedLat]);
      const lng = parseFloat(row[selectedLng]);
      if (
        !isNaN(lat) &&
        !isNaN(lng) &&
        lat >= -90 &&
        lat <= 90 &&
        lng >= -180 &&
        lng <= 180
      ) {
        return { lat, lng, data: row };
      }
      return null;
    })
    .filter((item): item is NonNullable<typeof item> => item !== null);

  useEffect(() => {
    if (markerData.length > 0 && selectedLat && selectedLng) {
      setMapCenter([markerData[0].lat, markerData[0].lng]);
    }
  }, [markerData, selectedLat, selectedLng]);

  return (
    <div className="p-4">
      <div className="flex flex-wrap gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Latitude Field
          </label>
          <select
            value={selectedLat}
            onChange={(e) => setSelectedLat(e.target.value)}
            className="p-2 border border-gray-300 rounded"
          >
            {headers.map((header: any) => (
              <option key={header} value={header}>
                {header}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Longitude Field
          </label>
          <select
            value={selectedLng}
            onChange={(e) => setSelectedLng(e.target.value)}
            className="p-2 border border-gray-300 rounded"
          >
            {headers.map((header: any) => (
              <option key={header} value={header}>
                {header}
              </option>
            ))}
          </select>
        </div>
      </div>
      <div className="w-full h-96 border rounded">
        <MapContainer
          center={mapCenter || [0, 0]}
          zoom={11} // Reduced zoom level
          scrollWheelZoom={false}
          style={{ height: "100%", width: "100%" }}
        >
          {mapCenter && <MapUpdater center={mapCenter} />}
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          {markerData.map((marker, index) => (
            <Marker
              key={index}
              position={[marker.lat, marker.lng]}
              icon={redPinIcon}
            >
              <Popup>
                {Object.entries(marker.data).map(([key, value ]) => (
                  <div key={key}>
                    <strong>{key}:</strong> {value ? `${value}` : ""}
                  </div>
                ))}
              </Popup>
            </Marker>
          ))}
        </MapContainer>
      </div>
    </div>
  );
};

export default MapPreview;
