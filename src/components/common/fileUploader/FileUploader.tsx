import { useRef, useState } from "react";

const FileUploader = ({
  setIsDialogOpen,
  uploadEntity,
}: {
  setIsDialogOpen: React.Dispatch<React.SetStateAction<boolean>>;
  uploadEntity: string;
}) => {
  const [files, setFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setFiles([...files, ...Array.from(event.target.files)]);
    }
  };

  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
  };

  const uploadFiles = async () => {
    if (files.length === 0) {
      alert("Please select at least one file.");
      return;
    }

    const formData = new FormData();
    files.forEach((file) => formData.append("files", file));

    setUploading(true);

    try {
      const response = await fetch("https://your-backend.com/upload", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        alert("Files uploaded successfully!");
        setFiles([]); // Clear files after upload
      } else {
        alert("Upload failed. Please try again.");
      }
    } catch (error) {
      //console.error("Upload error:", error);
      //alert("An error occurred while uploading.");
    } finally {
      setUploading(false);
    }
  };

  return (
    <div>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
        <div className="bg-white p-4 w-96">
          <h1>
            <p className="text-sm mt-1">
              {uploadEntity} data will benefit this project but we coudn't find
              any source from your data. If you can{" "}
              <b>upload some {uploadEntity} data assets.</b>
            </p>
          </h1>
          <div className="">
            {/* Hidden file input */}
            <input
              type="file"
              multiple
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
            />
            {/* Custom Upload Button */}
            <button
              onClick={() => fileInputRef.current?.click()}
              className="px-3 py-1 border-2 border-teal-500 text-teal-500 rounded-md mt-3 mb-3"
            >
              Choose Files
            </button>
            {files.length > 0 && (
              <ul className="list-disc mb-3">
                {files.map((file, index) => (
                  <li key={index} className="flex justify-between items-center">
                    {file.name}{" "}
                    <button
                      className="text-red-500 hover:text-red-700 text-sm"
                      onClick={() => removeFile(index)}
                    >
                      Remove
                    </button>
                  </li>
                ))}
              </ul>
            )}
          </div>
          <div className="flex justify-end gap-2 p-2">
            <button
              className="px-3 py-1 border-2 border-teal-500 text-teal-500 rounded-md"
              onClick={() => {
                setIsDialogOpen(false);
                setFiles([]);
              }}
            >
              Cancel
            </button>
            <button
              className={`px-3 py-1 rounded-md ${
                files.length !== 0
                  ? "bg-teal-500 text-white"
                  : "bg-[#CED2DE] text-[#888FAA] cursor-not-allowed"
              }`}
              disabled={files.length === 0}
              //onClick={handleApply}
            >
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileUploader;
