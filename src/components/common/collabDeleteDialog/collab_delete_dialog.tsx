import React from "react";
import Image from "next/image";


interface ConfirmDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    collaboratorName: string;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({ isOpen, onClose, onConfirm, collaboratorName }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white p-4 w-84 ">
                <div className="flex flex-col items-center gap-4">
                    <Image
                        src="/assets/icons/warning_avatar_icon.svg"
                        alt="Warning Icon"
                        width={40}
                        height={40}
                    />
                    <p className="text-[#C0433C] font-medium text-regular text-center mb-4">You are about to remove {collaboratorName}!</p>
                </div>
                <hr />
                <div className="flex justify-between mt-4">
                    <button onClick={onConfirm} className="text-[#C0433C] font-medium">
                        Yes, Remove it
                    </button>
                    <button onClick={onClose} className="bg-teal-500 text-white px-4 py-2 rounded">
                        No, Cancel
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ConfirmDialog;
