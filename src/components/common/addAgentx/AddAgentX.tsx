import React, { useEffect, useRef, useState } from "react";
import { Toast } from "@/components/ui/toast/toast";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { setActiveAssetTab } from "@/lib/store/features/project/projectAssetsSlice";
import { setProjectActiveTab } from "@/lib/store/features/project/projectTabSlice";
import { addAgentXToProject } from "@/service/agentXService";
import { Loader2 } from "lucide-react";
import { buttonVariants } from "../sidebar/sidebar-btn";
import filesService from "@/service/files";

export type ToastState = {
  message: string;
  type: "success" | "error";
} | null;

export default function AddAgentX({
  projectId,
  title,
  description,
  tags,
  isAdd,
  isAddedtoProject,
  setToast,
  getRecommendations,
}: any) {
  const dispatch = useDispatch();
  const router = useRouter();
  const [adding, setAdding] = useState(false);
  //const [addingIndex, setAddingIndex] = useState<number | null>(null);
  //const [toast, setToast] = useState<ToastState>(null);
  const [isAdded, setIsAdded] = useState(isAddedtoProject);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [file, setFile] = useState<File | null>(null);

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (!event.target.files || event.target.files.length === 0) return;

    const file = event.target.files[0];
    // const formData = new FormData();
    // formData.append("file", file);
    setAdding(true);
    try {
      const fileExtension = file.name.split(".").pop()?.toLowerCase() || "";
      const fileName = file.name.split(".")[0];
      const newFile = new File([file], `${fileName}.${fileExtension}`, {
        type: file.type,
      });

      const formData = new FormData();
      formData.append("file", newFile);
      const response = await filesService.uploadFile(
        newFile,
        description,
        title?.toLowerCase().replace(/ /g, "-") || ""
      );

      if (response.status !== 201) {
        throw new Error("Upload failed");
      }
      handleAddToProject(
        title,
        description,
        "Agent-X",
        [fileExtension, ...tags],
        fileExtension,
        response.data.url
      );
    } catch (error) {
      setToast({ message: "Something went wrong!", type: "error" });
      //console.error("Error uploading file:", error);
    } finally {
      setAdding(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleAddToProject = async (
    title: string,
    description: string,
    source: string,
    tags: string[],
    type: string,
    url?: string
  ) => {
    try {
      setAdding(true);
      dispatch(setActiveAssetTab("All Assets"));
      const res = await addAgentXToProject({
        projectId: projectId,
        title: title || "",
        description: description || "",
        type: type || "Database",
        url: url,
        source: source || "",
        tags: tags || [],
      });
      if (res) {
        setToast({
          message: "Added to Project successfully",
          type: "success",
        });
        window.scrollTo(0, 0);
        getRecommendations(false);
      }
    } catch (error) {
      setToast({ message: "Something went wrong!", type: "error" });
      //console.log(error);
    } finally {
      setAdding(false);
    }
  };
  return (
    <div>
      {isAdded ? (
        <button className="text-sm text-gray-500 w-full justify-end cursor-not-allowed">
          Added to Project
        </button>
      ) : (
        <div className="flex items-end justify-end">
          {isAdd ? (
            <button
              className="flex text-sm text-teal-500 w-full justify-end"
              onClick={() => {
                //setAddingIndex(index);
                handleAddToProject(
                  title,
                  description,
                  "Agent-X",
                  tags,
                  "Database"
                );
              }}
            >
              {adding ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <span className="flex gap-1">
                  <span>+</span>
                  <span>Add</span>
                  <span>to</span>
                  <span>Project</span>
                </span>
              )}
            </button>
          ) : (
            <div>
              {/* Hidden file input */}
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                className="hidden"
                accept=".xml,.json,.csv,.xlsx,.xls"
              />
              {/* Custom Upload Button */}
              {/* <button
                onClick={() => fileInputRef.current?.click()}
                className="px-3 py-1 border-2 border-teal-500 text-teal-500 rounded-md mt-3 mb-3"
              >
                Choose Files
              </button> */}
              <button
                className="flex text-sm text-teal-500 w-full justify-end"
                onClick={() => fileInputRef.current?.click()}
              >
                {adding ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <span className="flex gap-1">
                    <span>+</span>
                    <span>Upload</span>
                    <span>Data</span>
                  </span>
                )}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
