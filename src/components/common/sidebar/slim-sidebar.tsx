"use client";

import { useEffect, useRef, useState } from "react";
import { motion } from "framer-motion";
import {
  TbDatabase,
  TbChartArrowsVertical,
  TbBulb,
  TbTriangleSquareCircle,
  TbSettings,
  TbLogout,
  TbUserCircle,
  TbRocket,
  TbChevronLeft,
  TbChevronRight,
  TbHome,
  TbChevronsLeft,
} from "react-icons/tb";
import { HiOutlineSupport } from "react-icons/hi";
import { cn } from "@/lib/utils";
import { NavSection } from "@/types/sidebar";
import Notification from "@/components/pages/Notification/Notification";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover/popover";
import Link from "next/link";
import { MdOutlineManageAccounts } from "react-icons/md";
import { useSelector, useDispatch } from "react-redux";
import { toggleSlimSidebar } from "@/lib/store/features/sidebar/sidebarSlice";
import { PlusIcon } from "@heroicons/react/24/outline";

interface SlimSidebarProps {
  activeSection: string;
  onSectionClick: (section: NavSection) => void;
  setIsOpen: (isOpen: boolean) => void;
  setTitle: (title: string) => void;
  setIsWorldDataset: (isWorld: boolean) => void;
  isDropdownOpen: boolean;
  dropdownRef: React.RefObject<HTMLDivElement>;
  handleLogout: () => void;
}

import type { RootState } from '@/lib/store';

export function SlimSidebar({
  activeSection,
  onSectionClick,
  isDropdownOpen,
  dropdownRef,
  handleLogout,
  setIsOpen,
  setTitle,
  setIsWorldDataset
}: SlimSidebarProps) {
  const favorites = useSelector((state: RootState) => state.favorites.items);
  const [openPopover, setOpenPopover] = useState<string | null>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const dialogRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();
  const isExpanded = useSelector((state: any) => state.sidebar.isSlimExpanded);
  const userInfo = useSelector((state: any) => state.user.userInfo);

  const {
    user: { isSysAdmin },
  } = userInfo;

  const showButton = isSysAdmin || userInfo?.user?.department?.some(
    (department: { departmentRole: string }) =>
      department.departmentRole === "admin" ||
      department.departmentRole === "editor"
  );

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dialogRef.current && !dialogRef.current.contains(event.target as Node)) {
        setIsDialogOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const sections = {
    home: {
      title: "Home",
      icon: TbHome,
      items: [],
      href: "/",
    },
    data: {
      title: "Data",
      icon: TbDatabase,
      items: [
        { label: "World Data", href: "/data/world-data" },
        { label: "Company Data", href: "/data/company-data" },
        { label: "Platform Connection", href: "/data/platform-connection" },
      ],
      href: "/data",
    },
    value: {
      title: "Value Analytics",
      icon: TbChartArrowsVertical,
      items: [
        { label: "Operational Efficiency", href: "/value/operational-efficiency" },
        { label: "Cost Reduction", href: "/value/cost-reduction" },
        { label: "Revenue Growth", href: "/value/revenue-growth" },
        { label: "Customer Experience", href: "/value/customer-experience" },
      ],
      href: "/value",
    },
    opportunities: {
      title: "Opportunities",
      icon: TbBulb,
      items: [],
      href: "/explore-opportunities",
    },
    apps: {
      title: "Apps",
      icon: TbRocket,
      items: [{ label: "Governance Insights", href: "/apps/governance" }],
      href: "/apps",
    },
    projects: {
      title: "Projects",
      icon: TbTriangleSquareCircle,
      items: [],
      href: "/projects",
    },
    support: {
      title: "Support",
      icon: HiOutlineSupport,
      items: [],
      href: "/support",
    },
    profile: {
      title: "Profile",
      icon: TbUserCircle,
      items: [],
      href: "/profile",
    },
  };

  const navItems = [
    { id: "home", icon: TbHome, label: "Home" },
    { id: "value", icon: TbChartArrowsVertical, label: "Value Analytics" },
    { id: "opportunities", icon: TbBulb, label: "Opportunities" },
    { id: "apps", icon: TbRocket, label: "Apps" },
    { id: "projects", icon: TbTriangleSquareCircle, label: "Projects" },
    { id: "data", icon: TbDatabase, label: "Data" },
    { id: "support", icon: HiOutlineSupport, label: "Support" },
    // { id: "settings", icon: TbSettings, label: "Settings" },
    { id: "notifications", icon: Notification, label: "Notifications" },
    { id: "profile", icon: TbUserCircle, label: "Profile" },
  ];

  const disabledNavIds =
    process.env.NEXT_PUBLIC_USER_NAV_DISABLED?.split(",") || [];

  return (
    <motion.div
      className="flex flex-col fixed inset-y-0 left-0 top-0 bg-[#f3f3f3] z-50"
      animate={{ width: isExpanded ? 220 : 80 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >

      <nav className="flex-1 flex flex-col mt-2">
        {/* Sutra Logo Section */}
        <div className="flex h-10 items-center justify-between mt-2 pl-4 pr-2 transition-opacity">
          {isExpanded ? (
            <>
              <img
                src="/sutraLogo-Color.svg"
                alt="SUTRA.ai Logo"
                className="h-fit w-36"
              />
              {/* Collapse/Expand Toggle - show next to logo when expanded */}
              {isHovered && (
                <div className="relative group">
                  <button
                    onClick={() => dispatch(toggleSlimSidebar(!isExpanded))}
                    className="p-1 text-gray-500 hover:text-gray-700 transition-all duration-200"
                    title={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
                  >
                    <TbChevronsLeft
                      className={`w-6 h-full transition-transform duration-200 ${isExpanded ? 'rotate-0' : 'rotate-180'
                        }`}
                    />
                  </button>
                  {/* Custom Tooltip */}
                  <div className="absolute right-0 top-full mt-1 p-2 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                    {isExpanded ? "Collapse sidebar" : "Expand sidebar"}
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="flex justify-center w-full h-10">
              {isHovered ? (
                /* Show expand button instead of icon when hovered and collapsed */
                <div className="relative group">
                  <button
                    onClick={() => dispatch(toggleSlimSidebar(!isExpanded))}
                    className="p-2 text-gray-500 hover:text-gray-800 transition-all duration-200"
                    title={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
                  >
                    <TbChevronsLeft
                      className={`w-6 h-6 transition-transform duration-200 ${isExpanded ? 'rotate-0' : 'rotate-180'
                        }`}
                    />
                  </button>
                  {/* Custom Tooltip */}
                  <div className="absolute left-1/2 -translate-x-1/2 top-full mt-1 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                    {isExpanded ? "Collapse sidebar" : "Expand sidebar"}
                  </div>
                </div>
              ) : (
                /* Show Sutra icon when not hovered and collapsed */
                <img
                  src="/sutra-icon.svg"
                  alt="SUTRA.ai Icon"
                  className="h-8 w-8"
                />
              )}
            </div>
          )}
        </div>
        <div className="flex-1 space-y-1 mt-6">
          {navItems.slice(0, 6).map((item) => (
            <div key={item.id}>
              {isExpanded ? (
                <Popover
                  open={openPopover === item.id}
                  onOpenChange={(open) => setOpenPopover(open ? item.id : null)}
                >
                  <PopoverTrigger asChild>
                    <button
                      onMouseEnter={() => setOpenPopover(item.id)}
                      onMouseLeave={() => setOpenPopover(null)}
                      onClick={() => {
                        if (!disabledNavIds.includes(item.id)) {
                          onSectionClick(item.id as NavSection);
                        }
                      }}
                      className={cn(
                        "group relative w-full h-[56px] flex items-center px-4 text-left text-[#3B4154] transition-all hover:bg-white",
                        activeSection === item.id ? "text-teal-600 hover:text-teal-800 bg-white font-[800]" : "hover:text-[#3B4154]"
                      )}
                    >
                      {activeSection === item.id && (
                        <motion.div
                          layoutId="activeSection"
                          className="absolute rounded-r-[4px] left-0 w-[6px] h-full bg-[#00B2A1]"
                        />
                      )}
                      <item.icon
                        className={cn(
                          "w-7 h-7 mr-2",
                          activeSection === item.id && "text-teal-600"
                        )}
                      />
                      <span className="font-semibold text-base group-hover:font-bold">{item.label}</span>
                    </button>
                  </PopoverTrigger>
                  <PopoverContent
                    side="right"
                    className="w-64 mt-2 p-0 rounded-md shadow-md"
                    sideOffset={-40}
                    align="start"
                    alignOffset={-10}
                    onMouseEnter={() => setOpenPopover(item.id)}
                    onMouseLeave={() => setOpenPopover(null)}
                  >
                    {sections[item.id as keyof typeof sections] && (
                      <div className="py-2">
                        <div className="px-4 py-2">
                          <h3 className="font-semibold text-lg text-gray-900 flex items-center gap-2">
                            <item.icon className="w-5 h-5" />
                            {sections[item.id as keyof typeof sections].title}
                          </h3>
                        </div>
                        {sections[item.id as keyof typeof sections].items
                          .length > 0 && (
                            <div className="py-1 border-t">
                              {sections[
                                item.id as keyof typeof sections
                              ].items.map((subItem: any, index: number) => (
                                <Link
                                  key={index}
                                  href={subItem.href}
                                  className="block px-4 py-2 text-md text-gray-700 hover:bg-gray-100"
                                >
                                  {subItem.label}
                                </Link>
                              ))}
                            </div>
                          )}
                        {(item.id === 'apps' || item.id === 'insights') && favorites.length > 0 && (
                          <div className="py-1 border-t">
                            <div className="px-4 py-2">
                              <h4 className="text-sm font-medium text-gray-500">Favorite Apps</h4>
                            </div>
                            {favorites.map((favorite) => (
                              <Link
                                key={favorite.id}
                                href={`${favorite.href}?name=${encodeURIComponent(
                                  favorite.name
                                )}&isAiBox=${favorite.isAiBox ?? false}&appUrl=${encodeURIComponent(
                                  favorite.appUrl || ""
                                )}`}
                                className="block px-4 py-2 text-md text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                              >
                                {favorite.isAiBox ? (
                                  <img src="/assets/ai_app_icon.svg" alt="" className="w-5 h-5" />
                                ) : (
                                  <img src="/assets/app-icon.svg" alt="" className="w-5 h-5" />
                                )}
                                {favorite.name}
                              </Link>
                            ))}
                          </div>
                        )}
                        {item.id === 'data' && showButton && (
                          <div className="py-2 border-t">
                            <button
                              onClick={() => setIsDialogOpen(!isDialogOpen)}
                              className="flex items-center gap-2 w-full text-left px-3 py-2 text-gray-700 hover:bg-gray-100 focus-within:ring-0 focus:outline-none"
                            >
                              <PlusIcon className="w-5 h-5 text-gray-500" />
                              <span className="font-semibold">Add Data Asset</span>
                            </button>
                            {isDialogOpen && (
                              <div
                                ref={dialogRef}
                                className="absolute mt-3 left-0 mt-1 w-40 bg-white shadow-lg rounded-md py-2 text-black z-10"
                              >
                                <button
                                  className="flex items-center gap-2 px-3 py-2 text-gray-700 hover:bg-gray-100 w-full text-left"
                                  onClick={() => {
                                    setIsDialogOpen(false);
                                    setIsOpen(true);
                                    setTitle("Add World Data");
                                    setIsWorldDataset(true);
                                  }}
                                >
                                  <PlusIcon className="w-5 h-5 text-gray-500" />
                                  <span className="text-sm">World Data</span>
                                </button>
                                <button
                                  className="flex items-center gap-2 px-3 py-2 text-gray-700 hover:bg-gray-100 w-full text-left"
                                  onClick={() => {
                                    setIsDialogOpen(false);
                                    setIsOpen(true);
                                    setTitle("Add Company Data");
                                    setIsWorldDataset(false);
                                  }}
                                >
                                  <PlusIcon className="w-5 h-5 text-gray-500" />
                                  <span className="text-sm">Company Data</span>
                                </button>
                                <button
                                  className="flex items-center gap-2 px-3 py-2 text-gray-700 hover:bg-gray-100 w-full text-left"
                                  onClick={() => {
                                    setIsDialogOpen(false);
                                    setIsOpen(true);
                                    setTitle("Add Document Data");
                                    setIsWorldDataset(false);
                                  }}
                                >
                                  <PlusIcon className="w-5 h-5 text-gray-500" />
                                  <span className="text-sm">Documents</span>
                                </button>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </PopoverContent>
                </Popover>
              ) : (
                <Popover
                  open={openPopover === item.id}
                  onOpenChange={(open) => setOpenPopover(open ? item.id : null)}
                >
                  <PopoverTrigger asChild>
                    <button
                      onMouseEnter={() => setOpenPopover(item.id)}
                      onMouseLeave={() => setOpenPopover(null)}
                      onClick={() => {
                        if (!disabledNavIds.includes(item.id)) {
                          onSectionClick(item.id as NavSection);
                        }
                      }}
                      className={cn(
                        "group relative w-full h-[60px] flex items-center justify-center text-[#3B4154] transition-all hover:bg-white",
                        activeSection === item.id ? "text-teal-800 hover:text-teal-800 bg-white" : "hover:text-[#3B4154]"
                      )}
                    >
                      {activeSection === item.id && (
                        <motion.div
                          layoutId="activeSection"
                          className="absolute rounded-r-[4px] left-0 w-[6px] h-full bg-[#00B2A1]"
                        />
                      )}
                      <item.icon
                        className={cn(
                          "w-7 h-7",
                          activeSection === item.id && "text-teal-800"
                        )}
                      />
                    </button>
                  </PopoverTrigger>
                  <PopoverContent
                    side="right"
                    className="w-64 p-0 mt-2 rounded-md shadow-md"
                    sideOffset={-8}
                    align="start"
                    alignOffset={-10}
                    onMouseEnter={() => setOpenPopover(item.id)}
                    onMouseLeave={() => setOpenPopover(null)}
                  >
                    {sections[item.id as keyof typeof sections] && (
                      <div className="py-2">
                        <div className="px-4 py-2">
                          <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                            <item.icon className="w-5 h-5" />
                            {sections[item.id as keyof typeof sections].title}
                          </h3>
                        </div>
                        {sections[item.id as keyof typeof sections].items
                          .length > 0 && (
                            <div className="py-1 border-t">
                              {sections[
                                item.id as keyof typeof sections
                              ].items.map((subItem: any, index: number) => (
                                <Link
                                  key={index}
                                  href={subItem.href}
                                  className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  {subItem.label}
                                </Link>
                              ))}
                            </div>
                          )}
                        {(item.id === 'apps' || item.id === 'insights') && favorites.length > 0 && (
                          <div className="py-1 border-t">
                            <div className="px-4 py-2">
                              <h4 className="text-sm font-medium text-gray-500">Favorite Apps</h4>
                            </div>
                            {favorites.map((favorite) => (
                              <Link
                                key={favorite.id}
                                href={`${favorite.href}?name=${encodeURIComponent(
                                  favorite.name
                                )}&isAiBox=${favorite.isAiBox ?? false}&appUrl=${encodeURIComponent(
                                  favorite.appUrl || ""
                                )}`}
                                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                              >
                                {favorite.isAiBox ? (
                                  <img src="/assets/ai_app_icon.svg" alt="" className="w-5 h-5" />
                                ) : (
                                  <img src="/assets/app-icon.svg" alt="" className="w-5 h-5" />
                                )}
                                {favorite.name}
                              </Link>
                            ))}
                          </div>
                        )}
                        {item.id === 'data' && showButton && (
                          <div className="px-4 py-2 border-t">
                            <button
                              onClick={() => setIsDialogOpen(!isDialogOpen)}
                              className="flex items-center gap-2 w-full text-left px-2 py-2 text-gray-700 hover:bg-gray-100 rounded-md focus-within:ring-0 focus:outline-none"
                            >
                              <PlusIcon className="w-5 h-5 text-gray-500" />
                              <span className="font-semibold">Add Data Asset</span>
                            </button>
                            {isDialogOpen && (
                              <div
                                ref={dialogRef}
                                className="absolute left-0 mt-1 w-40 bg-white shadow-lg rounded-md py-2 text-black z-10"
                              >
                                <button
                                  className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 w-full text-left"
                                  onClick={() => {
                                    setIsDialogOpen(false);
                                    setIsOpen(true);
                                    setTitle("Add World Data");
                                    setIsWorldDataset(true);
                                  }}
                                >
                                  <PlusIcon className="w-5 h-5 text-gray-500" />
                                  <span className="text-sm">World Data</span>
                                </button>
                                <button
                                  className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 w-full text-left"
                                  onClick={() => {
                                    setIsDialogOpen(false);
                                    setIsOpen(true);
                                    setTitle("Add Company Data");
                                    setIsWorldDataset(false);
                                  }}
                                >
                                  <PlusIcon className="w-5 h-5 text-gray-500" />
                                  <span className="text-sm">Company Data</span>
                                </button>
                                <button
                                  className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 w-full text-left"
                                  onClick={() => {
                                    setIsDialogOpen(false);
                                    setIsOpen(true);
                                    setTitle("Add Document Data");
                                    setIsWorldDataset(false);
                                  }}
                                >
                                  <PlusIcon className="w-5 h-5 text-gray-500" />
                                  <span className="text-sm">Documents</span>
                                </button>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </PopoverContent>
                </Popover>
              )}
            </div>
          ))}
        </div>

        <div
          className="py-3 space-y-1 flex justify-center flex-col"
          data-dropdown-button
        >
          {/* Company Logo - only show when expanded and before support */}
          {isExpanded && (
            <div className="flex justify-center items-center py-2 mb-2">
              <img
                src={process.env.NEXT_PUBLIC_USER_LOGO || "/default-logo.svg"}
                alt="Company Logo"
                className="h-8"
              />
            </div>
          )}

          {navItems.slice(6).map((item) => {
            if (item.id === "profile") {
              return (
                <div key={item.id}>
                  {isExpanded ? (
                    <Popover
                      open={openPopover === item.id}
                      onOpenChange={(open) =>
                        setOpenPopover(open ? item.id : null)
                      }
                    >
                      <PopoverTrigger asChild>
                        <button
                          onMouseEnter={() => setOpenPopover(item.id)}
                          onMouseLeave={() => setOpenPopover(null)}
                          onClick={() => {
                            if (!disabledNavIds.includes(item.id)) {
                              onSectionClick(item.id as NavSection);
                            }
                          }}
                          className={cn(
                            "group w-full h-[48px] flex items-center px-4 text-left text-[#3B4154] transition-all hover:bg-white",
                            activeSection === item.id ? "text-teal-800 hover:text-teal-800 bg-white" : "hover:text-[#3B4154]"
                          )}
                        >
                          <item.icon
                            className={cn(
                              "w-7 h-7 mr-2",
                              activeSection === item.id && "text-teal-800"
                            )}
                          />
                          <span className="font-semibold text-base">
                            {item.label}
                          </span>
                        </button>
                      </PopoverTrigger>
                      <PopoverContent
                        side="right"
                        className="p-0 w-48 rounded-md shadow-md"
                        sideOffset={-40}
                        align="start"
                        alignOffset={-10}
                        onMouseEnter={() => setOpenPopover(item.id)}
                        onMouseLeave={() => setOpenPopover(null)}
                      >
                        <div className="py-2 text-sm">
                          {isSysAdmin && (
                            <a
                              href="/manage"
                              className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer"
                            >
                              <MdOutlineManageAccounts className="w-6 h-6" />
                              Manage
                            </a>
                          )}
                          <a
                            href="/profile"
                            className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer"
                          >
                            <TbUserCircle className="w-6 h-6" />
                            View Profile
                          </a>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleLogout();
                            }}
                            className="flex items-center gap-2 w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
                          >
                            <TbLogout className="w-6 h-6" />
                            Logout
                          </button>
                        </div>
                      </PopoverContent>
                    </Popover>
                  ) : (
                    <Popover
                      open={openPopover === item.id}
                      onOpenChange={(open) =>
                        setOpenPopover(open ? item.id : null)
                      }
                    >
                      <PopoverTrigger asChild>
                        <button
                          onMouseEnter={() => setOpenPopover(item.id)}
                          onMouseLeave={() => setOpenPopover(null)}
                          onClick={() => {
                            if (!disabledNavIds.includes(item.id)) {
                              onSectionClick(item.id as NavSection);
                            }
                          }}
                          className={cn(
                            "group w-full h-[48px] flex items-center justify-center text-[#3B4154] transition-all hover:bg-white",
                            activeSection === item.id ? "text-teal-800 hover:text-teal-800 bg-white" : "hover:text-[#3B4154]"
                          )}
                        >
                          <item.icon
                            className={cn(
                              "w-7 h-7",
                              activeSection === item.id && "text-teal-800"
                            )}
                          />
                        </button>
                      </PopoverTrigger>
                      <PopoverContent
                        side="right"
                        className="p-0 w-48 rounded-md shadow-md"
                        sideOffset={-8}
                        align="start"
                        alignOffset={-10}
                        onMouseEnter={() => setOpenPopover(item.id)}
                        onMouseLeave={() => setOpenPopover(null)}
                      >
                        <div className="py-2 text-sm">
                          {isSysAdmin && (
                            <a
                              href="/manage"
                              className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer"
                            >
                              <MdOutlineManageAccounts className="w-6 h-6" />
                              Manage
                            </a>
                          )}
                          <a
                            href="/profile"
                            className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer"
                          >
                            <TbUserCircle className="w-6 h-6" />
                            View Profile
                          </a>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleLogout();
                            }}
                            className="flex items-center gap-2 w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
                          >
                            <TbLogout className="w-6 h-6" />
                            Logout
                          </button>
                        </div>
                      </PopoverContent>
                    </Popover>
                  )}
                </div>
              );
            }

            if (item.id === "notifications") {
              return (
                <div key={item.id}>
                  {isExpanded ? (
                    <div className={cn(
                      "group w-full h-[48px] flex items-center text-left text-[#3B4154] transition-all hover:bg-white",
                      activeSection === item.id ? "text-teal-800 hover:text-teal-800 bg-white" : "hover:text-[#3B4154]"
                    )}>
                      <item.icon
                        showLabel={true}
                        isActive={activeSection === item.id}
                        className="group-hover:font-bold"
                      />
                    </div>
                  ) : (
                    <Popover
                      open={openPopover === item.id}
                      onOpenChange={(open) =>
                        setOpenPopover(open ? item.id : null)
                      }
                    >
                      <PopoverTrigger asChild>
                        <div
                          className={cn(
                            "group w-full h-[48px] flex items-center justify-center text-[#3B4154] transition-all hover:bg-white",
                            activeSection === item.id ? "text-teal-800 hover:text-teal-800 bg-white" : "hover:text-[#3B4154]"
                          )}
                          onMouseEnter={() => setOpenPopover(item.id)}
                          onMouseLeave={() => setOpenPopover(null)}
                        >
                          <item.icon
                            isActive={activeSection === item.id}
                          />
                        </div>
                      </PopoverTrigger>
                      <PopoverContent
                        side="right"
                        className="w-auto py-2 px-4 text-lg font-medium"
                        sideOffset={-8}
                        onMouseEnter={() => setOpenPopover(item.id)}
                        onMouseLeave={() => setOpenPopover(null)}
                      >
                        {item.label}
                      </PopoverContent>
                    </Popover>
                  )}
                </div>
              );
            }

            return (
              <div key={item.id}>
                {isExpanded ? (
                  <Popover
                    open={openPopover === item.id}
                    onOpenChange={(open) =>
                      setOpenPopover(open ? item.id : null)
                    }
                  >
                    <PopoverTrigger asChild>
                      <button
                        onMouseEnter={() => setOpenPopover(item.id)}
                        onMouseLeave={() => setOpenPopover(null)}
                        onClick={() => onSectionClick(item.id as NavSection)}
                        className={cn(
                          "group w-full h-[48px] flex items-center px-4 text-left text-[#3B4154] transition-all hover:bg-white",
                          activeSection === item.id ? "text-teal-800 hover:text-teal-800 bg-white" : "hover:text-[#3B4154]"
                        )}
                      >
                        <item.icon
                          className={cn(
                            "w-7 h-7 mr-2",
                            activeSection === item.id && "text-teal-800"
                          )}
                        />
                        <span className="font-semibold text-base group-hover:font-bold">{item.label}</span>
                      </button>
                    </PopoverTrigger>
                    <PopoverContent
                      side="right"
                      className="w-64 p-0 rounded-md shadow-md"
                      sideOffset={-40}
                      onMouseEnter={() => setOpenPopover(item.id)}
                      onMouseLeave={() => setOpenPopover(null)}
                    >
                      {sections[item.id as keyof typeof sections] && (
                        <div className="py-2">
                          <div className="px-4 py-2">
                            <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                              <item.icon className="w-5 h-5" />
                              {sections[item.id as keyof typeof sections].title}
                            </h3>
                          </div>
                          {sections[item.id as keyof typeof sections].items
                            .length > 0 && (
                              <div className="py-1 border-t">
                                {sections[
                                  item.id as keyof typeof sections
                                ].items.map((subItem: any, index: number) => (
                                  <Link
                                    key={index}
                                    href={subItem.href}
                                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                  >
                                    {subItem.label}
                                  </Link>
                                ))}
                              </div>
                            )}
                        </div>
                      )}
                    </PopoverContent>
                  </Popover>
                ) : (
                  <Popover
                    open={openPopover === item.id}
                    onOpenChange={(open) =>
                      setOpenPopover(open ? item.id : null)
                    }
                  >
                    <PopoverTrigger asChild>
                      <button
                        onMouseEnter={() => setOpenPopover(item.id)}
                        onMouseLeave={() => setOpenPopover(null)}
                        onClick={() => onSectionClick(item.id as NavSection)}
                        className={cn(
                          "group w-full h-[48px] flex items-center justify-center text-[#3B4154] transition-all hover:bg-white",
                          activeSection === item.id ? "text-teal-800 hover:text-teal-800 bg-white" : "hover:text-[#3B4154]"
                        )}
                      >
                        <item.icon
                          className={cn(
                            "w-7 h-7",
                            activeSection === item.id && "text-teal-800"
                          )}
                        />
                      </button>
                    </PopoverTrigger>
                    <PopoverContent
                      side="right"
                      className="w-64 p-0 rounded-md shadow-md"
                      sideOffset={-8}
                      onMouseEnter={() => setOpenPopover(item.id)}
                      onMouseLeave={() => setOpenPopover(null)}
                    >
                      {sections[item.id as keyof typeof sections] && (
                        <div className="py-2">
                          <div className="px-4 py-2">
                            <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                              <item.icon className="w-5 h-5" />
                              {sections[item.id as keyof typeof sections].title}
                            </h3>
                          </div>
                          {sections[item.id as keyof typeof sections].items
                            .length > 0 && (
                              <div className="py-1 border-t">
                                {sections[
                                  item.id as keyof typeof sections
                                ].items.map((subItem: any, index: number) => (
                                  <Link
                                    key={index}
                                    href={subItem.href}
                                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                  >
                                    {subItem.label}
                                  </Link>
                                ))}
                              </div>
                            )}
                        </div>
                      )}
                    </PopoverContent>
                  </Popover>
                )}
              </div>
            );
          })}
        </div>

        {/* Intelligence tech by section - only show when expanded */}
        {isExpanded && (
          <div className="p-4 pt-2">
            <p className="text-[11px] text-gray-400 text-center">
              Intelligence tech by{" "}
              <Link
                target="_blank"
                href={"https://sutra.ai"}
                className="text-[#00B2A1]"
              >
                Sutra.AI
              </Link>{" "}
            </p>
          </div>
        )}
      </nav>
    </motion.div>
  );
}
