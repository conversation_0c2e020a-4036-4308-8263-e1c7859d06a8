"use client"

import { useEffect, useRef, useState } from "react"
import { SlimSidebar } from "./slim-sidebar"
import { SecondaryNav } from "./secondary-nav"
import type { NavSection } from "@/types/sidebar"
import { useSelector } from "react-redux"
import { useAppDispatch } from '@/lib/store/hooks'
import { toggleSidebar, setActiveSection as setActiveSectionState } from "@/lib/store/features/sidebar/sidebarSlice"
import { useRouter, usePathname } from "next/navigation"
import { resetFavorites } from "@/lib/store/features/favorites/favoritesSlice"
import { logout } from "@/lib/store/features/user/userSlice"
import AddDataset from "@/components/pages/AddDataset/AddDataset"
import { toggleIntercom } from "@/lib/store/features/intercom/intercomSlice"
import { setActiveLink } from "@/lib/store/features/activeModule/activeModuleSlice"
import { resetValuePillars } from "@/lib/store/features/va/valuePillarsSlice"
import { selectValueAnalyticsHasNoData } from '@/lib/store/features/va/valueAnalyticsSlice'

export function Sidebar() {
  const pathname = usePathname();
  const activeSectionState = useSelector((state: any) => state.sidebar.activeSection)
  const isSidebarOpen = useSelector((state: any) => state.sidebar.isOpen)
  const [activeSection, setActiveSection] = useState<NavSection>(activeSectionState)
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const dialogRef = useRef<HTMLDivElement>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null!);
  const [isOpen, setIsOpen] = useState(false);
  const [title, setTitle] = useState("");
  const [isWorldDataset, setIsWorldDataset] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const valuePillars = useSelector((state: any) => state.valuePillars.pillars);
  const valuePillarsStatus = useSelector((state: any) => state.valuePillars.status);
  const valueAnalyticsHasNoData = useSelector(selectValueAnalyticsHasNoData);

  const dispatch = useAppDispatch();
  const router = useRouter();

  useEffect(() => {
    // Function to check if viewport is mobile
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768); // 768px is a common breakpoint for mobile
    };

    // Check on initial load
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener("resize", checkIfMobile);

    // Cleanup event listener on component unmount
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  const getSectionFromPath = (path: string): NavSection => {
    if (path === '/') return 'home';
    else if (path.startsWith('/data')) return 'data';
    else if (path.startsWith('/explore-opportunities')) return 'opportunities';
    else if (path.startsWith('/projects')) return 'projects';
    else if (path.startsWith('/settings')) return 'settings';
    else if (path.startsWith('/operations/governance')) return 'data';
    else if (path.startsWith('/operations')) return 'projects';
    else if (path.startsWith('/profile')) return 'profile';
    else if (path.startsWith('/apps')) return 'apps';
    else if (path.startsWith('/value')) return 'value';
    return 'home'; // default
  };

  useEffect(() => {
    const newSection = getSectionFromPath(pathname);
    setActiveSection(newSection);
    dispatch(setActiveSectionState(newSection));
    if (newSection === 'profile') dispatch(toggleSidebar(false));
  }, [pathname, dispatch]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const buttonElement = document.querySelector('[data-dropdown-button]');
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonElement &&
        !buttonElement.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
      if (
        dialogRef.current &&
        !dialogRef.current.contains(event.target as Node) &&
        buttonElement &&
        !buttonElement.contains(event.target as Node)
      ) {
        setIsDialogOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    // dispatch(toggleSidebar(isSidebarOpen));
    dispatch(setActiveSectionState(activeSection));
  }, [activeSection, dispatch])

  const handleSectionClick = (section: NavSection) => {
    if (section !== "profile" && section !== "support") {
      if (section === activeSection) {
        dispatch(toggleSidebar(!isSidebarOpen))
      } else {
        setActiveSection(section)
        dispatch(toggleSidebar(true))
      }
    }
    if (section === "home") {
      router.push("/")
      dispatch(setActiveLink("/"))
    } else if (section === "data") {
      router.push("/data")
      dispatch(setActiveLink("/data"))
    } else if (section === "opportunities") {
      router.push("/explore-opportunities")
      dispatch(setActiveLink("/explore-opportunities"))
    } else if (section === "projects") {
      router.push("/projects")
      dispatch(setActiveLink("/projects"))
    } else if (section === "apps") {
      router.push("/apps")
      dispatch(setActiveLink("/apps"))
    } else if (section === "value") {
      router.push("/value");
      dispatch(setActiveLink("/value"));
    } else if (section === "profile") {
      setIsDropdownOpen(!isDropdownOpen);
    } else if (section === "support") {
      dispatch(toggleIntercom())
    }
  }

  const handleLogout = () => {
    localStorage.removeItem("authToken");
    localStorage.removeItem('returnUrl');
    sessionStorage.clear();
    dispatch(resetFavorites());
    dispatch(resetValuePillars());
    dispatch(logout());
    router.push("/login");
  };

  // Don't render anything on mobile
  if (isMobile) {
    return null;
  }

  return (
    <>
      <SlimSidebar 
        activeSection={activeSection} 
        onSectionClick={handleSectionClick} 
        isDropdownOpen={isDropdownOpen} 
        dropdownRef={dropdownRef} 
        handleLogout={handleLogout}
        setIsOpen={setIsOpen}
        setTitle={setTitle}
        setIsWorldDataset={setIsWorldDataset}
      />
      {/* <SecondaryNav activeSection={activeSection} setIsDialogOpen={setIsDialogOpen} isDialogOpen={isDialogOpen} dialogRef={dialogRef} setIsOpen={setIsOpen} setTitle={setTitle} setIsWorldDataset={setIsWorldDataset} disableValueItems={valueAnalyticsHasNoData} /> */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity z-50"
          onClick={() => setIsOpen(false)}
        />
      )}
      <AddDataset
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        title={title}
        isWorldDataset={isWorldDataset}
        className="z-50 relative"
      />
    </>
  )
}

