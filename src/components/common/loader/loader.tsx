// Loader.tsx
import React from "react";

const Loader = () => {
  return (
    <div className="flex justify-center items-center mt-20 h-full">
      <div
        style={{
          width: "40px",
          height: "40px",
          border: "2px solid white",
          borderRadius: "50%",
          animation: "spin 1s linear infinite",
          borderTopColor: "#00B2A1",
        }}
      ></div>

      <style jsx>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
};

export default Loader;
