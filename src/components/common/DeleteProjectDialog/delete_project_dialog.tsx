import React from "react";
import Image from "next/image";
import { useState } from "react";



interface DeleteProjectDialog {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
}

const DeleteProjectDialog: React.FC<DeleteProjectDialog> = ({ isOpen, onClose, onConfirm }) => {
    if (!isOpen) return null;

    const [checkedItems, setCheckedItems] = useState({
        dataLoss: false,
        projectLoss: false,
    });
    const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setCheckedItems({
            ...checkedItems,
            [e.target.name]: e.target.checked,
        });
    };

    const isConfirmed = Object.values(checkedItems).every(Boolean);
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white p-4 w-[450px] ">
                <div className="flex flex-col items-center gap-4">
                    <Image
                        src="/assets/icons/warning_avatar_icon.svg"
                        alt="Warning Icon"
                        width={30}
                        height={30}
                    />
                    <p className="text-[#C0433C] font-medium text-base text-center mb-2">You are about to delete a project!</p>
                </div>
                <div className="mt-4 text-left px-4">
                    <label className="flex items-center gap-2 text-base">
                        <input
                            type="checkbox"
                            name="dataLoss"
                            checked={checkedItems.dataLoss}
                            onChange={handleCheckboxChange}
                        />
                        I will lose all data associated with the project.
                    </label>
                    <label className="flex items-start justify-center gap-2 mt-2 text-base ">
                        <input
                            type="checkbox"
                            name="projectLoss"
                            checked={checkedItems.projectLoss}
                            onChange={handleCheckboxChange}
                            className="mt-1"
                        />
                        I will lose the deployed/operational project associated with the project.
                    </label>
                </div>
                <hr />
                <div className="flex justify-end mt-4 gap-6">
                    <button onClick={onConfirm} disabled={!isConfirmed} className={`text-base ${isConfirmed ? "text-red-600 hover:text-red-800" : "text-gray-400 cursor-not-allowed"
                        }`}>
                        Yes, Delete it.
                    </button>
                    <button onClick={onClose} className="bg-teal-500 text-white px-4 py-2 rounded">
                        No, I want to keep this project.
                    </button>
                </div>
            </div>
        </div>
    );
};

export default DeleteProjectDialog;
