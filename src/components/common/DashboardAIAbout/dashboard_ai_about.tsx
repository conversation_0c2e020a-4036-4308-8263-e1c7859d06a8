"use client";

import React from "react";

type AboutAppModalProps = {
    onClose: () => void;
};

export default function DashboardAIAbout({
    onClose,
}: AboutAppModalProps) {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg max-w-[80vw] w-full">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold text-gray-800">About Dashboard AI</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        ✕
                    </button>
                </div>
                <div className="px-6 py-10 text-gray-800" style={{ maxHeight: "70vh", overflowY: "auto" }}>
                    <div className="text-3xl font-bold mb-6">📊 Dashboard AI</div>

                    <section className="mb-10">
                        <h2 className="text-2xl font-semibold mb-2">Welcome to Dashboard AI!</h2>
                        <p>
                            <strong>Dashboard AI</strong> is an AI-powered, prompt-driven platform that transforms plain language into fully interactive, real-time dashboards. Designed for analysts, product managers, founders, and data-driven teams, it enables rapid visualization of metrics, trends, and KPIs — without needing to write a single line of code or SQL.
                        </p>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">🔍 Core Features</h2>
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-semibold">Prompt-Based Dashboard Generation</h3>
                                <p>
                                    Generate entire dashboards using natural language prompts. The AI interprets your request, selects appropriate chart types, connects data sources, and renders a clean layout instantly.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Smart Chart Selection & Layout</h3>
                                <p>
                                    Automatically chooses the best visualization types (bar, line, funnel, pie, map, gauge, etc.) and adapts layout for readability and storytelling. Features responsive, mobile-friendly design.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">AI Insights & Suggestions</h3>
                                <p>
                                    Dashboard AI doesn't just visualize — it analyzes. Offers real-time suggestions, detects anomalies, growth patterns, and identifies missing key metrics.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Interactive Canvas</h3>
                                <p>
                                    Like Canva but for dashboards. Rearrange charts, resize panels, group elements visually. Customize colors, labels, icons, and themes with preview modes for different layouts.
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">💼 Use Cases</h2>
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-semibold">Founders</h3>
                                <p>
                                    Get a bird's-eye view of product, growth, and revenue metrics.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Marketing Teams</h3>
                                <p>
                                    Visualize campaign performance, ad ROI, and conversion funnels.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Product Managers</h3>
                                <p>
                                    Track feature adoption, user flows, and NPS.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Operations</h3>
                                <p>
                                    Monitor SLAs, support tickets, and resource allocation.
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">🔄 Data Connectivity</h2>
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-semibold">Databases</h3>
                                <p>
                                    PostgreSQL, MySQL, BigQuery, MongoDB
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Spreadsheets & APIs</h3>
                                <p>
                                    Google Sheets, Excel, CSV uploads, REST, GraphQL, and Zapier
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Business Apps</h3>
                                <p>
                                    Stripe, Shopify, HubSpot, Google Analytics, Notion
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">🚀 Why Choose Dashboard AI?</h2>
                        <ul className="list-disc list-inside space-y-2">
                            <li>No-code, insight-first: Skip the data engineering — get straight to decisions</li>
                            <li>Flexible and fast: Build in minutes what used to take hours</li>
                            <li>Intelligent: Go beyond "what happened" and ask "why" — with help from AI</li>
                            <li>Real-time collaboration and version control</li>
                            <li>Seamless integration with popular tools and platforms</li>
                        </ul>
                    </section>

                    <p className="text-lg font-semibold">Start creating intelligent dashboards with AI assistance today!</p>
                </div>
                <div className="mt-6 flex justify-end">
                    <button
                        onClick={onClose}
                        className="bg-[#00B2A1] text-white px-4 py-2 rounded-md hover:bg-[#00A090]"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
} 