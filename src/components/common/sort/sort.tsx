import React, { useEffect, useState } from "react";
import Image from "next/image";
interface SortInterface {
  sortValue: string;
  sortOrder: number;
  setSortValue: React.Dispatch<React.SetStateAction<string>>;
  setSortOrder: React.Dispatch<React.SetStateAction<number>>;
}
const Sort: React.FC<SortInterface> = ({ sortValue, setSortValue, sortOrder, setSortOrder }) => {
  const handleSortChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const [selectedSortBy, selectedSortOrder] = event.target.value.split(",");
    setSortValue(selectedSortBy);
    setSortOrder(Number(selectedSortOrder));
  };
  return (
    <div>
      <span className="flex gap-2 items-center">
        <Image src="/assets/sort_icon.svg" alt="import" width={16} height={16} />
        <p className="text-[#333333] text-sm">Sort by: </p>
        <select
          className="text-gray-800 text-sm outline-none border flex justify-center p-1  rounded-md"
          // defaultValue={""}
          value={sortValue && sortOrder ? `${sortValue},${sortOrder}` : ""}
          onChange={handleSortChange}
        >
          {/* <option value="" className="hover:bg-teal-500 hover:text-white">
            Select
          </option> */}
          <option value="views,1" className="hover:bg-teal-500 hover:text-white">
            Relevance
          </option>
          <option value="title,-1" className="hover:bg-teal-500 hover:text-white">
            Name Ascending
          </option>
          <option value="title,1" className="hover:bg-teal-500 hover:text-white">
            Name Descending
          </option>
          <option value="updatedAt,-1" className="hover:bg-teal-500 hover:text-white">
            Last Modified
          </option>
        </select>
      </span>
    </div>
  );
};

export default Sort;
