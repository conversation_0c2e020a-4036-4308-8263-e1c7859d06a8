"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";

interface Filters {
  tags: string[];
  fileTypes: string[];
  licenses: string[];
}

interface FilterComponentProps {
  filters: Filters; // Object containing filters
  selectedFilters: Filters;
  setSelectedFilters: React.Dispatch<React.SetStateAction<Filters>>; // Function to apply selected filters
}

const Filter: React.FC<FilterComponentProps> = ({ filters, selectedFilters, setSelectedFilters }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isSelected, setIsSelected] = useState(false);
  const [tempFilters, setTempFilters] = useState<Filters>(selectedFilters);

  // Toggle filter selection
  const toggleFilter = (category: keyof Filters, value: string) => {
    setTempFilters((prevFilters) => ({
      ...prevFilters,
      [category]: prevFilters[category].includes(value)
        ? prevFilters[category].filter((item) => item !== value) // Remove if already selected
        : [...prevFilters[category], value], // Add if not selected
    }));
    setIsSelected(true);
  };

  useEffect(() => {
    setTempFilters(selectedFilters);
  }, [selectedFilters]);

  const handleApply = () => {
    setSelectedFilters(tempFilters);
    setIsOpen(false);
  };
  return (
    <div className="font-[Lato]  mt-[1.5px]">
      <span className="flex gap-2" onClick={() => setIsOpen(true)}>
        <Image
          src="/assets/filter_icon.svg"
          alt="import"
          width={15}
          height={15}
        />
        <button className="text-gray-700 text-sm">Filters</button>
      </span>
      {/* Overlay (Optional) */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={() => setIsOpen(false)} />
      )}

      {/* Side Dialogue */}
      <div
        className={`fixed inset-y-0 right-0 z-50 w-1/4 bg-white shadow-lg transform
          ${isOpen ? "translate-x-0" : "translate-x-full"}
          transition-transform duration-300 ease-in-out
          flex flex-col
          overflow-y-auto
        `}
      >
        <div className="flex h-12 justify-between bg-[#3B4154] text-white p-5 items-center">
          <h2 className="text-lg">Filters</h2>
          {/* Close Button */}
          <button onClick={() => setIsOpen(false)}>✖</button>
        </div>
        <div className="p-5">
          {filters &&
            Object.entries(filters).map(([category, options]) => (
              <div key={category} className="flex flex-col gap-2 mt-2 border-b">
                <h3 className="text-md font-semibold text-[#3B4154]">
                  {category !== "fileTypes"
                    ? category.charAt(0).toUpperCase() + category.slice(1).toLowerCase()
                    : "File Types"}
                </h3>
                <div className="flex flex-col gap-2 mb-2">
                  {options.map((filter: any) => (
                    <label key={filter} className="flex items-center space-x-2 cursor-pointer text-[#3B4154] text-sm">
                      <input
                        type="checkbox"
                        checked={tempFilters[category as keyof Filters]?.includes(filter)}
                        onChange={() => toggleFilter(category as keyof Filters, filter)}
                        className="h-4 w-4  appearance-none border border-[#6c757d] rounded-sm cursor-pointer 
                        checked:border-[#00B2A1] checked:bg-white flex items-center justify-center 
                        focus:outline-none focus:ring-0 relative checked:before:content-['✔'] checked:before:text-[#00B2A1] 
                        checked:before:absolute checked:before:left-1/2 checked:before:top-1/2 checked:before:-translate-x-1/2 checked:before:-translate-y-1/2 
                        checked:before:text-[12px] checked:before:font-bold"
                      />
                      <span className="text-[#3B4154] text-sm">{filter}</span>
                    </label>
                  ))}
                </div>
              </div>
            ))}
        </div>
        <div className="flex justify-end gap-2 p-2">
          <button
            className="px-8 py-2 border-2 border-teal-500 text-teal-500 rounded-md"
            onClick={() => setIsOpen(false)}
          >
            Cancel
          </button>
          <button
            className={`px-8 py-2 rounded-md ${isSelected ? "bg-teal-500 text-white" : "bg-[#CED2DE] text-[#888FAA] cursor-not-allowed"
              }`}
            disabled={!isSelected}
            onClick={handleApply}
          >
            Apply
          </button>
        </div>
      </div>
    </div>
  );
};

export default Filter;
