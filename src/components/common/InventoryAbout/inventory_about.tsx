"use client";

import React from "react";

type AboutAppModalProps = {
    onClose: () => void;
};


export default function InventoryAbout({
    onClose,
}: AboutAppModalProps) {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg max-w-[80vw] w-full">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold text-gray-800">About Data to Narrative App</h2>
                    <button
                        onClick={() => onClose()}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        ✕
                    </button>
                </div>
                <div className=" px-6 py-10 text-gray-800" style={{ maxHeight: "70vh", overflowY: "auto" }}>
                    <div className="text-3xl font-bold mb-6">📝 Data to Narrative App → 📊</div>

                    <section className="mb-10">
                        <h2 className="text-2xl font-semibold mb-2">Welcome to Data to Narrative!</h2>
                        <p>
                            <strong>Data to Narrative</strong> is your intelligent data analysis companion that transforms complex datasets into clear, actionable insights through AI-powered analytics and visualization. Upload your data files and instantly receive comprehensive analysis, visual representations, and narrative explanations—no technical expertise required.
                        </p>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">🔍 What Can You Do With This App?</h2>

                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-semibold">Instant Data Analysis</h3>
                                <p>
                                    Upload various file formats (CSV, JSON, XLS, XLSX) and receive immediate AI-generated insights without writing a single line of code or configuring complex analysis parameters.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Smart Narrative Generation</h3>
                                <p>
                                    Transform raw numbers into compelling narratives that explain key trends, anomalies, and insights in plain language that both technical and non-technical stakeholders can understand.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Data Visualization</h3>
                                <p>
                                    Explore your data through automatically generated charts with flexible options to highlight specific columns and relationships, making complex patterns immediately visible.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Trend Detection</h3>
                                <p>
                                    Leverage AI to automatically identify and highlight significant trends, patterns, and outliers in your dataset that might otherwise go unnoticed.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Data Summary & Preview</h3>
                                <p>
                                    Get a comprehensive statistical summary and clean preview of your data structure without opening specialized software or writing queries.
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">💼 Business Value</h2>

                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-semibold">Time Efficiency</h3>
                                <p>
                                    Reduce analysis time from hours to minutes by eliminating manual data exploration and visualization tasks, allowing your team to focus on strategic decision-making.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Democratized Data Insights</h3>
                                <p>
                                    Enable all team members—regardless of technical background—to extract meaningful insights from complex data, breaking down silos between data teams and business units.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Enhanced Decision Making</h3>
                                <p>
                                    Transform data into narratives that provide context and meaning, supporting more informed and confident business decisions based on clear understanding rather than raw numbers.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Communication Advantage</h3>
                                <p>
                                    Easily share insights with stakeholders through ready-made visualizations and narratives that effectively communicate the story behind your data.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Resource Optimization</h3>
                                <p>
                                    Minimize the need for specialized data analyst involvement in routine data exploration tasks, optimizing your workforce allocation and reducing bottlenecks.
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">🚀 How to Use</h2>
                        <ol className="list-decimal list-inside space-y-2">
                            <li><strong>Upload Your Data</strong>: Click the upload button and select your data file (CSV, JSON, XLS, XLSX)</li>
                            <li><strong>Explore Results</strong>: Review the generated narrative, data preview, and summary</li>
                            <li><strong>Visualize Insights</strong>: Select from recommended chart options to visualize specific columns</li>
                            <li><strong>Discover Trends</strong>: Review the automatically detected trends and patterns</li>
                            <li><strong>Share Findings</strong>: Export or share the complete analysis with your team</li>
                        </ol>
                    </section>

                    <p className="text-lg font-semibold">Start transforming your data into actionable narratives today!</p>
                </div>
                <div className="mt-6 flex justify-end">
                    <button
                        onClick={() => onClose()}
                        className="bg-[#00B2A1] text-white px-4 py-2 rounded-md hover:bg-[#00A090]"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
}


