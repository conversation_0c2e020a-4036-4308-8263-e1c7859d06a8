"use client";

import React, { useState, useEffect, Dispatch, SetStateAction } from "react";
import { cn } from "@/lib/utils"; // If you have a classnames utility; otherwise remove

interface FilePreviewProps {
  rowsPerPage: number;
  page: number;
  setPage: Dispatch<SetStateAction<number>>;
  totalRows: number;
  parsedRows: Record<string, any>[];
  headers: string[];
  isFullScreen?: boolean;
}

function getPaginationItems(current: number, total: number): (number | string)[] {
  if (total <= 5) {
    return Array.from({ length: total }, (_, i) => i + 1);
  }

  const pages: (number | string)[] = [];
  pages.push(1);

  let start = Math.max(current - 1, 2);
  let end = Math.min(current + 1, total - 1);

  if (current <= 3) {
    start = 2;
    end = 4;
  }
  if (current >= total - 2) {
    start = total - 3;
    end = total - 1;
  }

  if (start > 2) {
    pages.push("...");
  }

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  if (end < total - 1) {
    pages.push("...");
  }

  pages.push(total);
  return pages;
}

const FilePreview: React.FC<FilePreviewProps> = ({
  rowsPerPage,
  page: currentPage,
  setPage: setCurrentPage,
  totalRows,
  parsedRows,
  headers,
  isFullScreen = true,
}) => {
  // --- Pagination Logic ---
  // const totalRows = parsedRows.length
  const totalPages = Math.ceil(totalRows / rowsPerPage);

  // Slice rows for the current page
  const startIndex = (currentPage - 1) * rowsPerPage + 1;
  const endIndex = Math.min(startIndex + rowsPerPage - 1, totalRows);

  // Handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const handlePrev = () => {
    if (currentPage > 1) setCurrentPage((prev) => prev - 1);
  };
  const handleNext = () => {
    if (currentPage < totalPages) setCurrentPage((prev) => prev + 1);
  };
  const paginationItems = getPaginationItems(currentPage, totalPages);

  return (
    <div className="border border-gray-300 rounded-md overflow-y-auto">
      {headers.length > 0 ? (
        <>
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                {headers.map((header) => (
                  <th key={header} className="px-4 py-2 text-left text-xs font-bold tracking-wider">
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {parsedRows.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {headers.map((header) => (
                    <td key={header} className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                      {row[header] ?? ""}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>

          {/* Pagination UI */}
          {!isFullScreen && totalPages > 1 && (
            <div className="flex items-center justify-center gap-3 p-4">
              <p className="mr-auto text-gray-500">
                Showing {startIndex} - {endIndex} of {totalRows} results
              </p>
              {/* Back button */}
              <button
                onClick={handlePrev}
                disabled={currentPage === 1}
                className={cn("text-gray-500 disabled:cursor-not-allowed disabled:text-gray-300")}
              >
                Back
              </button>

              {/* Page numbers */}
              {paginationItems.map((item, idx) =>
                typeof item === "number" ? (
                  <button
                    key={item}
                    onClick={() => handlePageChange(item)}
                    className={cn(
                      "h-8 w-8 rounded-md text-sm font-medium flex items-center justify-center",
                      item === currentPage ? "bg-teal-500 text-white" : "text-gray-500 hover:text-gray-700"
                    )}
                  >
                    {item}
                  </button>
                ) : (
                  <span key={`ellipsis-${idx}`} className="text-gray-500">
                    {item}
                  </span>
                )
              )}

              {/* Next button */}
              <button
                onClick={handleNext}
                disabled={currentPage === totalPages}
                className={cn("text-teal-500 disabled:cursor-not-allowed disabled:text-gray-300")}
              >
                Next
              </button>
            </div>
          )}
        </>
      ) : (
        <p className="text-center text-gray-500 py-4">No data available for preview.</p>
      )}
    </div>
  );
};

export default FilePreview;
