"use client";

import React from "react";

type AboutAppModalProps = {
    onClose: () => void;
};

export default function ResumeAnalyzerAIAbout({
    onClose,
}: AboutAppModalProps) {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg max-w-[80vw] w-full">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold text-gray-800">About Resume Analyzer AI</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        ✕
                    </button>
                </div>
                <div className="px-6 py-10 text-gray-800" style={{ maxHeight: "70vh", overflowY: "auto" }}>
                    <div className="text-3xl font-bold mb-6">📄 Resume Analyzer AI</div>

                    <section className="mb-10">
                        <h2 className="text-2xl font-semibold mb-2">Welcome to Resume Analyzer AI!</h2>
                        <p>
                            <strong>Resume Analyzer AI</strong> is an intelligent, end-to-end resume screening platform designed for recruiters, HR teams, and hiring managers. It combines AI-powered resume parsing, candidate scoring, and automated communication tools to streamline the hiring process.
                        </p>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">🔍 Core Features</h2>
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-semibold">Flexible Resume Import</h3>
                                <p>
                                    Upload hundreds of resumes (PDF, DOCX, ZIP) in bulk or import individual resumes. Integrate with job boards, career pages, or email addresses for automatic processing.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">AI-Powered Resume Parsing & Scoring</h3>
                                <p>
                                    Extract key details and match resumes against job descriptions using semantic analysis. Generate comprehensive scores based on role fit, skill match, experience relevance, and more.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Auto-Generated Email Communication</h3>
                                <p>
                                    Generate personalized email drafts for both shortlisted and rejected candidates. Integrate with Gmail, Outlook, or SMTP for seamless sending with optional review before delivery.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Visual Hiring Dashboard</h3>
                                <p>
                                    Track your hiring pipeline with an interactive dashboard showing shortlisted, rejected, under review, and emailed candidates. Filter and search by various criteria with export capabilities.
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">💼 Use Cases</h2>
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-semibold">Recruiters</h3>
                                <p>
                                    Save time screening resumes with objective AI ranking and automated communication.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">HR Managers</h3>
                                <p>
                                    Get a clear overview of hiring pipelines across departments with detailed analytics.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Startups</h3>
                                <p>
                                    Quickly process high-volume applicants without needing a full ATS system.
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Agencies</h3>
                                <p>
                                    Manage multiple clients' hiring pipelines from one unified dashboard.
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">✨ Key Features</h2>
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-semibold">Bulk & Single Resume Upload</h3>
                                <p>
                                    Handle high-volume screening or ad-hoc reviews effortlessly
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">AI Scoring Engine</h3>
                                <p>
                                    Score each resume based on role-specific requirements
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">Auto Email Drafts</h3>
                                <p>
                                    Instantly generate and send polite rejection or shortlist notifications
                                </p>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold">ATS Integration</h3>
                                <p>
                                    Export results or integrate with major ATS systems (Greenhouse, Lever, etc.)
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className="mb-10">
                        <h2 className="text-xl font-semibold mb-4">🚀 Why Choose Resume Analyzer AI?</h2>
                        <ul className="list-disc list-inside space-y-2">
                            <li>Reduce manual screening time by up to 80%</li>
                            <li>Ensure fair, bias-minimized evaluation with consistent AI scoring</li>
                            <li>Communicate faster with professional, AI-written messages</li>
                            <li>Gain full visibility across your recruitment funnel — from CV to contact</li>
                            <li>Seamless integration with existing HR tools and workflows</li>
                        </ul>
                    </section>

                    <p className="text-lg font-semibold">Start streamlining your hiring process with AI-powered resume analysis today!</p>
                </div>
                <div className="mt-6 flex justify-end">
                    <button
                        onClick={onClose}
                        className="bg-[#00B2A1] text-white px-4 py-2 rounded-md hover:bg-[#00A090]"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
} 