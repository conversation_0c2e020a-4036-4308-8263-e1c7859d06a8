"use client";

import Link from "next/link";
import { ReactNode } from "react";
import { TbArrowUpRight } from "react-icons/tb";
import { FiArrowRight } from "react-icons/fi";

export interface AppCardProps {
  id: string;
  name: string;
  description: string;
  icon: ReactNode;
  link?: string;
  onClick?: () => void;
  alert?: {
    message: string;
    type: "info" | "warning" | "success" | "error";
    timestamp: string;
  };
}

export function AppCard({
  id,
  name,
  description,
  icon,
  link,
  onClick,
  alert,
}: AppCardProps) {
  const cardContent = (
    <div className="p-8 h-full flex items-start hover:bg-gray-50 group cursor-pointer">
      <div className="w-10 h-10 rounded-md bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0">
        {icon}
      </div>
      <div className="flex-1 min-w-0">
        <h3 className="font-medium text-[#1E293B]">{name}</h3>
        <p className="text-xs text-gray-500 truncate">{description}</p>
        <div className="flex items-center gap-1 text-xs text-gray-500 mt-1 transition-all duration-200 opacity-60 group-hover:opacity-100 group-hover:font-medium group-hover:translate-x-1 cursor-pointer">
          <span>Go to app</span>
          <FiArrowRight className="w-4 h-4 ml-1 transition-transform duration-200 group-hover:translate-x-1" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex flex-col h-full border border-gray-200 rounded-lg overflow-hidden transition-all hover:shadow-md">
      {/* Top section with app info and link or click */}
      {onClick ? (
        <div onClick={onClick} role="button" tabIndex={0} style={{ outline: "none" }}>{cardContent}</div>
      ) : link ? (
        <Link href={link} className="block">{cardContent}</Link>
      ) : (
        cardContent
      )}

      {/* Bottom section with alert or action */}
      {alert && (
        <div className={`p-3 mt-8 border-t border-gray-200 `}>
          <div className="flex items-start">
            {getAlertIcon(alert.type)}
            <div className="ml-2">
              <p className="text-sm">{alert.message}</p>
              <p className="text-xs text-gray-500">{alert.timestamp}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Function to get the alert icon based on the type
function getAlertIcon(type: string): ReactNode {
  switch (type) {
    case "info":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          className="icon icon-tabler icons-tabler-outline icon-tabler-info-square-rounded"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none" />
          <path d="M12 9h.01" />
          <path d="M11 12h1v4h1" />
          <path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z" />
        </svg>
      );
    case "warning":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          className="icon icon-tabler icons-tabler-outline icon-tabler-alert-square-rounded"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none" />
          <path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z" />
          <path d="M12 8v4" />
          <path d="M12 16h.01" />
        </svg>
      );
    case "success":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          className="icon icon-tabler icons-tabler-outline icon-tabler-square-rounded-check"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none" />
          <path d="M9 12l2 2l4 -4" />
          <path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z" />
        </svg>
      );
    case "error":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 text-red-500"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
            clipRule="evenodd"
          />
        </svg>
      );
    default:
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 text-gray-400"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
            clipRule="evenodd"
          />
        </svg>
      );
  }
}
