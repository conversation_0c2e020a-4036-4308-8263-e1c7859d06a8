"use client";

import { TbStar } from "react-icons/tb";
import { useEffect, useState } from "react";
import { getWhatsNew, WhatsNewItem } from "@/service/homepageService";
import { useNotifications, useNotificationCount } from "@/service/hooks/useNotification";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import { X, Bell, MessageSquare, Rocket } from "lucide-react";
import { Button } from "@/components/ui/button/button";
import { setProjectActiveTab } from "@/lib/store/features/project/projectTabSlice";
import { setActiveManageTab } from "@/lib/store/features/project/projectManageSlice";

export function NewInPlatform() {
  const [whatsNew, setWhatsNew] = useState<WhatsNewItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);
  const {
    notifications,
    loading: notificationLoading,
    error,
    markAsRead,
    markAllAsRead,
    hasMore,
    loadMore,
  } = useNotifications();
  const { count, setCount } = useNotificationCount();
  const [groupedNotifications, setGroupedNotifications] = useState<Record<string, any[]>>({});
  const dispatch = useDispatch();
  const router = useRouter();

  useEffect(() => {
    const fetchWhatsNew = async () => {
      try {
        const whatsNewResponse = await getWhatsNew();
        setWhatsNew(whatsNewResponse ? whatsNewResponse.slice(0,3) : []);
      } catch (error) {
        console.error('Error fetching pillar data:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchWhatsNew();
  }, []);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"; // Prevent background scrolling
    } else {
      document.body.style.overflow = ""; // Restore background scrolling
    }
    return () => {
      document.body.style.overflow = ""; // Cleanup on unmount
    };
  }, [isOpen]);

  // Group notifications by date
  useEffect(() => {
    if (!notifications) return;

    const grouped: Record<string, any[]> = {};

    notifications.forEach((notification) => {
      const date = new Date(notification.createdAt);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      let dateKey;

      if (date.toDateString() === today.toDateString()) {
        dateKey = "Today";
      } else if (date.toDateString() === yesterday.toDateString()) {
        dateKey = "Yesterday";
      } else {
        dateKey = date.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
          year: "numeric",
        });
      }

      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }

      grouped[dateKey].push(notification);
    });

    setGroupedNotifications(grouped);
  }, [notifications]);

  const handleNotificationClick = (
    notifyId: string,
    type: string,
    projectId: string
  ) => {
    setIsOpen(false);
    markAsRead(notifyId);
    switch (type) {
      case "collaborator_added":
        router.push(`/projects/dashboard/${projectId}`);
        dispatch(setProjectActiveTab("Manage"));
        dispatch(setActiveManageTab("Collaborators"));
        return;
      case "project_status_changed":
        router.push(`/projects/dashboard/${projectId}`);
        dispatch(setProjectActiveTab("Overview"));
        dispatch(setActiveManageTab("General"));
        return;
      case "system_maintenance":
        dispatch(setProjectActiveTab("Overview"));
        dispatch(setActiveManageTab("General"));
        return;
      case "comment_added":
        router.push(`/projects/dashboard/${projectId}`);
        dispatch(setProjectActiveTab("Overview"));
        dispatch(setActiveManageTab("General"));
        return;
      case "project_deployed":
        router.push(`/apps/deployed-projects`);
        dispatch(setProjectActiveTab("Overview"));
        dispatch(setActiveManageTab("General"));
        return;
      default:
        if (projectId) router.push(`/projects/dashboard/${projectId}`);
        dispatch(setProjectActiveTab("Overview"));
        dispatch(setActiveManageTab("General"));
        return;
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "collaborator_added":
        return (
          <img src="/assets/Group_7093.svg" alt="user" className="w-8 h-8" />
        );
      case "project_status_changed":
        return (
          <img src="/assets/Group_7099.svg" alt="user" className="w-8 h-8" />
        );
      case "system_maintenance":
        return (
          <img src="/assets/Group_7101.svg" alt="user" className="w-8 h-8" />
        );
      case "comment_added":
        return <MessageSquare className="h-6 w-6 text-gray-500" />;
      case "project_deployed":
        return <Rocket className="h-6 w-6 text-gray-500" />;
      default:
        return <Bell className="h-6 w-6 text-gray-500" />;
    }
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    if (scrollHeight - scrollTop <= clientHeight * 1.5) {
      if (hasMore && !notificationLoading) {
        loadMore();
      }
    }
  };
  function ItemCardShimmer() {
    return (
      <div className="block p-4 border border-gray-200 rounded-lg animate-pulse">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-gray-200 rounded" />
            <div className="h-4 w-32 bg-gray-200 rounded" />
          </div>
          <div className="h-3 w-20 bg-gray-200 rounded" />
        </div>
        <div className="h-5 bg-gray-200 rounded w-3/4 mb-2" />
        <div className="space-y-1">
          <div className="h-3 bg-gray-200 rounded w-full" />
          <div className="h-3 bg-gray-200 rounded w-[90%]" />
          <div className="h-3 bg-gray-200 rounded w-[80%]" />
        </div>
      </div>
    );
  }

  return (
    <div className="border border-gray-200 rounded-xl bg-white shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <TbStar className="text-2xl" />
          <h2 className="text-xl font-semibold text-[#111827]">
            What&apos;s New?
          </h2>
        </div>
         <div
          className="text-sm hover:underline font-medium cursor-pointer"
          aria-label="View all apps"
          onClick={() => setIsOpen(true)}
        >
          View All ↗︎
        </div>
      </div>

      {/* Notification Panel */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity z-50"
          onClick={() => setIsOpen(false)}
        />
      )}
      <div
        className={`font-[Lato] z-50 fixed top-0 right-0 h-screen w-[40%] bg-white shadow-lg transform ${
          isOpen ? "translate-x-0" : "translate-x-full"
        } transition-transform duration-300 ease-in-out flex flex-col pb-5`}
      >
        <div className="sticky top-0 z-10 flex flex-col">
          <div className="flex items-center h-12 justify-between w-full px-4 bg-[#3B4154]">
            <h2 className="text-lg font-medium text-white">Your Notifications</h2>
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-white/20"
              onClick={() => setIsOpen(false)}
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
          <button
            className="text-teal-500 text-sm flex justify-end px-5 pt-3"
            onClick={() => {
              markAllAsRead(setCount);
            }}
          >
            Mark all as Read
          </button>
        </div>

        <div className="flex-1 overflow-y-auto mt-4 pb-4" onScroll={handleScroll}>
          {notificationLoading && notifications.length === 0 ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : error ? (
            <div className="text-red-500 p-4 text-center">Error loading notifications</div>
          ) : notifications.length === 0 ? (
            <div className="text-gray-500 p-4 text-center">No notifications yet</div>
          ) : (
            Object.entries(groupedNotifications).map(([date, dateNotifications], index) => (
              <div key={index} className="mb-6">
                <h3 className="text-sm font-semibold bg-[#F4F5F6] text-[#3B4154] mb-2 px-4 py-2">
                  {date}
                </h3>

                {dateNotifications.map((notification, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between px-5 py-3 mb-2 rounded-lg hover:bg-gray-50 relative cursor-pointer"
                    onClick={() =>
                      handleNotificationClick(
                        notification._id,
                        notification.type,
                        notification?.relatedId
                      )
                    }
                  >
                    <div className="mr-3">{getNotificationIcon(notification.type)}</div>

                    <div className="flex-1">
                      <div
                        dangerouslySetInnerHTML={{
                          __html: notification.message.replace(
                            /"([^"]+)"/g,
                            '<span class="text-teal-500">"$1"</span>'
                          ),
                        }}
                      />
                      <p className="text-sm text-gray-500 mt-1">
                        {new Date(notification.createdAt).toLocaleDateString("en-GB", {
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                        })}
                      </p>
                    </div>

                    {!notification.isRead && (
                      <div className="h-2 w-2 rounded-full bg-teal-500"></div>
                    )}
                  </div>
                ))}
              </div>
            ))
          )}
          {notificationLoading && notifications.length > 0 && (
            <div className="flex justify-center items-center h-16">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
            </div>
          )}

          {!notificationLoading && !hasMore && notifications.length > 0 && (
            <div className="text-gray-500 text-sm text-center p-4">No more notifications</div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {loading ? (
          Array.from({ length: 3 }).map((_, idx) => <ItemCardShimmer key={idx} />)
        ) : (
          whatsNew.map((item) => {
            return (
              <div key={item._id}>
                <div className="block p-4 border border-gray-200 rounded-lg transition-all">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      {getItemIcon(item.category)}
                      <span className="text-sm font-medium ml-2 text-[#4b5563]">{item.title}</span>
                    </div>
                    <span className="text-xs text-[#6b7280]">
                      {new Date(item.updatedAt).toLocaleDateString("en-GB", {
                        day: "2-digit",
                        month: "short",
                        year: "numeric",
                      })}
                    </span>
                  </div>
                  <h3 className="font-medium text-[#111827] mb-1">{item.header}</h3>
                  <p className="text-sm text-[#4b5563] line-clamp-3">{item.description}</p>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
}

function getItemTypeLabel(type: string): string {
  switch (type) {
    case "opportunity":
      return "New Opportunity";
    case "project":
      return "Project Finished";
    case "feature":
      return "New Feature";
    default:
      return "New Item";
  }
}

function getItemIcon(type: string) {
  switch (type) {
    case "Opportunity":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 text-[#00B2A1]"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
            clipRule="evenodd"
          />
        </svg>
      );
    case "Notifications":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 text-[#00B2A1]"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
            clipRule="evenodd"
          />
        </svg>
      );
    case "Feature":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 text-[#00B2A1]"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z" />
        </svg>
      );
    default:
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 text-gray-400"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
            clipRule="evenodd"
          />
        </svg>
      );
  }
}
