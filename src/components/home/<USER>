"use client";

import React, { useEffect } from "react";
import { ValuePillarsOverall } from "@/components/pages/ValueAnalytics/value-pillars-overall";
import NoDataPage from "@/components/pages/ValueAnalytics/no-data-page";
import { WelcomeBar } from "./WelcomeBar";
import { ApplicationsSection } from "./ApplicationsSection";
import { NewInPlatform } from "./NewInPlatform";
import { useAppDispatch, useAppSelector } from "@/lib/store/hooks";
import { fetchAllValueAnalytics } from "@/lib/store/features/va/valueAnalyticsSlice";
import Loader from "@/components/common/loader/loader";
import { useDispatch, useSelector } from "react-redux";
import type { RootState, AppDispatch } from "@/lib/store";
import Link from "next/link";
import { TbCurrentLocation, TbTrendingUp, TbU<PERSON>s, Tb<PERSON>ig<PERSON><PERSON>, Tb<PERSON>urrencyDollar, TbThumbUp, TbDiscount, TbAdjustments } from "react-icons/tb";

export function HomePage() {
  const dispatch = useDispatch<AppDispatch>();
  const { projects, status } = useSelector((state: RootState) => state.valueAnalytics);
  const valueAnalyticsStatus = useSelector((state: RootState) => state.valueAnalytics.status);

  const pillarConfig = {
    "Operational Efficiency": {
      description: "Track how AI improves your operational processes and workflows",
      valueType: "Soft $$",
      valueTypeClass: "bg-purple-100 text-purple-700",
      icon: <TbCurrentLocation className="w-7 h-7 text-gray-700" />
    },
    "Revenue Growth": {
      description: "Measure increased revenue from AI-enhanced products and services",
      valueType: "Hard $$",
      valueTypeClass: "bg-teal-100 text-teal-700",
      icon: <TbTrendingUp className="w-7 h-7 text-gray-700" />
    },
    "Customer Experience": {
      description: "Analyze improvements in customer satisfaction and engagement",
      valueType: "Soft $$",
      valueTypeClass: "bg-purple-100 text-purple-700",
      icon: <TbUsers className="w-7 h-7 text-gray-700" />
    },
    "Cost Reduction": {
      description: "Quantify cost savings achieved through AI implementation",
      valueType: "Hard $$",
      valueTypeClass: "bg-teal-100 text-teal-700",
      icon: <TbPigMoney className="w-7 h-7 text-gray-700" />
    }
  };

  const allPillarNames = Array.from(
    new Set(
      projects.flatMap(project => project.valuePillars.map((pillar: import("@/types/value-analytics").ValuePillar) => pillar.name))
    )
  );

  function ValuePillarShimmer() {
    return (
      <div className="animate-pulse bg-white border border-gray-200 rounded-xl p-5 flex flex-col h-full justify-between shadow-sm">
        <div className="flex items-center mb-3">
          <div className="w-8 h-8 bg-gray-200 rounded-md" />
        </div>
        <div className="h-5 bg-gray-200 rounded w-3/4 mb-2" />
        <div className="h-4 bg-gray-200 rounded w-full mb-4" />
        <div className="flex items-center gap-2">
          <div className="h-6 w-20 bg-gray-200 rounded" />
          <div className="h-5 w-12 bg-gray-200 rounded" />
        </div>
      </div>
    );
  }

  useEffect(() => {
    if (valueAnalyticsStatus === "idle" || valueAnalyticsStatus === "failed") {
      dispatch(fetchAllValueAnalytics());
    }
  }, [dispatch, valueAnalyticsStatus]);

  return (
    <div className="flex flex-col h-full gap-8 bg-[#F6F8FA]">
      {/* Welcome Bar */}
      <div>
        <WelcomeBar />
      </div>

      {/* Value Analytics Section */}
      <div className="mx-8">
        <div className="bg-white border border-gray-200 rounded-xl shadow-sm p-6">
          <div className="flex flex-col justify-between items-start mb-6">
            <div className="text-2xl font-bold pb-1 text-gray-900">Value Analytics Pillars</div>
            <div className="text-base text-gray-500">Your AI transformation journey starts here</div>
          </div>
          {/* Value Pillars Section */}
          {status === 'loading' ? (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
              {Array.from({ length: 4 }).map((_, idx) => (
                <ValuePillarShimmer key={idx} />
              ))}
            </div>
          ) :
            (projects.length === 0 ? (<div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
              <div className="border border-dashed rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <TbAdjustments size={24} className="text-blue-500 bg-blue-100 rounded-full p-1" />
                  <div className="font-semibold">Operational Efficiency</div>
                </div>
                <div className="text-sm text-gray-500 mt-1">Track how AI improves your operational processes and workflows</div>
              </div>

              <div className="border border-dashed rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <TbCurrencyDollar size={24} className="text-green-500 bg-green-100 rounded-full p-1" />
                  <div className="font-semibold">Revenue Growth</div>
                </div>
                <div className="text-sm text-gray-500 mt-1">Measure increased revenue from AI-enhanced products and services</div>
              </div>

              <div className="border border-dashed rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <TbThumbUp size={24} className="text-purple-500 bg-purple-100 rounded-full p-1" />
                  <div className="font-semibold">Customer Experience</div>
                </div>
                <div className="text-sm text-gray-500 mt-1">Analyze improvements in customer satisfaction and engagement</div>
              </div>

              <div className="border border-dashed rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <TbDiscount size={24} className="text-yellow-500 bg-yellow-100 rounded-full p-1" />
                  <div className="font-semibold">Cost Reduction</div>
                </div>
                <div className="text-sm text-gray-500 mt-1">Quantify cost savings achieved through AI implementation</div>
              </div>
            </div>) : (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {["Operational Efficiency", "Customer Experience", "Revenue Growth", "Cost Reduction"].map((pillarName) => {
                  const config = pillarConfig[pillarName as keyof typeof pillarConfig];
                  let totalValue = 0;
                  if (allPillarNames.includes(pillarName)) {
                    const pillarData = projects.flatMap(project =>
                      project.valuePillars.filter((p: import("@/types/value-analytics").ValuePillar) => p.name === pillarName)
                    );
                    totalValue = pillarData.reduce(
                      (sum, p) => sum + p.benefits.reduce(
                        (bSum: number, b: import("@/types/value-analytics").Benefit) =>
                          bSum + b.metrics.reduce((mSum, m) => mSum + (m.dollarValue || 0), 0),
                        0
                      ),
                      0
                    );
                  }
                  const href = `/value/${pillarName.toLowerCase().replace(/ /g, "-")}`;
                  return (
                    <Link href={href} key={pillarName}>
                      <div className="bg-white border border-gray-200 rounded-xl p-5 flex flex-col h-full justify-between shadow-sm cursor-pointer hover:shadow-md transition">
                        <div className="flex items-center mb-3">{config.icon}</div>
                        <div className="font-semibold text-lg text-gray-900 mb-1">{pillarName}</div>
                        <div className="text-sm text-gray-500 mb-4">{config.description}</div>
                        {totalValue > 0 && (
                          <div className="flex items-center mb-2">
                            <span className="text-xl font-bold text-gray-900">
                              ${Math.round(totalValue).toLocaleString()}
                            </span>
                            <span className={`ml-2 px-2 py-1 rounded text-xs font-semibold ${config.valueTypeClass}`}>{config.valueType}</span>
                          </div>
                        )}
                      </div>
                    </Link>
                  );
                })}
              </div>
            ))
          }
        </div>
      </div>

      {/* Applications Section */}
      <div className="mx-8">
        <ApplicationsSection />
      </div>

      {/* New in Platform Section */}
      <div className="mx-8 mb-8 pb-8">
        <NewInPlatform />
      </div>
    </div>
  );
}
