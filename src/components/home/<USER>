"use client";

import { TbRocket } from "react-icons/tb";
import { AppCard, AppCardProps } from "./AppCard";
import Link from "next/link";
import { useEffect, useState } from "react";
import { getRecentProjects } from "@/service/agentEOService";
import { useRouter } from "next/navigation";

// Shimmer skeleton card
function ShimmerCard() {
  return (
    <div className="animate-pulse rounded-lg border border-gray-200 p-4">
      <div className="flex items-center gap-4">
        <div className="w-10 h-10 bg-gray-200 rounded-md" />
        <div className="flex-1">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
          <div className="h-3 bg-gray-200 rounded w-1/2" />
        </div>
      </div>
      <div className="mt-4 h-3 bg-gray-200 rounded w-full mb-2" />
      <div className="h-3 bg-gray-200 rounded w-5/6" />
    </div>
  );
}

export function ApplicationsSection() {
  const [apps, setApps] = useState<AppCardProps[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    async function fetchApps() {
      setLoading(true);
      setError(null);
      try {
        const response = await getRecentProjects(1, 10);
        const projects = (response as any).projects || [];
        console.log("Recent Apps API projects:", projects);
        if (projects.length > 0) {
          const mapped = projects.slice(0, 3).map((item: any) => {
            let iconSrc = "/assets/app-icon.svg";
            if (item.isAiBox) {
              iconSrc = "/assets/ai_app_icon.svg";
            } else if (item.appType === "Dashboard") {
              iconSrc = "/assets/dashboard-icon.svg";
            } else if (item.appType === "API") {
              iconSrc = "/assets/api-icon.svg";
            }
            return {
              id: item._id,
              name: item.name,
              description: item.description || "",
              icon: (
                <img
                  src={iconSrc}
                  alt={item.appType || "App"}
                  className="w-10 h-10 rounded-md bg-gray-100"
                />
              ),
              onClick: () => {
                router.push(
                  `/apps/deployed-projects/${item._id}?name=${encodeURIComponent(
                    item.name
                  )}&isAiBox=${item.isAiBox ?? false}&appUrl=${encodeURIComponent(
                    item.appUrl || item.app_url || ""
                  )}`
                );
              },
            };
          });
          setApps(mapped);
        } else {
          setApps([]);
        }
      } catch (e: any) {
        setError("Failed to load recent apps");
        setApps([]);
      } finally {
        setLoading(false);
      }
    }
    fetchApps();
  }, [router]);

  return (
    <div className="border border-gray-200 rounded-xl bg-white shadow-sm p-6">
      <div className="flex justify-between items-center mb-6 h-full">
        <div className="flex items-center gap-2">
          <TbRocket className="text-2xl" />
          <h2 className="text-xl font-semibold text-[#111827]">Apps</h2>
        </div>
        <Link
          href="/apps"
          className="text-sm hover:underline font-medium"
          aria-label="View all apps"
        >
          View All ↗︎
        </Link>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {loading ? (
          Array.from({ length: 3 }).map((_, idx) => <ShimmerCard key={idx} />)
        ) : error ? (
          <div className="col-span-3 text-center text-red-500">{error}</div>
        ) : apps.length === 0 ? (
          <div className="col-span-3 text-center text-gray-400">
            No recent apps found.
          </div>
        ) : (
          apps.map((app) => (
            <AppCard
              key={app.id}
              id={app.id}
              name={app.name}
              description={app.description}
              icon={app.icon}
              onClick={app.onClick}
            />
          ))
        )}
      </div>
    </div>
  );
}
