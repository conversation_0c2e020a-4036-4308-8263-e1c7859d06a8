"use client";
import { useState, useEffect } from "react";
import { http } from "@/service/methods";
import { usePathname } from 'next/navigation';

export type ProgressPhase =
  | "data"
  | "opportunities"
  | "projects"
  | "apps"
  | "value";
export type PhaseStatus = "not-started" | "in-progress" | "completed";

export function ProgressPhase() {
  const [selectedPhase, setSelectedPhase] = useState<ProgressPhase>("opportunities");
  const [hoveredPhase, setHoveredPhase] = useState<ProgressPhase | null>(null);
  const [phaseData, setPhaseData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const pathname = usePathname();

  useEffect(() => {
    async function fetchPhaseData() {
      setLoading(true);
      setError(null);
      try {
        const res = await http.get<any>("/value-analytics/company-connections-opportunities");
        setPhaseData(res.data);
      } catch (err: any) {
        setError("Failed to load journey data");
      } finally {
        setLoading(false);
      }
    }
    fetchPhaseData();
  }, [pathname]);

  function getPhaseStatus(phase: ProgressPhase) {
    if (!phaseData) return { status: "not-started", statusText: "" };
    let value = 0;
    switch (phase) {
      case "data":
        value = phaseData.dataConnections;
        break;
      case "opportunities":
        value = phaseData.totalOpportunities;
        break;
      case "projects":
        value = phaseData.projects;
        break;
      case "apps":
        value = phaseData.apps;
        break;
      case "value":
        value = phaseData.totalMetrics;
        break;
      default:
        value = 0;
    }
    if (typeof value === "number" && value > 0) {
      switch (phase) {
        case "data":
          return { status: "completed", statusText: `${value} data sources connected` };
        case "opportunities":
          return { status: "completed", statusText: `${value} opportunities identified` };
        case "projects":
          return { status: "completed", statusText: `${value} projects created` };
        case "apps":
          return { status: "completed", statusText: `${value} apps deployed` };
        case "value":
          return { status: "completed", statusText: `${value} metrics tracked` };
        default:
          return { status: "completed", statusText: `${value}` };
      }
    } else {
      switch (phase) {
        case "data":
          return { status: "not-started", statusText: `No data sources connected` };
        case "opportunities":
          return { status: "not-started", statusText: `No opportunities identified` };
        case "projects":
          return { status: "not-started", statusText: `Ready to create projects` };
        case "apps":
          return { status: "not-started", statusText: `App builder available` };
        case "value":
          return { status: "not-started", statusText: `Value tracking pending` };
        default:
          return { status: "not-started", statusText: "Not started" };
      }
    }
  }

  const phases = [
    {
      id: "data" as ProgressPhase,
      title: "Data",
      description: "Connect and consolidate your data sources to establish a single source of truth. Import financial data, operational metrics, and other business information to build a comprehensive view of your organization.",
      ...getPhaseStatus("data"),
      metrics: {
        savings: "15-25% cost reduction",
        efficiency: "60% faster reporting",
        timeframe: "2-4 weeks"
      }
    },
    {
      id: "opportunities" as ProgressPhase,
      title: "Opportunities",
      description: "Leverage AI-powered analytics to discover hidden patterns and potential improvements in your data. Identify cost savings, revenue opportunities, and operational efficiencies across your business.",
      ...getPhaseStatus("opportunities"),
      metrics: {
        savings: "10-40% potential savings",
        efficiency: "3x faster discovery",
        timeframe: "1-2 weeks"
      }
    },
    {
      id: "projects" as ProgressPhase,
      title: "Projects",
      description: "Transform identified opportunities into actionable projects with clear timelines and deliverables. Track progress, manage resources, and ensure successful execution of your strategic initiatives.",
      ...getPhaseStatus("projects"),
      metrics: {
        savings: "20-30% project efficiency",
        efficiency: "50% faster delivery",
        timeframe: "4-12 weeks"
      }
    },
    {
      id: "apps" as ProgressPhase,
      title: "Apps",
      description: "Build custom applications tailored to your specific business needs. Create dashboards, reports, and tools that streamline operations and enhance decision-making capabilities.",
      ...getPhaseStatus("apps"),
      metrics: {
        savings: "70% dev cost reduction",
        efficiency: "5x faster development",
        timeframe: "2-8 weeks"
      }
    },
    {
      id: "value" as ProgressPhase,
      title: "Value",
      description: "Measure and track the impact of your initiatives. Monitor ROI, cost savings, and business improvements to demonstrate tangible value and guide future strategic decisions.",
      ...getPhaseStatus("value"),
      metrics: {
        savings: "Track full ROI impact",
        efficiency: "Real-time monitoring",
        timeframe: "Ongoing"
      }
    }
  ];

  const hoveredPhaseData = phases.find(p => p.id === hoveredPhase);

  return (
    <div className="relative w-full">
      <div className="flex flex-col items-start w-full">
        <div className="text-xl font-semibold text-[#111827] mb-2 text-left">
          Your Data to Value Journey
        </div>
        {/* Timeline */}
        <div className="flex items-end justify-start w-full relative" style={{ minHeight: 56 }}>
          {/* Timeline line */}
          <div className="absolute left-0 right-8 top-1/2 h-0.5 bg-gray-200 -translate-y-1/2"></div>
          {phases.map((phase) => (
            <div
              key={phase.id}
              className="relative flex flex-col items-center w-full"
              style={{ zIndex: 10 }}
              onMouseEnter={() => setHoveredPhase(phase.id)}
              onMouseLeave={() => setHoveredPhase(null)}
            >
              {/* Timeline dot */}
              <div
                className="w-4 h-4 rounded-full border-2 bg-white border-[#00B2A1] flex items-center justify-center cursor-pointer hover:scale-110 transition-all"
                style={{ marginBottom: '-8px' }}
                onClick={() => setSelectedPhase(phase.id)}
              >
                <div className="w-2 h-2 bg-[#00B2A1] rounded-full"></div>
              </div>
              {/* Phase label */}
              <span
                className="text-sm mt-2 font-medium text-center cursor-pointer"
                onClick={() => setSelectedPhase(phase.id)}
              >
                {phase.title}
              </span>
            </div>
          ))}
        </div>
      </div>
      {/* Hover Popover */}
      {hoveredPhase && hoveredPhaseData && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg p-6 z-50">
          <h4 className="text-lg font-semibold text-[#111827] mb-3">
            {hoveredPhaseData.title}
          </h4>
          <p className="text-[#4b5563] text-sm mb-4 leading-relaxed">
            {hoveredPhaseData.description}
          </p>
          {/* Status Summary Bar */}
          <div className="bg-gray-100 rounded-md px-4 py-3 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${hoveredPhaseData.status === 'completed' ? 'bg-green-500' :
                  hoveredPhaseData.status === 'in-progress' ? 'bg-yellow-500' :
                    'bg-gray-400'
                  }`}></div>
                <span className={`text-xs font-medium ${hoveredPhaseData.status === 'completed' ? 'text-green-700' :
                  hoveredPhaseData.status === 'in-progress' ? 'text-yellow-700' :
                    'text-gray-600'
                  }`}>
                  {hoveredPhaseData.status === 'completed' ? 'Completed' :
                    hoveredPhaseData.status === 'in-progress' ? 'In Progress' :
                      'Not Started'}
                </span>
              </div>
            </div>
            <div className="text-xs text-gray-600 mt-1">
              {hoveredPhaseData.statusText}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}