"use client";
import React from "react";
import { FaSearch } from "react-icons/fa";

interface SearchBarProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function SearchBar({ value, onChange }: SearchBarProps) {
  return (
    <div className="relative w-full">
      <FaSearch className="absolute left-3 inset-y-3 flex items-center text-[#666F8F]" size={16} />
      <input
        type="text"
        placeholder="Search by name"
        className="w-full pl-8 border border-[#CFD2DE] text-black px-3 rounded-[4px] bg-[#F4F5F6] placeholder-[#3B4154] focus:border-[#00B2A1] focus:ring-1 focus:ring-[#00B2A1] outline-none h-10"
        value={value}
        onChange={onChange}
      />
    </div>
  );
}
