"use client";
import React, { useEffect, useState } from "react";
import { Eye, EyeOff, X } from "lucide-react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { activateUser, createUser, deActivateUser, updateUser, User } from "@/service/userService";
import { DepartmentsResponse } from "@/service/departments";
import Loader from "../common/loader/loader";

interface UserDrawerProps {
  mode: "add" | "edit";
  isOpen: boolean;
  deps: DepartmentsResponse;
  user?: User;
  onClose: () => void;
  onSave: () => void;
}

export default function UserDrawer({
  mode,
  isOpen,
  deps,
  user,
  onSave,
  onClose,
}: UserDrawerProps) {
  const [form, setForm] = useState({
    fullName: "",
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    departments: [{ department: "", role: "" }],
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingactive, setLoadingActive] = useState(false);




  useEffect(() => {
    if (mode === "edit" && user) {
      setForm({
        fullName: user.name,
        username: user.username,
        email: user.email,
        password: "",
        confirmPassword: "",
        departments: user.department.map((d) => ({
          department: d.departmentId._id,
          role: d.departmentRole,
        })),
      });
    } else {
      setForm({
        fullName: "",
        username: "",
        email: "",
        password: "",
        confirmPassword: "",
        departments: [{ department: "", role: "" }],
      });
    }
  }, [mode, user, isOpen]);

  useEffect(() => {
    document.body.style.overflow = isOpen ? "hidden" : "";
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  const handleInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (mode === "add") {
      const { name, value } = e.target;
      setForm((f) => ({ ...f, [name]: value }));
    }
  };

  const handleSubmit = async () => {
    if (mode === "add") {
      const { fullName, username, email, password, confirmPassword, departments } = form;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

      if (!fullName || !username || !email || !password || !confirmPassword || departments.some((d) => !d.department || !d.role)) {
        toast.error("Please fill out all required fields before saving.");
        return;
      }

      if (!emailRegex.test(email)) {
        toast.error("Please enter a valid email address.");
        return;
      }

      if (password !== confirmPassword) {
        toast.error("Passwords do not match.");
        return;
      }

      if (password.length < 8) {
        toast.error("Password should be 8 characters or more.");
        return;
      }
      setLoading(true);
      const userData = {
        name: fullName,
        username,
        email,
        password,
        about: "about",
        isSysAdmin: false,
        department: departments.map((d) => ({
          departmentId: d.department,
          departmentRole: d.role,
        })),
      };

      try {
        const res = await createUser(userData);
        console.log(res);
        toast.success("User created successfully!");
        setLoading(false);
        onClose();
        onSave();
      } catch (error: any) {
        console.error(error);
        toast.error(error.data.message || "something went wrong, try again");
      } finally {
        setLoading(false);
      }
    } else {
      setLoading(true);
      const { departments } = form;
      const userData = {
        department: departments.map((d) => ({
          departmentId: d.department,
          departmentRole: d.role,
        })),
      };
      try {
        const res = await updateUser(userData, user?._id || "");
        console.log(res);
        toast.success("User Updated successfully!");
        setLoading(false);
        onClose();
        onSave();
      } catch (error: any) {
        console.error(error);
        toast.error(error.data.message || "something went wrong, try again");
      } finally {
        setLoading(false);
      }
    }
  };

  const handleActivate = async () => {
    setLoadingActive(true);
    try {
      if (user?.isDeleted) {
        const res = await activateUser(user._id);
        toast.success("User activated successfully!");
        onClose();
        onSave();
      } else {
        const res = await deActivateUser(user?._id || "");
        toast.success("User deactivated successfully!");
        onClose();
        onSave();
      }
    } catch (error: any) {
      console.error(error);
      toast.error(error.data.message || "something went wrong, try again");
    } finally {
      setLoadingActive(false);
    }
  }

  const selectedDepartments = form.departments.map((d) => d.department);

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar
        closeOnClick
        pauseOnHover
        draggable
        toastStyle={{ zIndex: 99999 }}
      />

      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={onClose} />
      )}

      <div
        className={`fixed top-0 right-0 h-full w-[40%] bg-white shadow-lg z-50 transform transition-transform duration-300 ease-in-out ${isOpen ? "translate-x-0" : "translate-x-full"
          } focus:outline-none focus:ring-2 focus:ring-[#00B2A1]`}
        tabIndex={-1}
      >
        <div className="flex items-center justify-between p-4" style={{ backgroundColor: "#3B4154" }}>
          <h2 className="text-lg font-medium text-white">
            {mode === "add" ? "Add User" : "Edit User"}
          </h2>
          <button onClick={onClose} className="p-1">
            <X className="w-6 h-6 text-white" />
          </button>
        </div>

        {/* Scrollable content */}
        <div className="p-6 overflow-y-auto space-y-4 max-h-[calc(100vh-140px)]">
          {[
            { name: "fullName", label: "Full Name", type: "text" },
            { name: "username", label: "Username", type: "text" },
            { name: "email", label: "Email", type: "email" },
            ...(mode === "add"
              ? [
                { name: "password", label: "Password", type: "password" },
                { name: "confirmPassword", label: "Confirm Password", type: "password" },
              ]
              : [])
          ].map(({ name, label, type }) => (
            <div key={name}>
              <label className="block text-sm font-medium mb-1">
                {label} <span className="text-red-500">*</span>
              </label>

              {(name === "password" || name === "confirmPassword") ? (
                <div className="relative">
                  <input
                    name={name}
                    type={(name === "password" ? showPassword : showConfirmPassword) ? "text" : "password"}
                    value={(form as any)[name]}
                    onChange={handleInput}
                    autoComplete="new-password"
                    className="w-full border rounded px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-[#00B2A1]"
                  />
                  <button
                    type="button"
                    onClick={() =>
                      name === "password"
                        ? setShowPassword(!showPassword)
                        : setShowConfirmPassword(!showConfirmPassword)
                    }
                    className="absolute top-1/2 right-3 transform -translate-y-1/2 text-gray-500 focus:outline-none"
                  >
                    {(name === "password" ? showPassword : showConfirmPassword) ? (
                      <EyeOff className="w-5 h-5" />
                    ) : (
                      <Eye className="w-5 h-5" />
                    )}
                  </button>
                </div>
              ) : (
                <input
                  name={name}
                  type={type}
                  value={(form as any)[name]}
                  onChange={handleInput}
                  readOnly={mode === "edit"}
                  autoComplete="off"
                  className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#00B2A1] ${mode === "edit" ? "bg-gray-100 cursor-not-allowed" : ""}`}
                />
              )}
            </div>
          ))}

          <div className="space-y-4">
            {form.departments.map((entry, index) => (
              <div key={index} className="flex gap-4 items-end">
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">Department <span className="text-red-500">*</span></label>
                  <select
                    value={entry.department}
                    onChange={(e) => {
                      const newDeps = [...form.departments];
                      newDeps[index].department = e.target.value;
                      setForm({ ...form, departments: newDeps });
                    }}
                    className="w-full border rounded px-3 py-2 bg-white focus:outline-none focus:ring-2 focus:ring-[#00B2A1]"
                  >
                    <option value="" disabled>
                      -- Select Department --
                    </option>
                    {deps?.data.departments.departments
                      .filter(
                        (d) =>
                          entry.department === d._id ||
                          !selectedDepartments.includes(d._id)
                      )
                      .map((d) => (
                        <option key={d._id} value={d._id}>
                          {d.name}
                        </option>
                      ))}
                  </select>
                </div>

                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">Role <span className="text-red-500">*</span></label>
                  <select
                    value={entry.role}
                    onChange={(e) => {
                      const newDeps = [...form.departments];
                      newDeps[index].role = e.target.value;
                      setForm({ ...form, departments: newDeps });
                    }}
                    className="w-full border rounded px-3 py-2 bg-white focus:outline-none focus:ring-2 focus:ring-[#00B2A1]"
                  >
                    <option value="" disabled>
                      Select role…
                    </option>
                    <option value="member">Member</option>
                    <option value="editor">Editor</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>

                {index > 0 && (
                  <button
                    onClick={() => {
                      const newDeps = form.departments.filter((_, i) => i !== index);
                      setForm({ ...form, departments: newDeps });
                    }}
                    className="text-red-500"
                  >
                    Remove
                  </button>
                )}
              </div>
            ))}

            {form.departments.length < deps?.data.departments.departments.length && (
              <button
                onClick={() =>
                  setForm({
                    ...form,
                    departments: [...form.departments, { department: "", role: "" }],
                  })
                }
                className="text-sm text-[#00B2A1] hover:underline mt-2"
              >
                + Add Department
              </button>
            )}
          </div>
        </div>

        <div className="flex justify-end p-4 border-t space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border rounded text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#00B2A1]"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={loading}
            className="px-4 py-2 bg-[#00B2A1] text-white rounded focus:outline-none focus:ring-2 focus:ring-[#00B2A1] flex items-center justify-center min-w-[120px]"
          >
            {loading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              mode === "add" ? "Create User" : "Save Changes"
            )}
          </button>
          {mode === "edit" && <button
            onClick={handleActivate}
            disabled={loadingactive}
            className={`px-4 py-2 rounded focus:outline-none focus:ring-2 flex items-center justify-center min-w-[120px]
              ${user?.isDeleted
                ? 'border-[#00B2A1] border text-[#00B2A1] focus:ring-[#00B2A1]'
                : 'border-red-500 border text-red-500 focus:ring-red-500'}
            `}          >
            {loadingactive ? (
              <div className="w-5 h-5 border-2 border-[#00B2A1] border-t-transparent rounded-full animate-spin" />
            ) : (
              user?.isDeleted ? "Activate User" : "Deactivate User"
            )}
          </button>}
        </div>


      </div>
    </>
  );
}
