"use client";
import React, { useState, useMemo, useEffect, } from "react";
import SearchBar from "@/components/users/SearchBar";
import AddButton from "./AddButton";
import UsersTable from "./UsersTable";
import { dummyUsers } from "./data/user";
import UserDrawer from "./UserDrawer";
import getAllUsers, { User, UsersResponse } from "@/service/userService";
import Loader from "../common/loader/loader";
import departmentService, { DepartmentsResponse } from "@/service/departments";


export interface DepartmentAssignment {
  departmentId: string;
  departmentRole: string;
}

export interface FormattedUser {
  name: string;
  email: string;
  password: string;
  username: string;
  about: string;
  isSysAdmin: boolean;
  department: DepartmentAssignment[];
}

export default function UsersSection() {
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState<boolean>(false);
  const [allUsers, setAllUsers] = useState<UsersResponse>();
  const [allDeps, setAllDeps] = useState<DepartmentsResponse>();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [mode, setMode] = useState<"add" | "edit">("add");
  const [currentUser, setCurrentUser] = useState<User | undefined>(undefined);

  const fetchAllUsers = async () => {
    try {
      setLoading(true);
      const res = await getAllUsers();
      setAllUsers(res);
    } catch (error) {
      // console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllDepartments = async () => {
    try {
      const res = await departmentService.getAllDepartmentList();
      setAllDeps(res);
    } catch (error) {
      // console.log(error);
    }
  };



  const filtered = useMemo(
    () =>
      allUsers?.users.filter((u) =>
        u.name.toLowerCase().includes(search.toLowerCase())
      ),
    [search, allUsers]
  );

  const openAdd = () => {
    setMode("add");
    setCurrentUser(undefined);
    setDrawerOpen(true);
  };

  const openEdit = (id: string) => {
    const u = allUsers?.users.find((u) => u._id === id);
    if (!u) return;
    setMode("edit");
    setCurrentUser(u);
    setDrawerOpen(true);
  };

  const handleSave = (user: FormattedUser) => {
    if (mode === "add") {
      console.log("create user", user);
    } else {
      console.log("update user", user);
    }
  };

  useEffect(() => {
    fetchAllUsers();
    fetchAllDepartments();
  }, []);

  return (
    <>
      <div className="px-8 py-4">
        <div className="flex w-full gap-5 h-10 mb-4 mt-4">
          <SearchBar value={search} onChange={(e) => setSearch(e.target.value)} />
          <AddButton onClick={openAdd} label="Add Users" />
        </div>

        {loading ? (
          <Loader />
        ) : filtered?.length === 0 ? (
          <p className="text-center text-gray-500 mt-4">
            No users found for "{search}"
          </p>
        ) : (
          <UsersTable users={filtered || []} onEdit={openEdit} />
        )}
      </div>

      <UserDrawer
        mode={mode}
        isOpen={drawerOpen}
        deps={allDeps!}
        user={currentUser}
        onClose={() => setDrawerOpen(false)}
        onSave={() => fetchAllUsers()}
      />
    </>
  );

}
