import React from "react";
import { Department } from "@/service/departments";
import { Building } from "lucide-react";

type Props = {
  dept: Department;
  onClick?: () => void;
};

export default function DepartmentCard({ dept, onClick }: Props) {
  return (
    <div
      onClick={onClick}
      className="cursor-pointer border rounded-xl p-4 shadow-sm bg-white hover:shadow-md transition-shadow duration-200 w-full h-full flex flex-col justify-between"
    >
      <Building className="h-12 w-12 text-gray-500 mb-4" />
      <div className="font-semibold text-lg mb-1">{dept.name}</div>
      <div className="text-sm text-gray-500 truncate">
        {dept.description || "No description"}
      </div>
    </div>
  );
}
