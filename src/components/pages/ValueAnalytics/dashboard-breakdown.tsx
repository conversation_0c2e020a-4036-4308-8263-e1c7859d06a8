import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { TbTrendingUp, TbArrowUp } from "react-icons/tb"
import type { Project } from "@/types/value-analytics"

interface DashboardBreakdownProps {
  projects: Project[]
  valuePillar?: "Operational Efficiency" | "Cost Reduction" | "Revenue Growth" | "Customer Experience"
  numProjects?: number
}

export function DashboardBreakdown({ projects, valuePillar, numProjects = 5 }: DashboardBreakdownProps) {
  const getProjectsByValue = () => {
    return projects
      .map((project) => ({
        ...project,
        totalValue: project.valuePillars
          .filter((pillar) => !valuePillar || pillar.name === valuePillar)
          .reduce(
            (sum, pillar) =>
              sum +
              pillar.benefits.reduce(
                (benefitSum, benefit) =>
                  benefitSum +
                  benefit.metrics.reduce((metricSum, metric) => metricSum + (metric.dollarValue || 0), 0),
                0,
              ),
            0,
          ),
      }))
      .sort((a, b) => b.totalValue - a.totalValue)
  }

  const getAllMetricsByValue = () => {
    const metrics: { name: string; projectName: string; preValue: number | null; postValue: number | null; unit?: string }[] = [];
    const seenMetrics = new Set<string>();

    projects.forEach((project) => {
      project.valuePillars.forEach((pillar) => {
        pillar.benefits.forEach((benefit) => {
          benefit.metrics.forEach((metric) => {
            if (!seenMetrics.has(metric.name)) {
              seenMetrics.add(metric.name);
              metrics.push({
                name: metric.name,
                projectName: project.name,
                preValue: metric.preAI,
                postValue: metric.postAI,
                unit: metric.unit,
              });
            }
          });
        });
      });
    });

    // Sort by preValue (non-null values first, then by value)
    return metrics.sort((a, b) => {
      if (a.preValue === null && b.preValue === null) return 0;
      if (a.preValue === null) return 1;
      if (b.preValue === null) return -1;
      return b.preValue - a.preValue;
    });
  }

  const topProjects = getProjectsByValue()
  const topMetrics = getAllMetricsByValue()

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card className="col-span-1">
        <CardHeader>
          <CardTitle>Top Projects</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topProjects.map((project, index) => (
              <div key={project.id} className="flex items-center gap-4">
                <div className="flex-none flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600 font-semibold">
                  {index + 1}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-medium truncate">{project.name}</p>
                  <p className="text-xl font-bold text-gray-900">${Math.round(project.totalValue).toLocaleString()}</p>
                </div>
                <TbTrendingUp className="flex-none w-6 h-6 text-green-500" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card className="col-span-1">
        <CardHeader>
          <CardTitle>Top Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topMetrics.map((metric, index) => (
              <div key={index} className="flex items-center gap-4">
                <div className="flex-none flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-600 font-semibold">
                  <TbArrowUp className="w-4 h-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-1">
                    <span>
                      <p className="font-bold">{metric.name} {metric.unit && metric.preValue !== null && metric.postValue !== null && <span className="font-bold">({metric.unit})</span>} </p>
                    </span>
                  </div>
                  <p className="text-xl font-bold text-gray-900">
                    {metric.preValue !== null && metric.postValue !== null && (
                      `${Math.round(metric.preValue).toLocaleString()} → ${Math.round(metric.postValue).toLocaleString()}`
                    )}
                  </p>
                  <p className="text-sm text-gray-500">{metric.projectName}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}





