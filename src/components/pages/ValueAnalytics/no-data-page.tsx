import {
    TbChart<PERSON>rrowsVertical,
    Tb<PERSON>lock,
    TbAdjustments,
    TbCurrencyDollar,
    TbThumbUp,
    TbDiscount,
    TbChartBar,
    TbFileDescription,
} from "react-icons/tb";
import { HiMiniBuildingOffice2 } from "react-icons/hi2"; // used in place of home/graph icon in top right of "Visualize Progress"

export default function NoDataPage() {
    return (
        <div className="p-6">
            <div className="text-3xl font-bold pb-2">Value Analytics</div>
            <div className="text-sm text-gray-500">Your AI transformation journey starts here</div>

            <div className="border border-[#CBCBCB] border-dashed rounded-lg p-4 mt-4 bg-[#F3F3F3]">
                <div className="text-2xl font-semibold mb-2">Welcome to Your Value Analytics Dashboard</div>
                <div className="text-sm text-gray-500 font-bold mb-2">
                    Track and visualize the impact of your AI transformation initiatives
                </div>
                <div className="text-sm text-[#66666F]">
                    Your dashboard is ready but waiting for data. Once we get more data for your projects, you’ll see real-time
                    metrics on value creation, cost reduction and more.
                </div>

                <div className="grid grid-cols-2 gap-6 pt-8">
                    <div className="flex items-start space-x-3">
                        <TbChartBar size={28} className="mt-1 bg-[#DCDCDD] rounded-full p-[6px]" />
                        <div>
                            <div className="font-semibold">Track Value Creation</div>
                            <div className="text-sm text-gray-500">Monitor both hard and soft dollar benefits</div>
                        </div>
                    </div>

                    <div className="flex items-start space-x-3">
                        <TbChartArrowsVertical size={28} className="mt-1 bg-[#DCDCDD] rounded-full p-[6px]" />
                        <div>
                            <div className="font-semibold">Visualize Progress</div>
                            <div className="text-sm text-gray-500">See your transformation journey in real-time</div>
                        </div>
                    </div>

                    <div className="flex items-start space-x-3">
                        <TbFileDescription size={28} className="mt-1 bg-[#DCDCDD] rounded-full p-[6px]" />
                        <div>
                            <div className="font-semibold">Generate Reports</div>
                            <div className="text-sm text-gray-500">Share insights with key stakeholders</div>
                        </div>
                    </div>

                    <div className="flex items-start space-x-3">
                        <TbClock size={28} className="mt-1 bg-[#DCDCDD] rounded-full p-[6px]" />
                        <div>
                            <div className="font-semibold">Historical Analysis</div>
                            <div className="text-sm text-gray-500">Compare performance over time</div>
                        </div>
                    </div>


                </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                <div className="border border-dashed rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                        <TbAdjustments size={24} className="text-blue-500 bg-blue-100 rounded-full p-1" />
                        <div className="font-semibold">Operational Efficiency</div>
                    </div>
                    <div className="text-sm text-gray-500 mt-1">Track how AI improves your operational processes and workflows</div>
                </div>

                <div className="border border-dashed rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                        <TbCurrencyDollar size={24} className="text-green-500 bg-green-100 rounded-full p-1" />
                        <div className="font-semibold">Revenue Growth</div>
                    </div>
                    <div className="text-sm text-gray-500 mt-1">Measure increased revenue from AI-enhanced products and services</div>
                </div>

                <div className="border border-dashed rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                        <TbThumbUp size={24} className="text-purple-500 bg-purple-100 rounded-full p-1" />
                        <div className="font-semibold">Customer Experience</div>
                    </div>
                    <div className="text-sm text-gray-500 mt-1">Analyze improvements in customer satisfaction and engagement</div>
                </div>

                <div className="border border-dashed rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                        <TbDiscount size={24} className="text-yellow-500 bg-yellow-100 rounded-full p-1" />
                        <div className="font-semibold">Cost Reduction</div>
                    </div>
                    <div className="text-sm text-gray-500 mt-1">Quantify cost savings achieved through AI implementation</div>
                </div>
            </div>
        </div>
    );
}
