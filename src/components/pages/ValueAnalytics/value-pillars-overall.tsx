import { Card, CardContent } from "@/components/ui/card"
import { TbCurrentLocation, TbUsers, TbTrendingUp, TbPigMoney } from "react-icons/tb"
import type { Project, ValuePillar } from "@/types/value-analytics"
import Link from "next/link"

interface ValuePillarsOverallProps {
  projects: Project[]
  activeRoute?: string
}

export function ValuePillarsOverall({ projects, activeRoute = "" }: ValuePillarsOverallProps) {
  const valuePillars: ValuePillar["name"][] = [
    "Operational Efficiency",
    "Customer Experience",
    "Revenue Growth",
    "Cost Reduction",
  ]

  const calculatePillarMetrics = () => {
    let valuePillar: { [key: string]: number } = {}
    return valuePillars.map((pillarName) => {
      const pillarData = getPillarData(pillarName)
      const totalValue = pillarData.reduce(
        (sum, pillar) =>
          sum +
          pillar.benefits.reduce(
            (benefitSum, benefit) =>
              benefitSum + benefit.metrics.reduce((metricSum, metric) => {
                valuePillar[pillarName] = (valuePillar[pillarName] || 0) + metric.dollarValue
                return metricSum + Math.abs(metric.dollarValue)
              }, 0),
            0,
          ),
        0,
      )
      const benefitsCount = pillarData.reduce((sum, pillar) => sum + pillar.benefits.length, 0)
      const metricsCount = pillarData.reduce(
        (sum, pillar) => sum + pillar.benefits.reduce((benefitSum, benefit) => benefitSum + benefit.metrics.length, 0),
        0,
      )

      return {
        name: pillarName,
        totalValue,
        benefitsCount,
        metricsCount,
      }
    })
  }

  const getPillarData = (pillarName: ValuePillar["name"]) => {
    return projects.flatMap((project) => project.valuePillars.filter((pillar) => pillar.name === pillarName))
  }

  const pillarMetrics = calculatePillarMetrics()

  const getValueType = (pillarName: string) => {
    if (pillarName === "Operational Efficiency" || pillarName === "Customer Experience") {
      return "Soft $$"
    } else {
      return "Hard $$"
    }
  }

  const getPillarIcon = (pillarName: string) => {
    switch (pillarName) {
      case "Operational Efficiency":
        return <TbCurrentLocation className="w-6 h-6 text-gray-500" />
      case "Customer Experience":
        return <TbUsers className="w-6 h-6 text-gray-500" />
      case "Revenue Growth":
        return <TbTrendingUp className="w-6 h-6 text-gray-500" />
      case "Cost Reduction":
        return <TbPigMoney className="w-6 h-6 text-gray-500" />
      default:
        return null
    }
  }

  const getValueColor = (pillarName: string) => {
    const isActive = activeRoute.toLowerCase().includes(pillarName.toLowerCase().replace(" ", "-"))
    if (isActive) {
      return getValueType(pillarName) === "Soft $$" ? "text-purple-500" : "text-blue-500"
    }
    return "text-gray-500"
  }

  const pillarToHref = (pillarName: string) => {
    return `/${pillarName.toLowerCase().replace(/ /g, "-")}`
  }



  return (
    <Card className="md:col-span-2 pt-6" id="valuePillarsOverall">
    <CardContent>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
        {pillarMetrics.map((pillar) => {
          const isActive = activeRoute.toLowerCase().includes(pillar.name.toLowerCase().replace(" ", "-"))
          const href = pillarToHref(pillar.name)
          return (
            <Link href={href} key={pillar.name}>
              <div className="space-y-2 text-center cursor-pointer p-4 rounded-lg">
                <div className="flex justify-center mb-0">{getPillarIcon(pillar.name)}</div>
                <h3 className={`font-semibold text-xl !mt-0 text-gray-900`}>{pillar.name}</h3>
                <div className="space-y-1 mt-0">
                  <p className={`text-3xl font-bold text-gray-900`}>
                    ${Math.round(pillar.totalValue).toLocaleString()}
                  </p>
                  <p className={`text-sm font-medium !mt-0 ${getValueColor(pillar.name)}`}>
                    {getValueType(pillar.name)}
                  </p>
                  <p className={`text-sm text-gray-500`}>
                    {pillar.benefitsCount} Projects | {pillar.metricsCount} Metrics
                  </p>
                </div>
              </div>
            </Link>
          )
        })}
      </div>
    </CardContent>
  </Card>
  )
}

