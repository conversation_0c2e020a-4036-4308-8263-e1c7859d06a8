"use client"
import { Info } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { TbTrendingUp } from "react-icons/tb"
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from "recharts"
import { Label } from "@/components/ui/label"

interface DashboardOverviewProps {
  totalValue: number;
  hardValue: number;
  softValue: number;
  monthlyAggregation: {
    year: number;
    month: number;
    totalDollarValue: number;
    hardDollarValue: number;
    softDollarValue: number;
  }[];
}

export function DashboardOverview({ totalValue, hardValue, softValue, monthlyAggregation }: DashboardOverviewProps) {
  console.log(totalValue, "total");
  console.log(hardValue, "hard");
  console.log(softValue, "soft");
  // const data = monthlyAggregation.map(item => {
  //   // Create a date object to get month name (setting to 1st of the month)
  //   const date = new Date(item.year, item.month - 1, 1);
  //   const monthName = date.toLocaleString('en-US', { month: 'short' });

  //   return {
  //     month: `${monthName}-${item.year}`,
  //     total: item.totalDollarValue,
  //     hard: item.hardDollarValue,
  //     soft: item.softDollarValue
  //   };
  // });


  // Custom tooltip that shows both monthly and cumulative values
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      // Find the data point that matches the current label (month)
      const currentPoint = data.find(item => item.month === label);

      if (!currentPoint) return null;

      return (
        <div className="bg-white text-[16px] p-4 border rounded shadow-md">
          <p className="font-bold">{label}</p>

          <div className="mb-3">
            <p className="text-[#3b82f6]">
              Hard Savings: ${currentPoint.monthlyHardSavings.toLocaleString()}
            </p>
            <p className="text-[#8b5cf6]">
              Soft Savings: ${currentPoint.monthlySoftSavings.toLocaleString()}
            </p>
            <p className="text-[#00B2A1]">
              Total: ${currentPoint.monthlyTotalSavings.toLocaleString()}
            </p>
          </div>

          <div>
            <p className="text-xs font-semibold text-gray-500 uppercase mb-1">Running Total</p>
            <p className="text-[#3b82f6]">
              Hard Savings: ${currentPoint.hardSavings.toLocaleString()}
            </p>
            <p className="text-[#8b5cf6]">
              Soft Savings: ${currentPoint.softSavings.toLocaleString()}
            </p>
            <p className="text-[#00B2A1] font-bold">
              Total: ${currentPoint.totalSavings.toLocaleString()}
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  // First, we need to prepare both monthly and cumulative data
  const prepareData = () => {
    // Sort the monthly aggregation by date to ensure chronological order
    const sortedMonthlyData = [...monthlyAggregation].sort((a, b) => {
      return (a.year * 12 + a.month) - (b.year * 12 + b.month);
    });

    // Calculate cumulative totals
    let cumulativeHard = 0;
    let cumulativeSoft = 0;

    // Create an array to store both monthly and cumulative data
    const combinedData = sortedMonthlyData.map(item => {
      // Create a date object to get month name (setting to 1st of the month)
      const date = new Date(item.year, item.month - 1, 1);
      const monthName = date.toLocaleString('en-US', { month: 'short' });

      // Calculate cumulative values
      cumulativeHard += item.hardDollarValue;
      cumulativeSoft += item.softDollarValue;
      const cumulativeTotal = cumulativeHard + cumulativeSoft;

      return {
        month: `${monthName}-${item.year}`,
        // Monthly values
        monthlyHardSavings: item.hardDollarValue,
        monthlySoftSavings: item.softDollarValue,
        monthlyTotalSavings: item.hardDollarValue + item.softDollarValue,
        // Cumulative values
        hardSavings: cumulativeHard,
        softSavings: cumulativeSoft,
        totalSavings: cumulativeTotal
      };
    });

    return combinedData;
  };
  const data = prepareData();

  const formatYAxis = (value: number) => {
    return `$${value.toLocaleString()}`;
  };
  return (
    <div className="grid grid-cols-1 md:grid-cols-[320px_1fr] h-[240px] gap-4">
      <style jsx>{`
          .tooltip {
            position: relative;
            display: inline-block;
          }

          .tooltip .tooltiptext {
            visibility: hidden;
            background-color: #fff;
            color: #555;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            box-shadow: 0px 0px 12px #00000029;
            z-index: 100;
            bottom: 125%;
            left: 50%;
            width: 300px;
            margin-left: -81px;
            opacity: 0;
            transition: opacity 0.3s;
          }

          .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
          }
        `}</style>
      <div className="space-y-4">
        <Card className="h-full">
          <CardContent className="pt-6">
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <TbTrendingUp className="h-5 w-5 text-green-600" />
                <span className="text-sm text-gray-600">Project Value Released</span>
              </div>
              <p className="text-3xl font-bold pb-4">${Math.round(totalValue).toLocaleString()}</p>

              <div className="flex items-center space-x-2">
                <p className="text-sm text-gray-600 cursor-pointer">Hard $$</p>
                <div className="tooltip ml-1">
                  <Info className="w-4 h-4 text-gray-400" />
                  <span className="tooltiptext">Directly measurable financial benefits with clear documentation, such as reduced costs, increased revenue, or avoided expenses. Comprised of Revenue Growth and Cost Reduction initiatives.</span>
                </div>
              </div>

              <div className="mt-2 flex items-center space-x-2 ">
                <p className="text-md font-medium">${Math.round(hardValue).toLocaleString()}</p>
                <div className="flex-1">
                  <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div className="h-full bg-blue-500" style={{ width: `${(hardValue / totalValue) * 100}%` }}></div>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <p className="text-sm text-gray-600 cursor-pointer">Soft $$</p>
                <div className="tooltip ml-1">
                  <Info className="w-4 h-4 text-gray-400" />
                  <span className="tooltiptext">Estimated value of non-financial benefits like time saved, improved productivity, or enhanced customer satisfaction that indirectly impact profitability. Comprised of Operational Efficiency and Customer Experience</span>
                </div>
              </div>


              <div className="flex items-center space-x-2">
                <p className="text-md font-medium">${Math.round(softValue).toLocaleString()}</p>
                <div className="flex-1">
                  <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div className="h-full bg-purple-500" style={{ width: `${(softValue / totalValue) * 100}%` }}></div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>


      <Card id="stackedareaValueBreakdown" className="h-[240px]">
        <CardContent className="pt-6">
          <div className="flex justify-end space-x-4 mb-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-[#3b82f6]"></div>
              <Label htmlFor="hard">Hard Savings</Label>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-[#8b5cf6]"></div>
              <Label htmlFor="soft">Soft Savings</Label>
            </div>
          </div>
          <div className="h-[180px]">
            <ResponsiveContainer width="100%" height="100%" className="text-sm">
              <AreaChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={formatYAxis} width={80} />
                <Legend wrapperStyle={{ display: 'none' }} />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  type="monotone"
                  dataKey="softSavings"
                  stackId="1"
                  stroke="#8b5cf6"
                  fill="#8b5cf6"
                  name="Soft Savings"
                />
                <Area
                  type="monotone"
                  dataKey="hardSavings"
                  stackId="1"
                  stroke="#3b82f6"
                  fill="#3b82f6"
                  name="Hard Savings"
                />

                {/* <Area
                  type="monotone"
                  dataKey="totalSavings"
                  stackId="1"
                  stroke="#AFE1AF"
                  fill="#AFE1AF"
                  name="Total Savings"
                /> */}
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* <Card id="stackedareaValueBreakdown" className="h-[240px]">
        <CardContent className="pt-6">
          <div className="flex justify-end space-x-4 mb-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-[#00B2A1]"></div>
              <Label htmlFor="total">Project Value</Label>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-[#3b82f6]"></div>
              <Label htmlFor="hard">Hard Value</Label>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-[#8b5cf6]"></div>
              <Label htmlFor="soft">Soft Value</Label>
            </div>
          </div>
          <div className="h-[180px]">
            <ResponsiveContainer width="100%" height="100%" className="text-sm">
              <ComposedChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <RechartsTooltip />
                <Area
                  type="monotone"
                  dataKey="soft"
                  stackId="1"
                  stroke="#8b5cf6"
                  fill="#8b5cf6"
                  name="Soft Value"
                />
                <Area
                  type="monotone"
                  dataKey="hard"
                  stackId="1"
                  stroke="#3b82f6"
                  fill="#3b82f6"
                  name="Hard Value"
                />
                <Area
                  type="monotone"
                  dataKey="total"
                  stackId="1"
                  stroke="#00B2A1"
                  fill="#00B2A1"
                  name="Project Value"
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card> */}
    </div>
  )
}






