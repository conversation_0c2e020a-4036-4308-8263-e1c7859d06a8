import React, { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import { userService } from "@/service/api";
import { authService } from "@/service/api";
import { EyeIcon, EyeSlashIcon } from "@heroicons/react/24/solid";

const passwordCriteria = (password: string) => {
  return /^(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]).{8,}$/.test(password);
};

const ResetPassword: React.FC = () => {
  console.log("ResetPassword page loaded");
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const [timer, setTimer] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    if (!passwordCriteria(password)) {
      setError("Password must be at least 8 characters and contain at least 1 special character.");
      return;
    }
    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      return;
    }
    if (!token) {
      setError("Invalid or missing reset token.");
      return;
    }
    setLoading(true);
    try {
      await authService.resetPasswordWithToken({ token: token as string, newPassword: password, confirmPassword });
      setSuccess(true);
      router.push("/login?reset=success");
    } catch (err: any) {
      setError("Reset link expired or invalid. Please request a new link.");
    } finally {
      setLoading(false);
    }
  };

  const handleSendResetLink = async () => {
    setLoading(true);
    setError("");
    try {
      // await authService.sendResetLink({ email }); // <-- COMMENT THIS LINE
      setSuccess(true); // <-- DIRECTLY SET SUCCESS
      setTimer(30);
    } catch (err: any) {
      setError(err?.response?.data?.message || "Email not registered or error occurred.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col md:flex-row h-screen w-full">
      {/* Left Section (Reset Password Form) */}
      <div className="w-full md:w-[40%] bg-white flex flex-col justify-center items-center p-6 md:p-12 relative">
        <header className="flex items-center justify-center w-full px-4 h-12 relative">
          <div className="relative w-20 h-20 md:w-24 md:h-24">
            <Image src="/assets/Group 4355.svg" alt="Logo" layout="fill" objectFit="contain" />
          </div>
        </header>
        <div className="w-full max-w-sm mt-10 md:mt-20">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">Reset Password</h2>
          <form onSubmit={handleSubmit}>
            <label className="block text-gray-600 text-sm mb-1">New password</label>
            <div className="relative w-full mb-4">
              <input
                type={showPassword ? "text" : "password"}
                className="w-full border border-gray-400 text-black rounded-md px-3 py-2 pr-10 focus:border-[#00B2A1] focus:ring-1 focus:ring-[#00B2A1] outline-none"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-3 flex items-center text-gray-500 hover:text-[#00B2A1]"
                tabIndex={-1}
              >
                {showPassword ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />}
              </button>
            </div>
            <label className="block text-gray-600 text-sm mb-1">Re-enter password</label>
            <div className="relative w-full mb-4">
              <input
                type={showConfirmPassword ? "text" : "password"}
                className="w-full border border-gray-400 text-black rounded-md px-3 py-2 pr-10 focus:border-[#00B2A1] focus:ring-1 focus:ring-[#00B2A1] outline-none"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute inset-y-0 right-3 flex items-center text-gray-500 hover:text-[#00B2A1]"
                tabIndex={-1}
              >
                {showConfirmPassword ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />}
              </button>
            </div>
            {error && <p className="text-red-500 text-sm mb-2">{error}</p>}
            <button
              type="submit"
              className="w-full py-2 rounded-md text-white bg-[#00B2A1] hover:bg-[#009688] mb-2"
              disabled={loading}
            >
              {loading ? "Saving..." : "Save"}
            </button>
          </form>
          {success && (
            <></>
          )}
        </div>
      </div>
      {/* Right Section (Background Image) */}
      <div
        className="w-full md:w-[60%] bg-black flex flex-col justify-end text-white p-6 md:p-10 relative"
        style={{
          backgroundImage: "url('/assets/Mask Group 12.png')",
          backgroundSize: "cover",
          backgroundPosition: "center"
        }}
      >
        <div className="text-left pb-10">
          <h2 className="text-xl font-semibold mb-4 text-[#00B2A1]">
            AI for Business Builders
          </h2>
          <p className="text-gray-400">
            Discover and actualize innovative new business opportunities and applications,
            driving ongoing substantial business growth.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword; 