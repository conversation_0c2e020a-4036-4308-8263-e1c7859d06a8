import filesService from "@/service/files";
import { Loader2, X } from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import { ToastState } from "../project/tabs/manage";
import { Toast } from "@/components/ui/toast/toast";

interface AddCompanyDataDetailsProps {
  onSave: (data: any) => void;
  initialData?: any;
  formData?: any;
  scrollAtTopFunction: () => void;
}

interface UploadData {
  fileType: string;
  resourceLink: string;
  format: string;
  filesize?: number;
}

const AddCompanyDataDetails: React.FC<AddCompanyDataDetailsProps> = ({
  onSave,
  initialData,
  formData: globalFormData,
  scrollAtTopFunction,
}) => {
  const [isResourceLink, setIsResourceLink] = useState(true);
  const [name, setName] = useState(initialData?.name || "Catalogue - XML");
  const [description, setDescription] = useState(initialData?.description || "");
  const [fileType, setFileType] = useState(initialData?.fileType || "");
  const [resourceType, setResourceType] = useState(initialData?.resourceType || "link");
  const [resourceLink, setResourceLink] = useState(initialData?.resourceLink || "");
  const [format, setFormat] = useState(initialData?.format || "");
  const [uploadData, setUploadData] = useState<UploadData>(initialData || {});
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [toast, setToast] = useState<ToastState>(null);


  const handleSave = () => {
    let finalName = name;
    if (isResourceLink) {
      const urlParts = resourceLink.split("/");
      finalName = urlParts[urlParts.length - 1] || name;

      // Default to 'xml' if no format is detected or selected
      const detectedFormat = extractFormat(resourceLink);
      const finalFormat = format || detectedFormat || "csv";

      onSave({
        name: finalName,
        description,
        resourceType,
        resourceLink,
        format: finalFormat,
        fileType: finalFormat, // Ensure fileType is always set
        isUrl: true,
        filesize: 0,
      });
    } else {
      finalName = uploadData.fileType || name;

      onSave({
        ...uploadData,
        name: finalName,
        description,
        resourceType,
        isUrl: false,
      });
      scrollAtTopFunction();
    }
  };

  useEffect(() => {
    if (initialData) {
      setName(initialData.name);
      setDescription(initialData.description);
      setResourceType(initialData.resourceType);
      setResourceLink(initialData.resourceLink);
      setFormat(initialData.format);
      setFileType(initialData?.fileType || "");
      setUploadData(initialData);
    }
  }, [initialData]);

  // Add helper function to extract and validate format
  const extractFormat = (path: string): string => {
    const extension = path.split(".").pop()?.toLowerCase() || "";
    // Check if it's a valid format we support
    if (["csv", "xlsx", "xls", "json", "xml", "jpg", "png", "jpeg", "pdf", "doc", "docx"].includes(extension)) {
      return extension;
    }
    return "";
  };

  // Update the file upload handler
  const handleFileUpload = async (file: File) => {
    if (file.size > 20 * 1024 * 1024) {
      setToast({
        message: "File size exceeds 20 MB limit.",
        type: "error",
      });
      return;
    }

    setIsUploading(true);
    try {
      const fileExtension = file.name.split(".").pop()?.toLowerCase() || "";
      const fileName = file.name.split(".")[0];
      const newFile = new File([file], `${fileName}.${fileExtension}`, {
        type: file.type,
      });

      const formData = new FormData();
      formData.append("file", newFile);
      const response = await filesService.uploadFile(
        newFile,
        description,
        globalFormData?.title?.toLowerCase().replace(/ /g, "-") || ""
      );

      if (response.status !== 201) {
        throw new Error("Upload failed");
      }

      setFormat(fileExtension);

      setUploadData({
        ...uploadData,
        resourceLink: response.data.url,
        fileType: response.data.filename,
        format: fileExtension,
        filesize: file.size,
      });
      setIsResourceLink(false);
      setResourceLink(response.data.url);
      setName(fileName);
      setSelectedFile(file);
    } catch (error) {
      setToast({message:"Something went wrong while uploading file",type:"error"})
      //console.error("Error uploading file:", error);
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const isFormValid = () => {
    if (isResourceLink) {
      return resourceLink.trim() !== "" && format !== "";
    }
    return selectedFile !== null;
  };

  useEffect(() => {
    window.scrollTo(0, 0);
    scrollAtTopFunction();
  }, []);

  return (
    <div>
      {/* Name Input
      <label className="block text-sm font-medium">Name *</label>
      <input
        type="text"
        value={name}
        onChange={(e) => {
          setName(e.target.value);
        }}
        className="w-full p-2 border rounded mt-1 mb-4 hover:border-teal-500 focus:border-green-500 focus:ring-1 focus:ring-green-500"
      /> */}

      {/* Data Description */}
      <label className="block text-sm font-medium">Data Description</label>
      <textarea
        placeholder="Eg. Monthly sales data for Q1"
        value={description}
        onChange={(e) => {
          setDescription(e.target.value);
        }}
        className="w-full p-2 border rounded mt-1 mb-4 hover:border-teal-500 focus:border-green-500 focus:ring-1 focus:ring-green-500"
      ></textarea>

      {/* Resource Source */}
      <label className="block text-sm font-medium">
        Select Resource Source <span className="text-red-500">*</span>
      </label>
      <div className="flex gap-2 my-2">
        <button
          className={`px-4 py-2 rounded ${resourceType === "link" ? "bg-teal-500 text-white" : "bg-gray-200"}`}
          onClick={() => {
            setResourceType("link");
            setIsResourceLink(true);
          }}
        >
          Link to a file
        </button>
        {/* <button
          className={`px-4 py-2 rounded ${
            resourceType === "upload" ? "bg-teal-500 text-white" : "bg-gray-200"
          }`}
          onClick={() => setResourceType("upload")}
        >
          Upload a file
        </button> */}
        <div className="border border-teal-500 rounded-md ">
          <input
            type="file"
            onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])}
            className="hidden"
            id="file-upload"
            accept=".xml,.json,.csv,.xlsx,.xls,.jpg,.jpeg,.png,.pdf,.doc,.docx"
            ref={fileInputRef}
          />
          <label
            htmlFor="file-upload"
            className="bg-white text-gray-700 cursor-pointer inline-flex items-center px-4 py-2  rounded-md shadow-sm text-sm font-medium"
          >
            {isUploading ? <Loader2 className="animate-spin" /> : "Upload File"}
          </label>
          {/* {selectedFile && (
            <p className="mt-2 text-sm text-gray-600">
              Selected file: {selectedFile.name}
            </p>
          )} */}
        </div>
      </div>

      {resourceType === "link" && (
        <>
          <div className="relative">
            <input
              type="text"
              value={resourceLink}
              onChange={(e) => {
                const newLink = e.target.value;
                setResourceLink(newLink);
                // Auto-detect format from URL
                if (newLink) {
                  const detectedFormat = extractFormat(newLink);
                  setFormat(detectedFormat);
                }
              }}
              placeholder="http://pagwell.gov.uk/library/catalogue.xml"
              className="w-full p-2 border rounded mt-1 mb-4 hover:border-teal-500 focus:border-green-500 focus:ring-1 focus:ring-green-500 pr-10"
            />
            {resourceLink && (
              <button
                onClick={() => {
                  setResourceLink("");
                  setFormat("");
                  setSelectedFile(null);
                  setUploadData({} as UploadData);
                  setIsResourceLink(true);
                }}
                className="absolute right-2 top-[40%] -translate-y-1/2 p-2 text-gray-400 hover:text-gray-600 focus:outline-none"
                type="button"
              >
                <X />
              </button>
            )}
          </div>
          {/* Format Dropdown */}
          <label className="block text-sm font-medium">Format</label>
          <select
            value={format}
            onChange={(e) => {
              setFormat(e.target.value);
            }}
            className="w-full p-2 border rounded mt-1 mb-4 hover:border-teal-500 focus:border-green-500 focus:ring-1 focus:ring-green-500"
          >
            <option value="">Select Format (.csv, .xlsx, .xls, .json, .xml)</option>
            <option value="csv">.csv</option>
            <option value="xlsx">.xlsx</option>
            <option value="xls">.xls</option>
            <option value="json">.json</option>
            <option value="doc">.doc</option>
            <option value="docx">.docx</option>
            <option value="pdf">.pdf</option>
            <option value="jpeg">.jpeg</option>
            <option value="png">.png</option>
            <option value="jpg">.jpg</option>
          </select>
        </>
      )}
      {/* {resourceType === "upload" && ( */}
      <div className="mt-1 mb-4">
        {/* <input
            type="file"
            onChange={(e) =>
              e.target.files?.[0] && handleFileUpload(e.target.files[0])
            }
            className="hidden"
            id="file-upload"
            accept=".xml,.json,.csv,.xlsx"
          /> */}
        {/* <label
            htmlFor="file-upload"
            className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            {isUploading ? <Loader2 className="animate-spin" /> : "Choose File"}
          </label> */}
        {selectedFile && <p className="mt-2 text-sm text-gray-600">Selected file: {selectedFile.name}</p>}
      </div>
      {/* )} */}

      {/* Add Save button at the bottom */}
      <div className="mt-6 flex justify-end">
        <button
          onClick={handleSave}
          disabled={!isFormValid()}
          className={`px-4 py-2 text-white rounded transition-colors duration-200 ${isFormValid() ? "bg-teal-500 hover:bg-teal-600 active:bg-teal-700" : "cursor-not-allowed bg-gray-400"
            }`}
        >
          {initialData ? "Update Details" : "Save Details"}
        </button>
      </div>
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export default AddCompanyDataDetails;
