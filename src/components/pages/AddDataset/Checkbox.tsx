import React from "react";

interface CheckboxProps {
  id: string;
  label: string;
  checked: boolean;
  onChange: (e:any) => void;
}

const Checkbox: React.FC<CheckboxProps> = ({ id, label, checked, onChange }) => {
  return (
    <div className="flex items-center">
      <input
        type="checkbox"
        id={id}
        checked={checked}
        onChange={onChange}
        className="w-4 h-4 rounded border-[#00B2A1] text-[#00B2A1] focus:ring-[#00B2A1] accent-[#00B2A1]"
      />
      <label htmlFor={id} className="ml-2">{label}</label>
    </div>
  );
};

export default Checkbox;
