"use client";
import React, { useEffect, useRef, useState } from "react";
import { Info, ChevronDown, X, Loader2, Check } from "lucide-react";
import { datasetService } from "@/service/datasetService";
import Image from "next/image";
import { getProjects } from "@/service/projects";
import StepIndicator from "./StepIndicator";
import AddDataStep from "./AddDataStep";
import { toast, ToastContainer } from "react-toastify";
import { useRouter } from "next/navigation";
import CheckboxDialog from "./CheckboxDialog";
import ProcessingDialog from "./ProcessingDialog";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { setRunningInBgStatus } from "@/lib/store/features/dataset/runInBgSlice";
import Checkbox from "@/components/ui/checkbox/page";

interface AboutIntercept {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  title: string;
  isWorldDataset: boolean;
  projectDetails?: any;
  className?: string;
}

interface FormData {
  visibility: string;
  title: string;
  description: string;
  tags: string[];
  purpose: string;
  source: string;
  license: string;
  departments: string[];
  projects: string[];
  datasets?: any[];
  isWorldDataset: boolean;
}

interface SubmitData {
  title: string;
  slug: string;
  description: string;
  visibility: string;
  department: string[];
  projectIds: string[];
  isWorldDataset: boolean;
  files: {
    filename: string;
    isUrl?: boolean;
    filesize?: number;
    url?: string;
    description?: string;
    fileType?: string;
  }[];
  fileTypes: string[];
  tags: string[];
  license: string;
}

const AddDataset: React.FC<AboutIntercept> = ({
  isOpen,
  setIsOpen,
  title,
  isWorldDataset,
  projectDetails,
  className,
}) => {
  const dispatch = useDispatch();
  const isrunninginbg = useSelector((state: RootState) => state.runInBg.isRunningInBg);

  const [formData, setFormData] = useState<FormData>({
    visibility: "private",
    title: "",
    description: "",
    tags: [],
    purpose: projectDetails ? "project" : "",
    source: "",
    license: "",
    departments: [],
    projects: projectDetails ? [projectDetails._id] : [],
    datasets: [],
    isWorldDataset: false,
  });

  const [submitData, setSubmitData] = useState<SubmitData>({
    visibility: "private",
    title: "",
    description: "",
    tags: [],
    license: "",
    department: [],
    projectIds: [],
    isWorldDataset: isWorldDataset,
    files: [],
    fileTypes: [],
    slug: "",
  });

  const router = useRouter();
  const [isDepartmentDropdownOpen, setIsDepartmentDropdownOpen] = useState(false);
  const [isProjectDropdownOpen, setIsProjectDropdownOpen] = useState(false);
  const [isLicenseDropdownOpen, setIsLicenseDropdownOpen] = useState(false);
  const [departmentList, setdepartmentList] = useState<any>(null);
  const [checkedDepartments, setCheckedDepartments] = useState<string[]>([]);

  const [searchTag, setSearchTag] = useState("");
  const [isTagDropdownOpen, setIsTagDropdownOpen] = useState(false);
  const [tagList, setTagList] = useState<any>(null);
  const [projectList, setprojectList] = useState<any>(null);
  const [checkedProjects, setCheckedProjects] = useState<string[]>([projectDetails?.name]);
  const [isPrivate, setIsPrivate] = useState(true);

  const [licenseList, setLicenseList] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const scrollableContentRef = useRef<HTMLDivElement>(null);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isProcessingOpen, setIsProcessingOpen] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([
    "tags",
    "ontology",
    "unified_data_summary",
    "files_content_ontologies",
    "time_range",
    "geo_location",
    "language",
    "data_industry",
    "intended_use_case",
    "ai_ml_use_case",
    "file_content_summary",
    "validate_file",
  ]);
  //console.log(formData, "this is fresh form data");

  useEffect(() => {
    setCurrentStep(1);
    setCheckedDepartments([]);
    setCheckedProjects([]);
    setIsPrivate(true);
    setSearchTag("");
    setIsTagDropdownOpen(false);
    setFormData({
      visibility: "private",
      title: "",
      description: "",
      tags: [],
      purpose: "",
      source: "",
      license: "",
      departments: [],
      projects: [],
      datasets: [],
      isWorldDataset: false,
    });
    if (scrollableContentRef.current) {
      scrollableContentRef.current.scrollTop = 0;
    }
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  const slugify = (title: string): string => {
    return title.toLowerCase().trim().replace(/\s+/g, "-").replace(/-+/g, "-");
  };

  const checkDatasetTitle = async (title: string) => {
    try {
      const result: any = await datasetService.checkDatasetTitle(title);
      //console.log("CHECK DATASET TITLE", result);
      return result;
    } catch (error) {
      toast.error("Something Went Wrong!!");
      //throw error;
    }
  };

  useEffect(() => {
    setSubmitData({
      visibility: formData.visibility,
      title: formData.title,
      description: formData.description,
      tags: formData.tags,
      license: formData.license,
      department: formData.departments || [],
      projectIds: formData.purpose === "project" ? formData.projects || [] : [],
      files:
        formData.datasets?.map((dataset) => ({
          filename: `${dataset.name}`,
          filesize: dataset?.filesize || 0,
          url: dataset?.resourceLink || "",
          description: dataset?.description || "",
          fileType: dataset?.fileType || "",
          isUrl: dataset?.isUrl || false,
        })) || [],
      slug: formData.title?.toLowerCase().replace(/ /g, "-") || "",
      isWorldDataset: isWorldDataset,
      fileTypes: formData.datasets?.map((dataset) => dataset.fileType) || [],
    });
    // console.log(formData);
  }, [formData, isWorldDataset]);

  useEffect(() => {
    // console.log("submit data", submitData);
  }, [submitData]);

  const toggleVisibility = () => {
    const visibility = !isPrivate ? "private" : "public";
    setIsPrivate(!isPrivate);
    setFormData((prevData) => ({
      ...prevData,
      visibility: visibility,
    }));
  };

  const toggleDepartment = (deptName: string, deptId: string) => {
    setFormData((prevData) => ({
      ...prevData,
      departments: prevData.departments.includes(deptId)
        ? prevData.departments.filter((d) => d !== deptId)
        : [...prevData.departments, deptId],
    }));

    setCheckedDepartments((prev) =>
      prev.includes(deptName) ? prev.filter((d) => d !== deptName) : [...prev, deptName]
    );
    setIsDepartmentDropdownOpen(false);
  };

  useEffect(() => {
    if (projectDetails) {
      setFormData({ ...formData, purpose: "project" });
      setFormData({ ...formData, projects: [projectDetails._id] });
      setCheckedProjects([projectDetails.name]);
    }
  }, [isOpen]);

  const toggleProject = (project: string, projectId: string) => {
    setFormData((prevData) => ({
      ...prevData,
      projects: prevData.projects.includes(projectId)
        ? prevData.projects.filter((p) => p !== projectId)
        : [...prevData.projects, projectId],
    }));
    setCheckedProjects((prev) => (prev.includes(project) ? prev.filter((p) => p !== project) : [...prev, project]));
  };

  const toggleTag = (tagName: string) => {
    setFormData((prevData) => ({
      ...prevData,
      tags: prevData.tags.includes(tagName)
        ? prevData.tags.filter((tag) => tag !== tagName)
        : [...prevData.tags, tagName],
    }));
    setIsTagDropdownOpen(false);
  };

  const selectLicense = (license: string) => {
    setFormData((prevData) => ({ ...prevData, license }));
    setIsLicenseDropdownOpen(false);
  };

  const handleTagSubmit = async (e: any) => {
    e.preventDefault();
    const value = searchTag.trim();
    if (!value) return;
    toggleTag(value);
  };

  const [exploreUrlQuery, setExploreUrlQuery] = useState({
    page: 1,
    limit: 10,
    search: "",
  });

  const fetchExploreData = async () => {
    try {
      const result = await datasetService.getDepartmentList(exploreUrlQuery);
      setdepartmentList(result.data);
    } catch (err) {
      //console.log("Failed to fetch data.");
    }
  };

  useEffect(() => {
    fetchExploreData();
  }, [exploreUrlQuery]);

  const handleOnClose = () => {
    dispatch(setRunningInBgStatus({ status: true }));
    setIsProcessingOpen(false);
    setIsOpen(false);
    // Navigate away
    if (isWorldDataset) {
      router.push("/");
    } else {
      router.push("/");
    }
  };

  const fetchProjectsData = async () => {
    try {
      const result = await getProjects(exploreUrlQuery);
      // Filter out dashboard projects
      const filteredProjects = result?.projects?.filter((project) => project?.appType !== "Dashboard") ?? [];
      setprojectList(filteredProjects);
    } catch (err) {
      //console.log("Failed to fetch data.");
    }
  };

  useEffect(() => {
    fetchProjectsData();
  }, []);

  useEffect(() => {
    fetchExploreData();
  }, [exploreUrlQuery]);

  const fetchTagsData = async (search: string) => {
    if (!search) {
      setTagList(null);
      return;
    }
    try {
      const result = await datasetService.getTagList({
        search,
        type: "tag",
        page: 1,
        limit: 10,
      });
      setTagList(result.data);
      setIsTagDropdownOpen(true);
    } catch (err) {
      //console.error("Failed to fetch tag data.", err);
    }
  };

  const fetchLicenseData = async () => {
    try {
      const result = await datasetService.getTagList({
        type: "license",
        page: 1,
        limit: 10,
      });
      setLicenseList(result.data);
    } catch (err) {
      //console.error("Failed to fetch license data.", err);
    }
  };

  useEffect(() => {
    fetchLicenseData();
  }, []);

  const handleTagSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTag(value);
    fetchTagsData(value);
  };

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (!event.target.closest(".tag-dropdown")) {
        setIsTagDropdownOpen(false);
      }
    };
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (!event.target.closest(".project-dropdown")) {
        setIsProjectDropdownOpen(false);
      }
    };
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  const handleoncancel = () => {
    setIsOpen(false);
    setCurrentStep(1);
    setCheckedDepartments([]);
    setCheckedProjects([]);
    setIsPrivate(true);
    setSearchTag("");
    setIsTagDropdownOpen(false);
    setFormData({
      visibility: "private",
      title: "",
      description: "",
      tags: [],
      purpose: "",
      source: "",
      license: "",
      departments: [],
      projects: [],
      datasets: [],
      isWorldDataset: false,
    });
  };

  const handleSubmit = async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    try {
      const submitDataToSend = {
        ...submitData,
        title: formData.title,
        description: formData.description,
        visibility: formData.visibility,
        department: formData.departments,
        projectIds: formData.projects,
        isWorldDataset: isWorldDataset,
        files:
          formData.datasets?.map((dataset) => ({
            filename: dataset.name,
            filesize: dataset.filesize || 0,
            url: dataset.resourceLink,
            description: dataset.description,
            fileType: dataset.format,
            isUrl: dataset.isUrl,
          })) || [],
        fileTypes: formData.datasets?.map((dataset) => dataset.format) || [],
        tags: formData.tags,
        license: formData.license,
        slug: formData.title.toLowerCase().replace(/ /g, "-"),
        dataplaceKeys: selectedOptions,
      };

      const res: any = await datasetService.createDataset(submitDataToSend);

      if (res.status === 201) {
        setIsProcessingOpen(false);
        toast.success("Dataset Created Successfully!!");
        // Reset form
        setFormData({
          visibility: "private",
          title: "",
          description: "",
          tags: [],
          purpose: "",
          source: "",
          license: "",
          departments: [],
          projects: [],
          datasets: [],
          isWorldDataset: false,
        });
        setCheckedDepartments([]);
        setCheckedProjects([]);
        setIsPrivate(true);
        setCurrentStep(1);
        setIsOpen(false);

        if (!isrunninginbg) {
          if (isWorldDataset) {
            router.push(`/data/world-data/dataset/${res.data.dataset?._id}`);
          } else {
            router.push(`/data/company-data/dataset/${res.data.dataset?._id}`);
          }
        }
      }
    } catch (err) {
      //console.log(err);
      toast.error("Failed to create dataset");
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateForm = () => {
    if (!formData.title.trim()) {
      toast.error("Please enter a dataset title");
      return false;
    }
    if (!formData.departments || formData.departments.length === 0) {
      toast.error("Please select at least one department");
      return false;
    }
    if (formData.purpose === "project" && formData.projects.length === 0) {
      toast.error("Please select at least one project");
      return false;
    }
    return true;
  };

  const handleOnBack = () => {
    if (showDetails) {
      setShowDetails(false);
    } else setCurrentStep(currentStep - 1);
    setIsSubmitting(false);
  };

  useEffect(() => {
    if (selectedOptions.length > 0 && isProcessingOpen) {
      const timer = setTimeout(() => {
        handleSubmit();
      }, 6000);
      return () => clearTimeout(timer);
    }
  }, [selectedOptions, isProcessingOpen]);

  const handleDialogSave = (options: string[]) => {
    dispatch(setRunningInBgStatus({ status: false }));
    handleSubmit();
    setIsDialogOpen(false);
    setIsProcessingOpen(true);
  };

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [currentStep]);

  const handleProcessingComplete = () => {
    setIsProcessingOpen(false);
  };

  const scrollAtTopFunction = () => {
    if (scrollableContentRef.current) {
      scrollableContentRef.current.scrollTop = 0;
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (!event.target.closest(".department-dropdown")) {
        setIsDepartmentDropdownOpen(false);
      }
      if (!event.target.closest(".license-dropdown")) {
        setIsLicenseDropdownOpen(false);
      }
    };
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  return (
    <div className={className}>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss={false}
        draggable
        pauseOnHover={false}
      />
      <style jsx>{`
        .tooltip {
          position: relative;
          display: inline-block;
        }

        .tooltip .tooltiptext {
          visibility: hidden;
          minwidth: 300px;
          background-color: white;
          color: #00000;
          text-align: center;
          border-radius: 6px;
          padding: 8px;
          position: absolute;
          z-index: 10;
          bottom: 125%;
          left: 50%;
          margin-left: -40px;
          opacity: 0;
          transition: opacity 0.3s;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          font-size: 0.875rem;
          white-space: nowrap;
        }

        .tooltip:hover .tooltiptext {
          visibility: visible;
          opacity: 1;
        }
      `}</style>

      {/* Side Panel */}
      <div
        className={`z-50 fixed top-0 right-0 h-screen w-[calc(100%-210px)] bg-white shadow-lg transform ${isOpen ? "translate-x-0" : "translate-x-full"
          } transition-transform duration-300 ease-in-out overflow-hidden flex flex-col`}
      >
        {/* Top bar */}
        <div className="flex sticky h-12 justify-between bg-[#3B4154] text-white p-5 items-center">
          <h2 className="text-lg">{title}</h2>
          <button onClick={() => setIsOpen(false)} className="text-xl">
            <X />
          </button>
        </div>

        {/* Main form content */}
        <div className="flex-1 overflow-y-auto p-6" ref={scrollableContentRef}>
          <h1 className="text-xl w-3/4 font-medium text-[#3B4154] items-start mb-5">{title.replace("Add", "")}</h1>

          <StepIndicator currentStep={currentStep} />

          {currentStep === 1 && (
            <div className="mb-6 flex items-center justify-between">
              <div className="flex gap-2 items-center">
                <div className="flex items-center gap-2">
                  <label className="block text-gray-500 text-sm">Visibility</label>
                  <div className="tooltip">
                    <Info className="w-4 h-4 text-gray-400" />
                    <span className="tooltiptext">Toggle to make the dataset publicly available or private</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div
                    className={`flex items-center border rounded-full p-0.5 w-[48px] cursor-pointer transition-all duration-300 ${isPrivate ? "bg-teal-500" : "bg-gray-400"
                      }`}
                    onClick={toggleVisibility}
                  >
                    <div
                      className={`w-5 h-5 rounded-full bg-white shadow-md transform transition-transform duration-300 ${isPrivate ? "translate-x-[22px]" : "translate-x-0"
                        }`}
                    ></div>
                  </div>
                  <span className="text-m text-gray-800">{false ? "Private" : "Public"}</span>
                </div>
              </div>

              <span className="text-gray-500 text-sm">
                <span className="text-red-500 mr-1">*</span>
                Asterisks are mandatory fields
              </span>
            </div>
          )}

          {currentStep === 1 ? (
            <>
              {/* Title */}
              <div className="mb-6">
                <label className="mb-2 flex items-center text-gray-500 text-sm">
                  Title <span className="text-red-500">*</span>
                  <div className="tooltip ml-1">
                    <Info className="w-4 h-4 text-gray-400" />
                    <span className="tooltiptext text-black">Provide a clear title for your dataset</span>
                  </div>
                </label>
                <input
                  type="text"
                  className="w-full p-2 border rounded focus:outline-none focus:border-teal-500"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Eg. Sales Report"
                />
              </div>

              {/* Description */}
              <div className="mb-6">
                <label className="mb-2 flex items-center text-gray-500 text-sm">
                  Description
                  <div className="tooltip ml-1">
                    <Info className="w-4 h-4 text-gray-400" />
                    <span className="tooltiptext text-black">Add a detailed description of your dataset</span>
                  </div>
                </label>
                <textarea
                  className="w-full p-2 border rounded focus:outline-none focus:border-teal-500"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Eg. The dataset features key details..."
                  rows={4}
                />
              </div>

              {/* Department Type */}
              <div className="mb-6 relative department-dropdown">
                <label className="mb-2 flex items-center text-gray-500 text-sm">
                  Department Type <span className="text-red-500">*</span>
                  <div className="tooltip ml-1">
                    <Info className="w-4 h-4 text-gray-400" />
                    <span className="tooltiptext text-black">Select the relevant departments for the dataset</span>
                  </div>
                </label>
                <div
                  className="w-full p-2 border rounded focus:outline-none focus:border-teal-500 cursor-pointer flex justify-between items-center text-gray-500 text-sm"
                  onClick={() => setIsDepartmentDropdownOpen(!isDepartmentDropdownOpen)}
                >
                  <span>{checkedDepartments.length ? checkedDepartments.join(", ") : "Select Departments"}</span>
                  <ChevronDown className="w-4 h-4" />
                </div>
                {isDepartmentDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white border rounded shadow-lg">
                    {departmentList?.departments?.map((dept: any) => (
                      <div
                        key={dept._id}
                        className="p-2 hover:bg-gray-100 cursor-pointer flex items-center"
                        onClick={() => toggleDepartment(dept.name, dept._id)}
                      >
                        <Checkbox
                          id="Department"
                          label={dept.name}
                          checked={checkedDepartments.includes(dept.name)}
                          onChange={() => { }}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Tags */}
              <div className="mb-6 relative">
                <label className="mb-2 flex items-center text-gray-500 text-sm">
                  Tags
                  <div className="tooltip ml-1">
                    <Info className="w-4 h-4 text-gray-400" />
                    <span className="tooltiptext text-black">Enter relevant keywords to categorize your dataset</span>
                  </div>
                </label>
                <form onSubmit={handleTagSubmit}>
                  <input
                    type="text"
                    className="w-full p-2 border rounded focus:outline-none focus:border-teal-500"
                    value={searchTag}
                    onChange={handleTagSearch}
                    placeholder="Search tags..."
                  />
                </form>
                {isTagDropdownOpen && tagList?.tags?.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-white border rounded shadow-lg tag-dropdown">
                    {tagList.tags.map((tag: any) => (
                      <div
                        key={tag.name}
                        className="p-2 hover:bg-gray-100 cursor-pointer flex items-center"
                        onClick={() => toggleTag(tag.name)}
                      >
                        <input type="checkbox" checked={formData.tags.includes(tag.name)} readOnly className="mr-2" />
                        {tag.name}
                      </div>
                    ))}
                  </div>
                )}
                {formData.tags.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-4">
                    {formData.tags.map((tag) => (
                      <span
                        key={tag}
                        className="bg-[#F4F5F6] text-[#3B4154] px-2 py-1 rounded-[13px] text-sm flex items-center gap-1"
                      >
                        <Image src="/assets/icons/tag_icon.svg" alt="Warning Icon" width={15} height={15} />
                        {tag}
                        <button className="ml-1" onClick={() => toggleTag(tag)}>
                          <X className="h-4 w-4" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Purpose */}
              <div className="mb-6">
                <label className="mb-2 flex items-center text-gray-500 text-sm">
                  Select Purpose
                  <div className="tooltip ml-1">
                    <Info className="w-4 h-4 text-gray-400" />
                    <span className="tooltiptext text-black">Specify the purpose of dataset</span>
                  </div>
                </label>
                <div className="flex gap-4">
                  <button
                    className={`flex gap-1 items-center px-4 py-2 rounded ${formData.purpose === "project" ? "bg-teal-500 text-white" : "border border-teal-500"
                      }`}
                    onClick={() => {
                      setFormData({ ...formData, purpose: "project" });
                    }}
                  >
                    <Check className="w-5 h-5" />
                    <span>
                      For a Project
                    </span>
                  </button>
                  <button
                    className={`flex gap-1 items-center px-4 py-2 rounded ${formData.purpose === "general" ? "bg-teal-500 text-white" : "border border-teal-500 "
                      }`}
                    onClick={() => {
                      setFormData({ ...formData, purpose: "general" });
                    }}
                  >
                    <Check className="w-5 h-5" />
                    <span>
                      For General Use
                    </span>
                  </button>
                </div>
              </div>

              {/* Project selection (only if purpose=project) */}
              {formData.purpose === "project" && (
                <div className="mb-6 relative">
                  <label className="block mb-2 ">
                    Select Project(s) <span className="text-red-500">*</span>
                  </label>
                  <div
                    className="w-full p-2 border rounded focus:outline-none focus:border-teal-500 cursor-pointer flex justify-between items-center project-dropdown"
                    onClick={() => setIsProjectDropdownOpen(!isProjectDropdownOpen)}
                  >
                    <span>{checkedProjects.length ? checkedProjects.join(", ") : "Select Projects"}</span>
                    <ChevronDown className="w-4 h-4" />
                  </div>
                  {isProjectDropdownOpen && (
                    <div className="absolute z-10 w-full mt-1 bg-white border rounded shadow-lg project-dropdown">
                      {projectList?.map((project: any) => (
                        <div
                          key={project._id}
                          className="p-2 hover:bg-gray-100 cursor-pointer flex items-center"
                          onClick={() => toggleProject(project.name, project._id)}
                        >
                          <input
                            type="checkbox"
                            checked={checkedProjects.includes(project.name)}
                            onChange={() => { }}
                            className="mr-2"
                          />
                          {project.name}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Source & License */}
              <div className="mb-6 flex gap-4">
                <div className="w-1/2">
                  <label className="mb-2 flex items-center text-gray-500 text-sm">
                    Source
                    <div className="tooltip ml-1">
                      <Info className="w-4 h-4 text-gray-400" />
                      <span className="tooltiptext text-black">Provide the link or source of the dataset</span>
                    </div>
                  </label>
                  <input
                    type="text"
                    className="w-full p-2 border rounded focus:outline-none focus:border-teal-500"
                    value={formData.source}
                    onChange={(e) => setFormData({ ...formData, source: e.target.value })}
                    placeholder="Eg. https://example.com/dataset.json"
                  />
                </div>

                <div className="w-1/2 relative license-dropdown">
                  <div className="w-[100%]">
                    <label className="mb-2 flex items-center text-gray-500 text-sm">License</label>
                    <div
                      className="w-full p-2 border rounded focus:outline-none focus:border-teal-500 cursor-pointer flex justify-between items-center text-gray-500 text-sm"
                      onClick={() => setIsLicenseDropdownOpen(!isLicenseDropdownOpen)}
                    >
                      <span>{formData.license || "Select License"}</span>
                      <ChevronDown className="w-4 h-4" />
                    </div>
                    {isLicenseDropdownOpen && (
                      <div className="max-h-40 overflow-y-scroll absolute z-10 w-full mt-1 bg-white border rounded shadow-lg">
                        {licenseList?.tags?.map((license: any) => (
                          <div
                            key={license._id}
                            className="p-2 hover:bg-gray-100 cursor-pointer"
                            onClick={() => selectLicense(license.name)}
                          >
                            {license.name}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="mt-1">
                    <span className="mt-[3px]">
                      <div className="flex items-center gap-1 text-[#6e6e6e] text-[11px]">
                        <Info className="w-[12px] h-[12px] text-gray-800" />
                        License definitions and additional information can be found at{" "}
                        <a className="underline" href="http://opendefinition.org/licenses/">
                          opendefinition.org
                        </a>
                      </div>
                      <div className="w-[100%] mt-2a">
                        <p className=" text-[#6e6e6e] text-[11px] text-left mt-1">
                          The <i>data license</i> you select above only applies to the contents of any resource files
                          that you add to this dataset.
                        </p>
                      </div>
                    </span>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <>
              <AddDataStep
                formData={formData}
                setFormData={(data) =>
                  setFormData({
                    ...data,
                    isWorldDataset: formData.isWorldDataset,
                  })
                }
                setShowDetails={setShowDetails}
                showDetails={showDetails}
                scrollAtTopFunction={scrollAtTopFunction}
              />
            </>
          )}
        </div>

        {/* Bottom buttons */}
        <div className="bg-white border-t p-4 flex justify-end gap-4">
          <button className="px-4 py-2 border rounded" onClick={handleoncancel} disabled={isSubmitting}>
            Cancel
          </button>
          {currentStep > 1 && (
            <button className="px-4 py-2 border rounded" onClick={handleOnBack} disabled={isSubmitting}>
              Back
            </button>
          )}
          <button
            className={`px-4 py-2 ${currentStep === 2 && (!formData.datasets || formData.datasets.length === 0)
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-teal-500 hover:bg-teal-600"
              } text-white rounded flex items-center gap-2`}
            disabled={isSubmitting || (currentStep === 2 && (!formData.datasets || formData.datasets.length === 0))}
            onClick={async () => {
              if (currentStep === 1) {
                if (!validateForm()) {
                  return;
                }

                try {
                  const titleExist = await checkDatasetTitle(formData.title);
                  if (titleExist?.data?.exists) {
                    toast.error("Dataset title already exists");
                  } else {
                    setCurrentStep(2);
                    if (scrollableContentRef.current) {
                      scrollableContentRef.current.scrollTop = 0;
                    }
                  }
                } catch (error: any) {
                  toast.error(error?.data?.message || "An error occurred while checking the dataset title.");
                }
              } else if (formData.datasets && formData.datasets.length > 0) {
                setIsDialogOpen(true);
              }
            }}
          >
            {isSubmitting && <Loader2 className="w-4 h-4 animate-spin" />}
            {currentStep === 1 ? "Continue →" : "Save"}
          </button>
        </div>
      </div>

      {/* Checkbox Dialog */}
      <CheckboxDialog
        isOpen={isDialogOpen}
        selectedOptions={selectedOptions}
        setSelectedOptions={setSelectedOptions}
        onClose={() => setIsDialogOpen(false)}
        onSave={handleDialogSave}
      />

      {/* Processing Dialog */}
      <ProcessingDialog isOpen={isProcessingOpen} onClose={handleOnClose} onComplete={handleProcessingComplete} />
    </div>
  );
};

export default AddDataset;
