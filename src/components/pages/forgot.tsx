import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { authService } from "@/service/api";

const Forgot: React.FC = () => {
  const router = useRouter();
  const [usernameOrEmail, setUsernameOrEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");
  const [timer, setTimer] = useState(0);
  const [emailTouched, setEmailTouched] = useState(false);

  // Timer for resend link
  React.useEffect(() => {
    let interval: NodeJS.Timeout;
    if (timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [timer]);

  const isValidInput = (input: string) => {
    // Accepts either a valid email or a non-empty username
    return input && ( /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input) || input.length > 2 );
  };

  const handleSendResetLink = async () => {
    setLoading(true);
    setError("");
    try {
      await authService.sendResetLink({ usernameOrEmail });
      setSuccess(true);
      setTimer(30);
    } catch (err: any) {
      setError(err?.response?.data?.message || "No user found with that email or username, or error occurred.");
    } finally {
      setLoading(false);
    }
  };

  const handleProceed = (e: React.FormEvent) => {
    e.preventDefault();
    setEmailTouched(true);
    if (!isValidInput(usernameOrEmail)) {
      setError("Please enter a valid email address or username.");
      return;
    }
    handleSendResetLink();
  };

  return (
    <div className="flex flex-col md:flex-row h-screen w-full">
      {/* Left Section (Forgot Password Form) - 40% Width */}
      <div className="w-full md:w-[40%] bg-white flex flex-col justify-center items-center p-6 md:p-12 relative">
        <header className="flex items-center justify-center w-full px-4 h-12 relative">
          <div className="relative w-20 h-20 md:w-24 md:h-24">
            <Image src="/assets/Group 4355.svg" alt="Logo" layout="fill" objectFit="contain" />
          </div>
        </header>
        <div className="w-full max-w-sm mt-10 md:mt-20">
          {/* Back Button */}
          <button onClick={() => router.push('/login')} className="mb-4">
            <img 
              src="/assets/keyboard_backspace_24dp_FILL0_wght300_GRAD0_opsz24 (1).svg" 
              alt="Back"
              className="w-6 h-6"
            />
          </button>

          <h2 className="text-2xl font-semibold mb-4 text-gray-700">Forgot Password</h2>

          <form onSubmit={handleProceed}>
            <label className="block text-gray-600 text-sm mb-1">Username or email</label>
            <input
              type="text"
              className={`w-full border ${error ? 'border-red-500' : 'border-gray-400'} text-black rounded-md px-3 py-2 mb-4 focus:border-[#00B2A1] focus:ring-1 focus:ring-[#00B2A1] outline-none`}
              value={usernameOrEmail}
              onChange={(e) => { setUsernameOrEmail(e.target.value); setError(""); }}
              onBlur={() => setEmailTouched(true)}
              disabled={success}
              required
            />
            {error && <p className="text-red-500 text-sm mb-2">{error}</p>}
            <button
              type="submit"
              className={`w-full py-2 rounded-md text-white ${success ? 'bg-gray-300' : 'bg-[#00B2A1] hover:bg-[#009688]'} mb-2`}
              disabled={loading || success}
            >
              {loading ? 'Please wait...' : 'Proceed'}
            </button>
          </form>

          {/* Success message and resend link */}
          {success && (
            <div className="mt-4 flex items-start">
              <svg
                className="w-6 h-6 mr-2 mt-[2px]"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="12" cy="12" r="11" stroke="#00B2A1" strokeWidth="2" fill="white" />
                <path d="M8 12.5L11 15.5L16 9.5" stroke="#00B2A1" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              <div className="text-[#3B4154] text-base font-medium" style={{fontFamily: "Lato, sans-serif"}}>
                A password reset link has been sent to your registered email ID.
                <button
                  className="ml-2 text-[#00B2A1] text-base font-medium hover:underline disabled:text-gray-400"
                  onClick={handleSendResetLink}
                  disabled={timer > 0}
                  style={{ cursor: timer > 0 ? 'not-allowed' : 'pointer' }}
                >
                  Resend Link
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right Section (Background Image) - 60% Width */}
      <div 
        className="w-full md:w-[60%] bg-black flex flex-col justify-end text-white p-6 md:p-10 relative"
        style={{ 
          backgroundImage: "url('/assets/Mask Group 12.png')", 
          backgroundSize: "cover", 
          backgroundPosition: "center" 
        }}
      >
        <div className="text-left pb-10">
          <h2 className="text-xl font-semibold mb-4 text-[#00B2A1]">
            AI for Business Builders
          </h2>
          <p className="text-gray-400">
            Discover and actualize innovative new business opportunities and applications, 
            driving ongoing substantial business growth.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Forgot;
