import { Button } from "@/components/ui/button/button";
import { setActiveManageTab } from "@/lib/store/features/project/projectManageSlice";
import { setProjectActiveTab } from "@/lib/store/features/project/projectTabSlice";
import {
  useNotificationCount,
  useNotifications,
} from "@/service/hooks/useNotification";
// import useWebSocket from "@/service/hooks/useWebSocket";
import { X, Bell, MessageSquare, Rocket } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { MdPerson } from "react-icons/md";
import { useDispatch } from "react-redux";

interface notificationIntercept { 
  showLabel?: boolean;
  isActive?: boolean;
  className?: string;
}

const Notification: React.FC<notificationIntercept> = ({ showLabel = false, isActive = false, className = "" }) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { count, setCount } = useNotificationCount();
  const [isOpen, setIsOpen] = useState(false);
  const {
    notifications,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    hasMore,
    loadMore,
  } = useNotifications();
  const [groupedNotifications, setGroupedNotifications] = useState<
    Record<string, any[]>
  >({});

  const handleNotificationClick = (
    notifyId: string,
    type: string,
    projectId: string
  ) => {
    setIsOpen(false);
    markAsRead(notifyId);
    switch (type) {
      case "collaborator_added":
        router.push(`/projects/dashboard/${projectId}`);
        dispatch(setProjectActiveTab("Manage"));
        dispatch(setActiveManageTab("Collaborators"));
        return;
      case "project_status_changed":
        router.push(`/projects/dashboard/${projectId}`);
        dispatch(setProjectActiveTab("Overview"));
        dispatch(setActiveManageTab("General"));
        return;
      case "system_maintenance":
        dispatch(setProjectActiveTab("Overview"));
        dispatch(setActiveManageTab("General"));
        return;
      case "comment_added":
        router.push(`/projects/dashboard/${projectId}`);
        dispatch(setProjectActiveTab("Overview"));
        dispatch(setActiveManageTab("General"));
        return;
      case "project_deployed":
        router.push(`/apps/deployed-projects`);
        dispatch(setProjectActiveTab("Overview"));
        dispatch(setActiveManageTab("General"));
        return;
      default:
        if (projectId) router.push(`/projects/dashboard/${projectId}`);
        dispatch(setProjectActiveTab("Overview"));
        dispatch(setActiveManageTab("General"));
        return;
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"; // Prevent background scrolling
    } else {
      document.body.style.overflow = ""; // Restore background scrolling
    }
    return () => {
      document.body.style.overflow = ""; // Cleanup on unmount
    };
  }, [isOpen]);

  // Group notifications by date
  useEffect(() => {
    if (!notifications) return;

    const grouped: Record<string, any[]> = {};

    notifications.forEach((notification) => {
      const date = new Date(notification.createdAt);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      let dateKey;

      if (date.toDateString() === today.toDateString()) {
        dateKey = "Today";
      } else if (date.toDateString() === yesterday.toDateString()) {
        dateKey = "Yesterday";
      } else {
        dateKey = date.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
          year: "numeric",
        });
      }

      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }

      grouped[dateKey].push(notification);
    });

    setGroupedNotifications(grouped);
  }, [notifications]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "collaborator_added":
        return (
          <img src="/assets/Group_7093.svg" alt="user" className="w-8 h-8" />
        );
      case "project_status_changed":
        return (
          <img src="/assets/Group_7099.svg" alt="user" className="w-8 h-8" />
        );
      case "system_maintenance":
        return (
          <img src="/assets/Group_7101.svg" alt="user" className="w-8 h-8" />
        );
      case "comment_added":
        return <MessageSquare className="h-6 w-6 text-gray-500" />;
      case "project_deployed":
        return <Rocket className="h-6 w-6 text-gray-500" />;
      default:
        return <Bell className="h-6 w-6 text-gray-500" />;
    }
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    if (scrollHeight - scrollTop <= clientHeight * 1.5) {
      if (hasMore && !loading) {
        loadMore();
      }
    }
  };

  return (
    <div className="font-[Lato] z-50">
      <button
        onClick={() => setIsOpen(true)}
        className={`${showLabel ? 'w-full h-full flex items-center px-4 text-left' : 'flex justify-center items-center h-12'} ${className}`}
      >
        {count > 0 ? (
          <img
            src="/assets/icons/notification-on.svg"
            alt="New-Notification"
            className={`w-7 h-7 ${isActive ? 'filter-teal-800' : ''} ${showLabel ? 'mr-2' : ''}`}
          />
        ) : (
          <img
            src="/assets/icons/notification-off.svg"
            alt="Notification"
            className={`w-7 h-7 ${isActive ? 'filter-teal-800' : ''} ${showLabel ? 'mr-2' : ''}`}
          />
        )}
        {showLabel && (
          <span className={`font-semibold text-base ${isActive ? 'text-teal-800' : ''}`}>
            Notifications
          </span>
        )}
      </button>

      {/* Overlay (Optional) */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={() => setIsOpen(false)}
        />
      )}
      <div
        className={`font-[Lato] z-50 fixed top-0 right-0 h-screen w-[40%] bg-white shadow-lg transform ${isOpen ? "translate-x-0" : "translate-x-full"
          } transition-transform duration-300 ease-in-out flex flex-col pb-5`}
      >
        <div className="sticky top-0 z-10 flex flex-col   ">
          <div className="flex items-center h-12 justify-between w-full px-4 bg-[#3B4154]">
            <h2 className="text-lg font-medium text-white">
              Your Notifications
            </h2>
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-white/20"
              onClick={() => setIsOpen(false)}
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
          <button
            className="text-teal-500 text-sm flex justify-end px-5 pt-3"
            onClick={() => {
              markAllAsRead(setCount);
            }}
          >
            Mark all as Read
          </button>
        </div>

        <div
          className="flex-1 overflow-y-auto mt-4 pb-4"
          onScroll={handleScroll}
        >
          {loading && notifications.length === 0 ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : error ? (
            <div className="text-red-500 p-4 text-center">
              Error loading notifications
            </div>
          ) : notifications.length === 0 ? (
            <div className="text-gray-500 p-4 text-center">
              No notifications yet
            </div>
          ) : (
            Object.entries(groupedNotifications).map(
              ([date, dateNotifications], index) => (
                <div key={index} className="mb-6">
                  <h3 className="text-sm font-semibold bg-[#F4F5F6] text-[#3B4154] mb-2 px-4 py-2">
                    {date}
                  </h3>

                  {dateNotifications.map((notification, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between px-5 py-3 mb-2 rounded-lg hover:bg-gray-50 relative cursor-pointer"
                      onClick={() =>
                        handleNotificationClick(
                          notification._id,
                          notification.type,
                          notification?.relatedId
                        )
                      }
                    >
                      <div className=" mr-3">
                        {getNotificationIcon(notification.type)}
                      </div>

                      <div className="flex-1">
                        <div
                          dangerouslySetInnerHTML={{
                            __html: notification.message.replace(
                              /"([^"]+)"/g,
                              '<span class="text-teal-500">"$1"</span>'
                            ),
                          }}
                        />
                        <p className="text-sm text-gray-500 mt-1">
                          {new Date(notification.createdAt).toLocaleDateString(
                            "en-GB",
                            {
                              day: "2-digit",
                              month: "short",
                              year: "numeric",
                            }
                          )}
                        </p>
                      </div>

                      {!notification.isRead && (
                        <div className="h-2 w-2 rounded-full bg-teal-500"></div>
                      )}
                    </div>
                  ))}
                </div>
              )
            )
          )}
          {loading && notifications.length > 0 && (
            <div className="flex justify-center items-center h-16">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
            </div>
          )}

          {!loading && !hasMore && notifications.length > 0 && (
            <div className="text-gray-500 text-sm text-center p-4">
              No more notifications
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Notification;
