// ProcessedDataSummary.tsx
import React, { JSX } from "react";

interface FileContentOntology {
  file_name: string;
  read_status: string;
  file_columns: string[];
  time_range: {
    earliest_date: string;
    latest_date: string;
  };
  geo_location: any[];
  language: string;
  data_industry: any[];
  data_type: string;
  intended_use_case: any[];
  ai_ml_use_case: any[];
  file_content_summary: string;
  file_search_tags: string[];
  file_relevance_with_title: {
    is_relevant: string;
    relevance_comment: string;
  };
}

interface Ontology {
  entity_type: string;
  parent_entity_type: string;
  grand_parent_entity_type: string;
}

interface DataPlaceResponse {
  row_index?: number; // Optional, as we'll ignore it
  tags?: string[]; // Optional, as we'll ignore it
  unified_data_summary?: string; // Optional, as it might not always be present
  ontology?: Ontology;
  files_content_ontologies?: FileContentOntology[];
  [key: string]: any; // Allow dynamic keys for flexibility
}

const ProcessedDataSummary: React.FC<{ dataplaceResponse: DataPlaceResponse }> = ({ dataplaceResponse: data }) => {
  // Skip row_index and tags, handle unified_data_summary at the end as a simple field-value pair
  const { row_index, tags, unified_data_summary, ...rest } = data;

  // Helper to generate a unique key for any field or item
  const generateUniqueKey = (prefix: string, identifier: string | number): string => {
    return `${prefix}-${identifier}`;
  };

  // Helper to render field-value pairs
  const renderFieldValue = (field: string, value: any, isParent: boolean = false): JSX.Element | null => {
    if (value === null || value === undefined) return null;

    if (typeof value === "string") {
      return (
        <div
          key={generateUniqueKey(field, "string")}
          className="flex justify-between py-[10px] text-[#333333] text-[14px]"
        >
          <div className={`flex-initial w-[200px] ${isParent ? "font-bold" : ""}`}>{formatTitle(field)}</div>
          <div className="flex-auto w-[200px]">{processValue(value)}</div>
        </div>
      );
    }

    if (Array.isArray(value)) {
      if (value.length > 0 && typeof value[0] === "object") {
        // Handle array of objects (e.g., files_content_ontologies)
        return (
          <div key={generateUniqueKey(field, "array")} className="text-[#333333]">
            <div className="font-bold py-2 text-[18px]">{formatTitle(field)}</div>
            {value.map((item: any, index: number) => (
              <div key={generateUniqueKey(field, item.file_name || index)}>
                {Object.entries(item).map(([subField, subValue]: [string, any]) =>
                  renderFieldValue(subField, subValue)
                )}
              </div>
            ))}
            <div className="border-t border-gray-300 my-2"></div>
          </div>
        );
      } else {
        // Handle array of strings (converted to comma-separated string)
        return renderFieldValue(field, arrayToCommaString(value));
      }
    }

    if (typeof value === "object") {
      // Handle nested objects (e.g., ontology)
      return (
        <div key={generateUniqueKey(field, "object")}>
          <div className="font-bold py-2 text-[18px] text-[#333333]">{formatTitle(field)}</div>
          {Object.entries(value).map(([subField, subValue]: [string, any]) => renderFieldValue(subField, subValue))}
          <div className="border-t border-gray-300 my-2"></div>
        </div>
      );
    }

    return null; // Default case for unexpected types
  };

  return (
    <div className="pt-4 max-w-2xl">
      <div className="text-[20px] mb-2 text-[#3B4154] pb-[10px] font-semibold">Processed Data Summary</div>
      <div className="flex justify-between py-2 text-[14px] text-[#333333]">
        <div className="flex-initial w-[200px] font-semibold">Attributes</div>
        <div className="flex-auto w-[200px] font-semibold">Values</div>
      </div>
      <div className="border-t-2 border-[#ccc] mb-1"></div>

      {/* Render all fields except row_index, tags, and unified_data_summary */}
      {Object.entries(rest).map(([field, value]: [string, any]) => (
        <React.Fragment key={generateUniqueKey(field, "section")}>{renderFieldValue(field, value)}</React.Fragment>
      ))}

      {/* Render unified_data_summary at the end as a simple field-value pair */}
      {unified_data_summary && (
        <div className="flex justify-between py-2 text-[#333333]">
          <div className="flex-initial w-[200px] font-bold text-[18px]">{formatTitle("unified_data_summary")}</div>
          <div className="flex-auto w-[200px] text-[14px]">{processValue(unified_data_summary)}</div>
        </div>
      )}

      {/* Optional: Render tags as a comma-separated string if needed elsewhere */}
      {tags && (
        <div className="flex justify-between py-2 mt-4 text-[#333333]">
          <div className="flex-initial w-[200px] font-bold text-[18px]">{formatTitle("tags")}</div>
          <div className="flex-auto w-[200px] text-[14px]">{arrayToCommaString(tags)}</div>
        </div>
      )}
    </div>
  );
};

export default ProcessedDataSummary;

// dataUtils.ts
export const formatTitle = (key: string): string => {
  return key
    .replace(/_/g, " ")
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
};

export const arrayToCommaString = (arr: any[] | undefined): string => {
  return Array.isArray(arr) ? arr.join(", ") : "Not generated";
};

export const processValue = (value: any): string => {
  if (value === null || value === undefined) return "Not generated";
  if (typeof value === "string") return value;
  if (Array.isArray(value)) return arrayToCommaString(value);
  if (typeof value === "object") return JSON.stringify(value); // For nested objects, stringify for display
  return value.toString();
};
