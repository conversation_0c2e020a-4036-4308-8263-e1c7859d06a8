"use client";

import { Search } from "lucide-react";
import { Badge } from "@/components/ui/badge/badge";
import { Button } from "@/components/ui/button/button";
import Input from "@/components/ui/search-input/search-input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { dataService } from "@/service/dataService";

type DataAssetItemProps = {
  title: string;
  description: string;
  format?: string;
  formats?: string[];
  isImported?: boolean;
};

function DataAssetItem({
  title,
  description,
  format,
  formats,
  isImported = false,
}: DataAssetItemProps) {
  return (
    <div className="border border-[#e4e4e4] rounded-lg p-4">
      <div className="flex justify-between items-start mb-2">
        <h4 className="font-medium text-[#333333] flex-1 pr-4">{title}</h4>
        {isImported ? (
          <Button
            size="sm"
            className="bg-[#e4e4e4] text-[#666666] cursor-not-allowed"
            disabled
          >
            Imported
          </Button>
        ) : (
          <Button
            size="sm"
            className="bg-[#00b2a1] hover:bg-[#018e42] text-white"
          >
            Import
          </Button>
        )}
      </div>

      <p className="text-sm text-[#666666] mb-3">{description}</p>

      <div className="flex gap-2">
        {formats &&
          formats.map((fmt) => (
            <Badge
              key={fmt}
              variant="secondary"
              className="bg-[#f0f2f8] text-[#666666]"
            >
              {fmt}
            </Badge>
          ))}
      </div>
    </div>
  );
}

export default function ExploreWorldSection() {
  const [searchQuery, setSearchQuery] = useState("");
  const [dataAssets, setDataAssets] = useState<DataAssetItemProps[]>([]);
  const [page, setPage] = useState(1);
  const router = useRouter();

  useEffect(() => {
    const fetchDataAssets = async () => {
      try {
        const res = await dataService.getExternalSearchData(page);
        const data = await res.data.data;
        console.log("Fetched Data Assets ID:", data[0].did);
        const filteredData = data.filter((item: any) => !item.isImported);
        // Now, if filteredData is empty then we recall the API with next page
        if (filteredData.length === 0) {
          const hasNextPage = res.data.count > page * 10;
          if (hasNextPage) {
            setPage((prev) => prev + 1);
          }
          return;
        }
        // Map the data to the required format
        const dataParsed = filteredData.map((item: any) => ({
          title: item.title,
          description: item.description.substring(0, 250) + "...",
          format: item.filetypes[0] || "Unknown",
          formats: item.filetypes.length > 1 ? item.filetypes : undefined,
        }));
        setDataAssets(dataParsed);
      } catch (error) {
        console.error("Error fetching data assets:", error);
      }
    };
    fetchDataAssets();
  }, [page]);

  const handleOnSearchClick = () => {
    const url = `/data/world-data?q=${searchQuery}`;
    router.push(url);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="w-5 h-5" />
          Explore World Data
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#888888] w-4 h-4" />
          <Input
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
            }}
            onSearchClick={handleOnSearchClick}
            placeholder="Search for Data Assets"
            className="pl-10 bg-[#f0f2f8] border-[#cfd2de]"
          />
        </div>

        <div className="space-y-4">
          <h4 className="font-medium text-[#333333]">Suggested Data Assets</h4>

          {dataAssets.length > 0 ? (
            dataAssets.map((asset, index) => (
              <DataAssetItem
                key={index}
                title={asset.title}
                description={asset.description}
                format={asset.format}
                formats={asset.formats}
              />
            ))
          ) : (
            <p className="text-[#888888]">No data assets found.</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
