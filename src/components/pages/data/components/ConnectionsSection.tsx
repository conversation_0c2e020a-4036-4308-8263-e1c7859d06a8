"use client"

import { Database } from "lucide-react"
import { Badge } from "@/components/ui/badge/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useEffect, useState } from "react"
import { DatasageService } from "@/service/datasage"
import { circIn } from "framer-motion"

type ConnectionItemProps = {
  name: string
  type: string
  category: string
  status: string
  statusColor: string
  critical?: boolean
  failureReason?: string
  lastSync?: string
}

function ConnectionItem({
  name,
  type,
  category,
  status,
  statusColor,
  critical = false,
  failureReason,
  lastSync,
}: ConnectionItemProps) {
  if (critical) {
    return (
      <div className="border border-[#dc2625] bg-[#fee2e1] rounded-lg p-4">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className="text-sm font-bold text-[#333333]">{name}</span>
            <Badge className="bg-[#dc2625] text-white">Critical</Badge>
          </div>
          <span className={`text-sm ${statusColor}`}>{status == "Online" ? "Active" : "Inactive"}</span>
        </div>
        <p className="text-sm text-[#666666] mb-2">Last Sync: {lastSync}</p>
        <p className="text-sm text-[#666666]">{failureReason}</p>
      </div>
    );
  } else {
    return (
      <div className="border border-[#e4e4e4] rounded-lg p-4">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className="text-sm font-bold text-[#333333]">{name}</span>
          </div>
          <Badge variant="destructive" className={`relative top-8 right-2 text-sm bg-[#DCFCE7] !rounded-[16px] ${statusColor}`}>• {status == "Online" ? "Active" : "Inactive"}</Badge>
        </div>
        <p className="text-sm text-[#666666] mb-2">DBMS: {type}</p>
        <p className="text-sm text-[#666666]">Category: {category}</p>
      </div>
    );
  }
}

export default function ConnectionsSection() {
  const [connections, setConnections] = useState<ConnectionItemProps[]>([]);

  useEffect(() => {
    const fetchConnections = async () => {
      try {
        const res = await DatasageService.getDatasageConnections();
        const data = res.data.data;
        const fetchedConnections = data.map((i: any) => ({
          name: i.source_name,
          type: i.description,
          category: i.source_type,
          status: i.connection_status,
          statusColor: i.connection_status === "Online" ? "text-[#018e42]" : "text-[#dc2625]",
          critical: i.critical || false,
          failureReason: i.downtime_reason,
          lastSync: i.last_checked || "N/A",
        }));
        // console.log("Fetched Connections:", data);
        setConnections(fetchedConnections);
      } catch (error) {
        console.error("Error fetching connections:", error);
      }
    };
    fetchConnections();
  }, []);
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5" />
          Connections
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {connections.length > 0 ? (
          connections.map((conn, index) => (
            <ConnectionItem
              key={index}
              name={conn.name}
              type={conn.type}
              category={conn.category}
              status={conn.status}
              statusColor={conn.statusColor}
              critical={conn.critical}
              failureReason={conn.failureReason}
              lastSync={conn.lastSync}
            />
          ))
        ) : (
          <p className="text-sm text-[#666666]">No connections available.</p>
        )}
      </CardContent>
    </Card>
  )
}
