"use client"

import { useState } from "react"
import { AlertTriangle, ExternalLink, Plus, ChevronDown, ChevronUp } from "lucide-react"
import { Badge } from "@/components/ui/badge/badge"
import { Button } from "@/components/ui/button/button"
import { Progress } from "@/components/ui/progress/progress"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

type ActionItemProps = {
  type: string
  title: string
  subtitle: string
  status: string
  statusColor: string
  progress?: number
  note?: string
  showActions?: boolean
}

function ActionItem({
  type,
  title,
  subtitle,
  status,
  statusColor,
  progress,
  note,
  showActions = false,
}: ActionItemProps) {
  const typeColors = {
    Critical: "bg-[#fee2e1] text-[#dc2625]",
    High: "bg-[#ffedd5] text-[#9a3413]",
    Medium: "bg-[#fef9c3] text-[#854d0f]",
  }

  return (
    <div className="border border-[#e4e4e4] rounded-lg p-4">
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          <Badge className={typeColors[type as keyof typeof typeColors]}>{type}</Badge>
          <span className="text-sm font-medium text-[#333333]">{title}</span>
        </div>
        {showActions && (
          <div className="flex gap-2">
            <Button variant="ghost" size="sm" className="text-[#00b2a1] hover:text-[#018e42]">
              <ExternalLink className="w-4 h-4 mr-1" />
              Go to Project
            </Button>
            <Button size="sm" className="bg-[#00b2a1] hover:bg-[#018e42] text-white">
              <Plus className="w-4 h-4 mr-1" />
              Upload Data
            </Button>
          </div>
        )}
      </div>

      <p className="text-sm text-[#666666] mb-2">{subtitle}</p>
      <p className={`text-sm text-[#666666] mb-2`}>{note}</p>

      {progress && (
        <div className="mb-2">
          <Progress 
            value={progress} 
            className="h-1 mb-1 bg-[#f0f2f8]" 
            indicatorClassName="bg-[#3C82F6]" 
          />
          <div className="flex justify-between text-xs text-[#666666]">
            <span className={statusColor} >{status}</span>
            <span>{progress}%</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default function ActionSection() {
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <Card className="mb-6">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-[#eaa23b]" />
            <CardTitle className="text-lg">Action Needed</CardTitle>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="bg-[#fee2e1] text-[#dc2625]">
              3 Items
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              className="p-0 hover:bg-transparent"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <ChevronUp className="h-5 w-5 text-[#666666]" />
              ) : (
                <ChevronDown className="h-5 w-5 text-[#666666]" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      {isExpanded && (
        <CardContent className="space-y-4">
          <ActionItem
            type="Critical"
            title="SAP ERP - Production"
            subtitle="Last Sync: 1 day ago"
            status="Authentication failed"
            statusColor="text-[#dc2625]"
          />

        <ActionItem
          type="High"
          title="Inventory"
          subtitle="Project: Stock Forecast"
          note="Data Completion"
          status="1 file needed"
          statusColor="text-[#eaa23b]"
          progress={81}
          showActions
        />

        <ActionItem
          type="Medium"
          title="Part Number"
          subtitle="Project: Quote App"
          note="Data Completion"
          status="1 file needed"
          statusColor="text-[#eaa23b]"
          progress={77}
          showActions
        />
      </CardContent>)}
    </Card>
  )
}
