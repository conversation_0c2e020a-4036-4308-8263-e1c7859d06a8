"use client";

import { useRef } from "react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { ChevronRight } from "lucide-react";
import { AIBoxModel, AIProject } from "@/service/types/types";
import { getAiBoxes, getAiBoxResponse } from "@/service/aiServices";
import Loader from "@/components/common/loader/loader";
import { TbSparkles } from "react-icons/tb";

const demoData: AIProject[] = [
  {
    _id: "dummy-6",
    name: "Supply Chain Predictor",
    description: "Optimize inventory with AI-powered demand forecasting",
    thumbnailUrl: "https://placehold.co/50x50/brown/white?text=SC",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-3",
    name: "Sales Forecaster",
    description: "Predict future sales with machine learning models",
    thumbnailUrl: "https://placehold.co/50x50/orange/white?text=SF",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-1",
    name: "Smart Customer Support",
    description: "AI-powered customer service automation with sentiment analysis",
    thumbnailUrl: "https://placehold.co/50x50/teal/white?text=CS",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-2",
    name: "Document Analyzer",
    description: "Extract insights from documents using advanced NLP",
    thumbnailUrl: "https://placehold.co/50x50/purple/white?text=DA",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-4",
    name: "Email Campaign Optimizer",
    description: "Improve email marketing with AI-driven content suggestions",
    thumbnailUrl: "https://placehold.co/50x50/blue/white?text=EC",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-5",
    name: "HR Talent Matcher",
    description: "Match job candidates to positions using AI analysis",
    thumbnailUrl: "https://placehold.co/50x50/green/white?text=HR",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-7",
    name: "Financial Fraud Detector",
    description: "Identify suspicious transactions with machine learning",
    thumbnailUrl: "https://placehold.co/50x50/red/white?text=FD",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-8",
    name: "Market Trend Analyzer",
    description: "Track industry trends with AI-powered data analysis",
    thumbnailUrl: "https://placehold.co/50x50/indigo/white?text=MT",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-9",
    name: "Social Media Manager",
    description: "Schedule and optimize posts with AI content suggestions",
    thumbnailUrl: "https://placehold.co/50x50/pink/white?text=SM",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-10",
    name: "Customer Churn Predictor",
    description: "Identify at-risk customers before they leave",
    thumbnailUrl: "https://placehold.co/50x50/cyan/white?text=CP",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-11",
    name: "Legal Document Assistant",
    description: "Analyze contracts and legal documents with AI",
    thumbnailUrl: "https://placehold.co/50x50/gray/white?text=LA",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-12",
    name: "Product Recommendation Engine",
    description: "Boost sales with personalized product suggestions",
    thumbnailUrl: "https://placehold.co/50x50/yellow/black?text=PR",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-13",
    name: "Meeting Summarizer",
    description: "Automatically generate summaries from meeting transcripts",
    thumbnailUrl: "https://placehold.co/50x50/violet/white?text=MS",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-14",
    name: "Expense Report Analyzer",
    description: "Automate expense approval with AI policy checking",
    thumbnailUrl: "https://placehold.co/50x50/lime/black?text=ER",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-15",
    name: "Customer Segmentation Tool",
    description: "Create data-driven customer segments for targeted marketing",
    thumbnailUrl: "https://placehold.co/50x50/navy/white?text=CS",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-16",
    name: "Pricing Optimizer",
    description: "Set optimal prices based on market data and demand",
    thumbnailUrl: "https://placehold.co/50x50/maroon/white?text=PO",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-17",
    name: "Employee Engagement Analyzer",
    description: "Measure and improve workplace satisfaction with AI insights",
    thumbnailUrl: "https://placehold.co/50x50/olive/white?text=EE",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-18",
    name: "Product Feedback Analyzer",
    description: "Extract actionable insights from customer reviews",
    thumbnailUrl: "https://placehold.co/50x50/teal/white?text=PF",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-19",
    name: "Competitive Intelligence Tool",
    description: "Monitor competitors with AI-powered web analysis",
    thumbnailUrl: "https://placehold.co/50x50/purple/white?text=CI",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-20",
    name: "Project Risk Assessor",
    description: "Identify potential project risks with predictive analytics",
    thumbnailUrl: "https://placehold.co/50x50/orange/white?text=RA",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-21",
    name: "Business Process Optimizer",
    description: "Streamline workflows with AI-powered process analysis",
    thumbnailUrl: "https://placehold.co/50x50/blue/white?text=BP",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-22",
    name: "Customer Journey Mapper",
    description: "Visualize and optimize customer touchpoints with AI",
    thumbnailUrl: "https://placehold.co/50x50/green/white?text=CJ",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-23",
    name: "Data Quality Monitor",
    description: "Ensure data accuracy with AI-powered validation",
    thumbnailUrl: "https://placehold.co/50x50/red/white?text=DQ",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-24",
    name: "Compliance Checker",
    description: "Ensure regulatory compliance with AI document analysis",
    thumbnailUrl: "https://placehold.co/50x50/indigo/white?text=CC",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  },
  {
    _id: "dummy-25",
    name: "Knowledge Base Assistant",
    description: "Create and maintain company knowledge with AI assistance",
    thumbnailUrl: "https://placehold.co/50x50/pink/white?text=KB",
    version: "1.0.0",
    status: "active",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    repository: "",
    instanceDetails: {
      endpoint: "",
      healthCheck: ""
    },
    tags: [],
    dependencies: [],
    configuration: {
      inputSize: 0,
      confidenceThreshold: 0
    },
    readme: "",
    issues: [],
    changeLogs: []
  }
];

export default function AIBoxSection() {
  const scrollRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [aiBox, setAiBox] = useState<getAiBoxResponse | null>(null);

  // Fetch projects from API
  useEffect(() => {
    const fetchAIProjects = async () => {
      try {
        setLoading(true);
        const aiData = await getAiBoxes();
        // Combine API data with demo data
        // const combinedData = {
        //   ...aiData,
        //   data: [...aiData.data, ...demoData]
        // };
        setAiBox(aiData);
      } catch (err: any) {
        // console.log(err);
      } finally {
        setLoading(false);
      }
    };
    fetchAIProjects();
  }, []);

  return (
    <div className="bg-[#F5F5F5] relative w-full  mx-auto px-8 py-4">
      {loading ? (
        <Loader />
      ) : (
        <>
          {/* header section */}
          <div className="text-lg text-[#3B4154] font-semibold">
            Discover AI-In-A-Box Projects
          </div>
          <div className="text-sm text-[#666F8F] mb-4 mt-1">
            Explore hundreds of pre-built generative AI projects for your business and deploy them effortlessly — no
            coding required.
          </div>
          <div ref={scrollRef} className="flex flex-nowrap overflow-x-auto scroll-smooth gap-4 p-2 no-scrollbar">
            {aiBox?.data.map((card, index) => (
              <div
                key={card._id || `dummy-${index}`}
                onClick={() => router.push(`/projects/ai-project/${card._id}`)}
                className="w-60 min-w-60 max-w-60 min-h-64 flex flex-col bg-white rounded-[16px] overflow-hidden border border-[#CFD2DE] cursor-pointer hover:border-[#00B2A1]"
              >
                <div className="bg-gradient-to-br h-[8px] px-4 flex items-center text-[12px] text-white from-[#E36A64] to-[#ECBDA4]"></div>
                {/* <img
                className="object-cover"
                src={card.thumbnailUrl}
                height={50}
                width={50}
                alt="Thumbnail"
              /> */}
                <div className="flex flex-col items-start px-4 my-4">
                  <h3 className="font-semibold text-xl mt-2 mb-1">{card.name}</h3>
                  <p className="text-gray-600 text-sm mt-1">{card.description}</p>
                </div>
                <div className="bg-gray-100 w-fit rounded-full px-2 mx-4 flex items-center text-sm text-gray-400">
                  <TbSparkles className="w-4 h-4 mr-2 text-gray-400" /> AI-In-A-Box
                </div>
              </div>
            ))}
            {aiBox && aiBox.data && aiBox?.data?.length > 6 && (
              <div
                onClick={() => router.push("/projects/all-ai-projects")}
                className="min-w-[150px] flex flex-col items-center justify-center cursor-pointer text-white transition"
              >
                <div className="w-6 h-6 flex items-center justify-center bg-teal-500 rounded-full">
                  <ChevronRight className="w-4 h-4" />
                </div>
                <span className="mt-2 text-sm font-medium text-teal-500">View all</span>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
