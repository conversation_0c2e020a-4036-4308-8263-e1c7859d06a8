import React, { useState, useEffect } from "react";
import Image from "next/image";
import { ProjectDetails } from "@/service/types/types";
import { Info, ChevronDown, X, Pause, Trash, Edit, Play } from "lucide-react";
import { datasetService } from "@/service/datasetService";
import { deleteProject, updateProject } from "@/service/projects";
import DeleteProjectDialog from "@/components/common/DeleteProjectDialog/delete_project_dialog";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { ToastState } from "./manage";
import { Toast } from "@/components/ui/toast/toast";
import Checkbox from "@/components/ui/checkbox/page";

export interface UpdateProjectDetails {
  description: string;
  tags: string[];
  expectedReturn: string;
  department: string[];
  sensitive: boolean;
}

const GeneralTab = ({
  projectDetails,
  fetchProjectDetails
}: {
  projectDetails: ProjectDetails | null;
  fetchProjectDetails: () => void;
}) => {
  const router = useRouter();
  const [formData, setFormData] = useState<UpdateProjectDetails>({
    description: projectDetails?.description || "",
    tags: projectDetails?.tags || [],
    expectedReturn: projectDetails?.expectedReturn || "",
    sensitive: projectDetails?.sensitive || false,
    department: projectDetails?.department || [],
  });
  const [isReadOnly, setIsReadOnly] = useState(true);
  const [holdProject, setHoldProject] = useState(
    projectDetails?.status === "Hold" ? true : false || false
  );
  const [loadingHold, setLoadingHold] = useState(false);
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [loadingUpdate, setLoadingUpdate] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDepartmentDropdownOpen, setIsDepartmentDropdownOpen] =
    useState(false);
  const [departmentList, setdepartmentList] = useState<any>(null);
  const [toast, setToast] = useState<ToastState>(null);

  const [exploreUrlQuery, setExploreUrlQuery] = useState({
    page: 1,
    limit: 10,
    search: "",
  });
  const checkAccess = () => {
    const userInfo = useSelector((state: any) => state.user.userInfo);
    return projectDetails?.creator._id === userInfo.user._id;
  };
  const isEditEnable = projectDetails?.isAiBox
    ? checkAccess()
    : projectDetails?.currentUserRole?.userRole == "Executive" ||
    projectDetails?.currentUserRole?.userRole == "Creator";

  // console.log(isEditEnable);
  const fetchExploreData = async () => {
    try {
      const result = await datasetService.getDepartmentList(exploreUrlQuery);
      setdepartmentList(result.data);
    } catch (err) {
      // console.log("Failed to fetch data.");
    }
  };
  useEffect(() => {
    fetchExploreData();
  }, [exploreUrlQuery]);
  useEffect(() => {
    setFormData({
      description: projectDetails?.description || "",
      tags: projectDetails?.tags || [],
      expectedReturn: projectDetails?.expectedReturn || "",
      sensitive: projectDetails?.sensitive || false,
      department: projectDetails?.department || [],
    })
  }, [projectDetails]);

  const toggleDepartment = (dept: string) => {
    if (isReadOnly) return;
    setFormData((prevData) => ({
      ...prevData,
      department: prevData.department.includes(dept)
        ? prevData.department.filter((d) => d !== dept)
        : [...prevData.department, dept],
    }));
    setIsDepartmentDropdownOpen(false);
  };
  const getDepartmentNames = (allDepts: any[], selectedDepts: String[]) => {
    let departments = "";
    if (!allDepts) {
      return "Select Department";
    }
    selectedDepts.forEach((deptId) => {
      const dept = allDepts.find((d) => d._id === deptId);
      if (dept) {
        departments += dept.name + ", ";
      }
    });
    departments = departments.slice(0, -2);
    return departments;
  };

  const [searchTag, setSearchTag] = useState(""); // Track search input
  const [isTagDropdownOpen, setIsTagDropdownOpen] = useState(false);
  const [tagList, setTagList] = useState<any>(null);
  const toggleTag = (tagName: string) => {
    setFormData((prevData) => ({
      ...prevData,
      tags: prevData.tags.includes(tagName)
        ? prevData.tags.filter((tag) => tag !== tagName)
        : [...prevData.tags, tagName],
    }));
    setIsTagDropdownOpen(false); // Close dropdown after selection
    setSearchTag("");
  };

  const fetchTagsData = async (search: string) => {
    if (!search) {
      setTagList(null);
      return;
    }

    try {
      const result = await datasetService.getTagList({
        search,
        type: "tag", // Ensuring we only fetch tags
        page: 1,
        limit: 10,
      });
      setTagList(result.data);
      setIsTagDropdownOpen(true);
    } catch (err) {
      console.error("Failed to fetch tag data.", err);
    }
  };
  const handleTagSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTag(value);
    fetchTagsData(value);
  };

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (!event.target.closest(".tag-dropdown")) {
        setIsTagDropdownOpen(false);
      }
      if (!event.target.closest(".department-dropdown")) {
        setIsDepartmentDropdownOpen(false);
      }
    };
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  const cancelEdit = () => {
    setIsReadOnly(true);
    setFormData({
      ...formData,
      description: projectDetails?.description || "",
      tags: projectDetails?.tags || [],
      expectedReturn: projectDetails?.expectedReturn || "",
      sensitive: projectDetails?.sensitive || false,
      department: projectDetails?.department || [],
    });
  };

  const updateProjectHandler = async () => {
    let updateData: Record<string, any> = {};

    // Create a helper function to add changes to updateData
    const addToUpdateData = (key: string, oldValue: any, newValue: any) => {
      if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
        updateData[key] = newValue;
      }
    };
    if (!projectDetails?.isAiBox) {
      if (formData.description.trim() === "") {
        setToast({
          message: "Description is mandatory.",
          type: "error",
        });
        return;
      }
    }
    // Check for changes in each field

    addToUpdateData(
      "description",
      projectDetails?.description,
      formData.description
    );
    addToUpdateData(
      "expectedReturn",
      projectDetails?.expectedReturn,
      formData.expectedReturn
    );
    if (!formData.department || formData.department.length === 0) {
      setToast({
        message: "Department is mandatory.",
        type: "error",
      });
      return; // Stop the function if department is empty
    }
    addToUpdateData(
      "department",
      projectDetails?.department || [],
      formData.department
    );
    addToUpdateData("tags", projectDetails?.tags || [], formData.tags);
    addToUpdateData("sensitive", projectDetails?.sensitive, formData.sensitive);

    if (Object.keys(updateData).length > 0) {
      setLoadingUpdate(true);
      try {
        const response = await updateProject(
          updateData,
          projectDetails?._id || ""
        );
        if (response.status === 200 || response.status === 201) {
          setIsReadOnly(true);
          setToast({
            message: "Details updated successfully.",
            type: "success",
          });
          fetchProjectDetails();
        }
      } catch (error) {
        setToast({
          message: "Something went wrong.",
          type: "error",
        });
      } finally {
        setLoadingUpdate(false);
      }
    } else {
      setToast({
        message: "No changes detected. Please modify a field before updating.",
        type: "error",
      });
    }
  };

  const updateProjectStatus = async () => {
    setLoadingHold(true);
    let newStatus = "";
    if (holdProject) {
      newStatus = "In-process";
    } else {
      newStatus = "Hold";
    }
    try {
      const res = await updateProject(
        {
          status: newStatus,
        },
        projectDetails?._id || ""
      );
      if (res.status === 200 || res.status === 201) {
        setHoldProject(!holdProject);
      }
    } catch (error) {
      console.error("API error:", error);
    } finally {
      setLoadingHold(false);
    }
  };

  const handleConfirmRemove = async () => {
    setIsDialogOpen(false);
    setLoadingDelete(true);
    try {
      const res = await deleteProject(projectDetails?._id || "");
      if (res.status === 200 || res.status === 201) {
        router.push(`/projects`);
      }
    } catch (error) {
      setLoadingDelete(false);
      console.error("API error:", error);
    }
  };

  const handleDeleteClick = () => {
    setIsDialogOpen(true);
  };

  return (
    <>
      <div className="">
        <style jsx>{`
          .tooltip {
            position: relative;
            display: inline-block;
          }

          .tooltip .tooltiptext {
            visibility: hidden;
            minwidth: 200px;
            background-color: #fff;
            color: #555;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            box-shadow: 0px 0px 12px #00000029;
            z-index: 100;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            white-space: nowrap;
          }

          .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
          }
        `}</style>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
            dismissTime={3000}
          />
        )}
        <div className=" flex text-medium text-xl font-base p-4 items-center gap-2">
          General
          {isEditEnable ? (
            <div
              onClick={() => (isReadOnly ? setIsReadOnly(false) : cancelEdit())}
            >
              <Edit className="w-4 h-4 text-teal-500 cursor-pointer" />
            </div>
          ) : (
            <></>
          )}
        </div>
        <hr />
        <div className="px-6 py-2">
          <label className="mb-2 flex items-center">
            Project Name <span className="text-red-500">*</span>
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">Enter a clear project name</span>
            </div>
          </label>
          <input
            type="text"
            readOnly
            className="w-full p-2 border bg-gray-100 text-gray-400 cursor-not-allowed rounded focus:outline-none focus:transparent"
            value={projectDetails?.name || "Name of project"}
            placeholder="Eg. Sales Report"
          />
        </div>
        <div className="px-6 py-2">
          <label className="mb-2 flex items-center">
            Creator Name <span className="text-red-500">*</span>
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">Creator of the project</span>
            </div>
          </label>
          <input
            type="text"
            readOnly
            className="w-full p-2 border rounded bg-gray-100 text-gray-400 cursor-not-allowed focus:outline-none focus:transparent"
            value={projectDetails?.creator.name || "Name of project"}
            placeholder="Eg. Sales Report"
          />
        </div>
        <div className="px-6 py-2">
          <label className="mb-2 flex items-center">
            Project Category <span className="text-red-500">*</span>
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">Category of the project</span>
            </div>
          </label>
          <input
            type="text"
            readOnly
            className="w-full p-2 border rounded bg-gray-100 text-gray-400 cursor-not-allowed focus:outline-none focus:transparent"
            value={projectDetails?.isAiBox ? "AI in a Box" : "Custom"}
            placeholder="AI In a box"
          />
        </div>
        <div className="px-6 py-2">
          <label className="mb-2 flex items-center">
            Project Type <span className="text-red-500">*</span>
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">
                Select the type of the project
              </span>
            </div>
          </label>
          <input
            type="text"
            readOnly
            className="w-full p-2 border rounded bg-gray-100 text-gray-400 cursor-not-allowed focus:outline-none focus:transparent"
            value={projectDetails?.appType || "Name of project"}
            placeholder="Eg. Sales Report"
          />
        </div>
        <div className="px-6 py-2 relative">
          <label className="mb-2 flex items-center">
            Department Type<span className="text-red-500">*</span>
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">
                Select the relevant departments for this project
              </span>
            </div>
          </label>
          <div className="department-dropdown">
            <div
              className={`w-full p-2 border rounded focus:outline-non cursor-pointer flex justify-between items-center ${isReadOnly
                ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                : "focus:border-teal-500"
                }`}
              onClick={() =>
                setIsDepartmentDropdownOpen(!isDepartmentDropdownOpen)
              }
            >
              <span>
                {formData.department.length
                  ? getDepartmentNames(
                    departmentList?.departments,
                    formData.department
                  )
                  : "Select Departments"}
              </span>
              <ChevronDown className="w-4 h-4" />
            </div>
            {isDepartmentDropdownOpen && !isReadOnly && (
              <div className="absolute z-10 w-full mt-1 bg-white border rounded shadow-lg">
                {departmentList?.departments?.map((dept: any) => (
                  <div
                    key={dept._id}
                    className="p-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => toggleDepartment(dept._id)}
                  >
                    <Checkbox
                      id="Department"
                      label={dept.name}
                      checked={formData.department.includes(dept._id)}
                      onChange={() => { }}
                    />
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        <div className="px-6 py-2">
          <label className=" mb-2 flex items-center">
            Description<span className="text-red-500">*</span>
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">
                Briefly describe the purpose of this project
              </span>
            </div>
          </label>
          <textarea
            className={`w-full p-2 border rounded focus:outline-none ${isReadOnly
              ? "bg-gray-100 text-gray-400 cursor-not-allowed"
              : "focus:border-teal-500"
              }`}
            value={formData.description}
            readOnly={isReadOnly}
            onChange={(e) =>
              setFormData({ ...formData, description: e.target.value })
            }
            placeholder="Eg. What's the purpose of this project?"
            rows={4}
          />
        </div>
        {/* <div className="px-6 py-2">
          <label className="mb-2 flex items-center">
            Expected Return
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">
                Specify the expected return percentage
              </span>
            </div>
          </label>
          <input
            type="number"
            className={`w-full p-2 border rounded focus:outline-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none ${
              isReadOnly
                ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                : "focus:border-teal-500"
            }`}
            value={formData.expectedReturn}
            readOnly={isReadOnly}
            onChange={(e) => {
              const value = e.target.value;
              const numericValue = parseFloat(value);

              if (
                value === "" ||
                (!isNaN(numericValue) && numericValue <= 100)
              ) {
                setFormData({ ...formData, expectedReturn: value });
              }
            }}
            placeholder="Eg. 10%"
            max={100}
          />
        </div> */}
        {/* <div className="px-6 py-2 mb-6 relative">
          <label className=" mb-2 flex items-center">
            Tags
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">Enter relevant keywords to categorize the project</span>
            </div>
          </label>

          
          <input
            type="text"
            className={`w-full p-2 border rounded focus:outline-none ${isReadOnly ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "focus:border-teal-500"
              }`}
            value={searchTag}
            onChange={handleTagSearch}
            placeholder="Search tags..."
            readOnly={isReadOnly}
          />

          
          {isTagDropdownOpen && tagList?.tags?.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white border rounded shadow-lg tag-dropdown">
              {tagList.tags.map((tag: any) => (
                <div
                  key={tag.name}
                  className="p-2 hover:bg-gray-100 cursor-pointer flex items-center"
                  onClick={() => toggleTag(tag.name)}
                >
                  <input type="checkbox" checked={formData.tags.includes(tag.name)} readOnly className="mr-2" />
                  {tag.name}
                </div>
              ))}
            </div>
          )}

          
          {formData.tags.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-4">
              {formData.tags.map((tag) => (
                <span
                  key={tag}
                  className="bg-[#F4F5F6] text-[#3B4154] px-2 py-1 rounded-[13px] text-sm flex items-center gap-1"
                >
                  <Image src="/assets/icons/tag_icon.svg" alt="Warning Icon" width={15} height={15} />
                  {tag}
                  <button className="ml-1" onClick={() => toggleTag(tag)} disabled={isReadOnly}>
                    <X className="h-4 w-4" />
                  </button>
                </span>
              ))}
            </div>
          )}
        </div> */}
        {/* Tags */}
        <div className="px-6 py-2 mb-6 relative">
          <label className="mb-2 flex items-center">
            Tags
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">
                Enter relevant keywords to categorize the project
              </span>
            </div>
          </label>

          {/* Tags + Input (Combined) */}
          <div
            className={`flex flex-wrap items-center gap-2 p-2 border rounded min-h-[42px] ${isReadOnly
              ? "bg-gray-100 text-gray-400 cursor-not-allowed"
              : "focus-within:border-teal-500"
              }`}
            onClick={() =>
              !isReadOnly && document.getElementById("tagInput2")?.focus()
            }
          >
            {formData.tags.map((tag) => (
              <span
                key={tag}
                className={`${isReadOnly ? "bg-white" : "bg-[#F4F5F6]"
                  } text-[#3B4154] px-2 py-1 rounded-[13px] text-sm flex items-center gap-1`}
              >
                <Image
                  src="/assets/icons/tag_icon.svg"
                  alt="Tag Icon"
                  width={15}
                  height={15}
                />
                {tag}
                <button
                  className="ml-1"
                  onClick={() => toggleTag(tag)}
                  disabled={isReadOnly}
                >
                  <X className="h-4 w-4" />
                </button>
              </span>
            ))}

            <input
              id="tagInput2"
              type="text"
              className="flex-1 min-w-[120px] outline-none bg-transparent"
              value={searchTag}
              onChange={handleTagSearch}
              onKeyDown={(e) => {
                if (
                  e.key === "Backspace" &&
                  searchTag === "" &&
                  formData.tags.length &&
                  !isReadOnly
                ) {
                  toggleTag(formData.tags[formData.tags.length - 1]);
                }
              }}
              placeholder="Search tags..."
              readOnly={isReadOnly}
            />
          </div>

          {/* Dropdown for Fetched Tags */}
          {isTagDropdownOpen && tagList?.tags?.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white border rounded shadow-lg tag-dropdown">
              {tagList.tags.map((tag: any) => (
                <div
                  key={tag.name}
                  className="p-2 hover:bg-gray-100 cursor-pointer flex items-center"
                  onClick={() => !isReadOnly && toggleTag(tag.name)}
                >
                  <input
                    type="checkbox"
                    checked={formData.tags.includes(tag.name)}
                    readOnly
                    className="mr-2"
                  />
                  {tag.name}
                </div>
              ))}
            </div>
          )}
        </div>

        <hr />
        <div className="px-6 py-2 flex gap-4 items-start">
          <label
            className={`relative inline-block w-9 h-4 mt-1 ${isReadOnly ? "cursor-not-allowed opacity-50" : ""
              }`}
          >
            <input
              type="checkbox"
              className="opacity-0 w-0 h-0"
              checked={formData.sensitive}
              onChange={() =>
                !isReadOnly &&
                setFormData({ ...formData, sensitive: !formData.sensitive })
              }
              disabled={isReadOnly}
            />
            <span
              className={`absolute top-[-1px] left-0 right-0 bottom-0 transition rounded-full ${formData.sensitive ? "bg-teal-500" : "bg-gray-300"
                } ${isReadOnly ? "cursor-not-allowed" : "cursor-pointer"}`}
            >
              <span
                className={`absolute h-2.5 w-2.5 left-1 bottom-1 bg-white transition rounded-full ${formData.sensitive ? "translate-x-5" : "translate-x-0"
                  }`}
              />
            </span>
          </label>
          <label className="mb-2 flex items-center gap-1">
            Mark as sensitive
          </label>
        </div>
        {isEditEnable &&
          isReadOnly && ( // Replace shouldShowButtons with your condition
            <div className="px-6 py-2 flex gap-3">
              <button
                className="text-[#888FAA] border border-[#888FAA] rounded-md px-5 py-1.5 text-center flex items-center justify-center"
                onClick={() => updateProjectStatus()}
                disabled={loadingHold} // Disable button while loading
              >
                {loadingHold ? (
                  <div className="animate-spin border-2 border-[#888FAA] border-t-transparent rounded-full h-5 w-5"></div>
                ) : holdProject ? (
                  <div className="flex items-center gap-1">
                    <Play className="w-5 h-5 text-[#888FAA]" />
                    Resume Project
                  </div>
                ) : (
                  <div className="flex items-center gap-1">
                    <Pause className="w-5 h-5 text-[#888FAA]" />
                    Hold Project
                  </div>
                )}
              </button>

              <button
                className="text-white bg-[#C0433C] rounded-md px-5 py-1.5 text-center flex items-center justify-center"
                onClick={() => handleDeleteClick()}
                disabled={loadingDelete} // Disable button while loading
              >
                {loadingDelete ? (
                  <div className="animate-spin border-2 border-white border-t-transparent rounded-full h-5 w-5"></div>
                ) : (
                  <div className="flex items-center gap-1">
                    <Trash className="w-4 h-4 text-white" />
                    Delete Project
                  </div>
                )}
              </button>
            </div>
          )}

        {/* Edit button group*/}
        {isReadOnly ? (
          <div></div>
        ) : (
          <div className="flex gap-4 items-center justify-end py-6">
            <button className=" text-[#00B2A1]" onClick={() => cancelEdit()}>
              Cancel
            </button>
            <button
              className="text-white bg-[#00B2A1] rounded-md px-10 py-1.5 text-center flex items-center justify-center"
              onClick={() => updateProjectHandler()}
              disabled={loadingUpdate}
            >
              {loadingUpdate ? (
                <div className="animate-spin border-2 border-white border-t-transparent rounded-full h-5 w-5"></div>
              ) : (
                "Update"
              )}
            </button>
          </div>
        )}
        <div className="p-4"></div>
      </div>
      {/* Dialog */}
      <DeleteProjectDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onConfirm={handleConfirmRemove}
      />
    </>
  );
};

export default GeneralTab;
