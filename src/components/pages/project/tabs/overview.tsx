import React, { useState, useEffect } from "react";
import Card from "@/components/common/card/card";
import Image from "next/image";
import Readme from "@/components/ui/ProjectReadme/project_readme";
import { ProjectDetails } from "@/service/types/types";
import AddDataset from "../../AddDataset/AddDataset";
import { useDispatch } from "react-redux";
import { setProjectActiveTab } from "@/lib/store/features/project/projectTabSlice";
import { setActiveManageTab } from "@/lib/store/features/project/projectManageSlice";
import { getProjectHistory } from "@/service/history";
import { RxAvatar } from "react-icons/rx";
import Loader from "@/components/common/loader/loader";
import Link from "next/link";

interface DescriptionPart {
  title?: string;
  id?: string;
  thumbnail?: string;
  [key: string]: any;
}

interface ProjectHistoryItem {
  description: {
    parts: (DescriptionPart | string)[];
  };
  timestamp: string;
}

interface ApiResponse {
  data: {
    data: ProjectHistoryItem[];
    message: string;
  };
}

const OverviewTab = ({ projectDetails }: { projectDetails: ProjectDetails | null }) => {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState<Boolean>(true);
  const [projectHistoryData, setProjectHistoryData] = useState<ProjectHistoryItem[]>([]);

  const handleAddCollaborators = () => {
    dispatch(setProjectActiveTab("Manage"));
    dispatch(setActiveManageTab("Collaborators"));
  };
  const handleAddData = () => {
    dispatch(setProjectActiveTab("Assets"));
  };

  // console.log(projectDetails);
  useEffect(() => {
    const fetchProjectHistory = async () => {
      try {
        const response = (await getProjectHistory(projectDetails?._id || "")) as ApiResponse;
        if (response.data.data) {
          // console.log(response.data.data);
          setProjectHistoryData(response.data.data as ProjectHistoryItem[]);
        }
      } catch (error) {
        console.error("Failed to fetch comments:", error);
      } finally {
        setIsLoading(false);
      }
    };
    if (projectDetails?._id) {
      fetchProjectHistory();
    }
  }, [projectDetails]);

  if (isLoading) {
    return (
      <>
        <Loader />
      </>
    );
  }

  return (
    <>
      <div className="flex items-center space-x-4 mt-4">
        {projectDetails?.appType !== "Dashboard" && (
          <div className="w-full h-full" onClick={handleAddData}>
            <Card className=" cursor-pointer flex-1 p-8 flex items-center justify-center gap-2 border-[0.5px] border-[#CFD2DE]">
              <Image
                src="/assets/icons/add-data-icon.svg"
                alt="Logo"
                className="pt-1"
                width={15}
                height={15}
                objectFit="contain"
              />
              <p className="mt-2 text-gray-600">Add data to work with</p>
            </Card>
          </div>
        )}
        <div className="w-full h-full" onClick={handleAddCollaborators}>
          <Card className=" cursor-pointer flex-1 p-8 flex items-center justify-center gap-2 border-[0.5px] border-[#CFD2DE]">
            <Image
              src="/assets/icons/add-collb-icon.svg"
              alt="Logo"
              width={25}
              height={25}
              className="pt-1"
              objectFit="contain"
            />
            <p className="mt-2 text-gray-600">Add collaborators</p>
          </Card>
        </div>
      </div>
      <div className="gap-3 flex mt-6 bg-[#F4F5F6] h-[60vh] mb-[15px]">
        <div className="p-4  min-w-[350px]">
          <Readme projectId={projectDetails ? projectDetails?._id : ""} currentUserReadme={""} />
        </div>
        <div className="p-4 w-[100%]">
          <Card className="h-[100%]">
            <div className="h-[100%]">
              <div className="flex justify-between items-center border-b border-[#CFD2DE] p-[6px]">
                <span className="text-[16px] ml-[5px]">Project History</span>
              </div>
              <div className="px-4  h-[90%] overflow-y-scroll pt-[10px] pb-[50px]">
                {projectHistoryData &&
                  projectHistoryData.map((item, index) => (
                    <div key={index} className="text-[14px] mb-[20px]">
                      <div className="flex items-center">
                        {item?.description?.parts.map((part, i) => (
                          <React.Fragment key={i}>
                            {i === 0 && typeof part !== "string" && part?.thumbnail ? (
                              <img
                                src={`${process.env.NEXT_PUBLIC_API_BASE_URL?.replace("/api", "")}${part.thumbnail}`}
                                alt="avatar"
                                className="w-[22px] h-[22px] rounded-full"
                              />
                            ) : (
                              i === 0 && <RxAvatar className="w-[22px] h-[22px]" />
                            )}
                            <p
                              className={`text-[#3B4154] ml-[5px] ${i === item?.description?.parts.length - 1 ? 'whitespace-nowrap overflow-hidden text-ellipsis max-w-[24rem]' : ''}`}
                            >
                              {typeof part === "string" ? (
                                <span>{part} </span>
                              ) : part?.isUser ? (
                                <span>{part?.title}</span>
                              ) : (
                                <span className="font-bold">{part?.title}</span>
                              )}
                            </p>
                          </React.Fragment>
                        ))}
                      </div>
                      <p className="text-[#666F8F]  ml-[30px]">{item?.timestamp}</p>
                    </div>
                  ))}
              </div>
            </div>
          </Card>
        </div>
      </div>
    </>
  );
};

export default OverviewTab;
