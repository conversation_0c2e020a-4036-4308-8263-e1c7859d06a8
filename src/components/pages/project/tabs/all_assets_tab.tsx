import Loader from "@/components/common/loader/loader";
import { dataService } from "@/service/dataService";
import { ProjectDetails } from "@/service/types/types";
import { DatasetResponse } from "@/types/dataset";
import React, { useEffect, useState } from "react";
import { MagnifyingGlassIcon } from "@heroicons/react/24/solid";
import Tile from "@/components/common/tile/tile";
import { useParams, useRouter } from "next/navigation";
import { Pagination } from "@/components/common/pagination/pagination";
import AddToProject from "@/components/common/addToProject/addToProject";
import ProgressBarTabs from "@/components/ui/progress-bar/ProgressBar";

const AllAssetsTab = ({
  projectDetails,
}: {
  projectDetails: ProjectDetails | null;
}) => {
  const { id } = useParams() as { id: string };
  const router = useRouter();
  const [loading, setLoading] = useState<boolean>(false);
  const [allAssets, setAllAssets] = useState<DatasetResponse | null>(null);
  const [query, setQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [urlQuery, setUrlQuery] = useState({
    page: 1,
    limit: 10,
    search: "",
    projectId: id,
  });

  const fetchAllAssets = async () => {
    try {
      setLoading(true);
      const res = await dataService.getAllAssets(urlQuery);
      setAllAssets(res);
    } catch (error) {
      // console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setUrlQuery((prev) => ({
      ...prev,
      search: query,
    }));
  };

  const handleViewDataset = (id: string, isInProject: boolean) => {
    if (projectDetails)
      router.push(
        `/projects/dashboard/${encodeURIComponent(
          projectDetails._id
        )}/dataset/${id}?projectId=${encodeURIComponent(
          projectDetails._id
        )}&isInProject=${encodeURIComponent(isInProject)}`
      );
  };

  useEffect(() => {
    setUrlQuery((prev) => ({
      ...prev,
      page: currentPage,
    }));
  }, [currentPage]);

  useEffect(() => {
    fetchAllAssets();
  }, [urlQuery]);

  // console.log(allAssets);

  return (
    <div className="font-[Lato] pt-5 pl-5 h-full">
      {loading ? (
        <span>
          <Loader />
        </span>
      ) : (
        <>
          <div className="border-b-2 pb-5">
            <h1 className="text-[#3B4154] text-xl font-[lato] font-semibold mt-5">
              Related Data Assets
            </h1>

            <form
              onSubmit={(e) => handleSubmit(e)}
              className="flex gap-0.5  items-center"
            >
              <input
                type="text"
                placeholder="Search Datasets"
                className="w-[701px] h-[44px] bg-[#F4F5F6] text-[#3B4154] border rounded-sm p-2 mt-2 outline-none placholder-[#3B4154]"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
              />
              <button
                type="submit"
                className="w-[44px] h-[44px] text-white bg-teal-500 p-3 mt-2 rounded-sm"
              >
                <MagnifyingGlassIcon />
              </button>
            </form>
          </div>
          <div className=" overflow-y-auto mt-5">
            {allAssets?.datasets?.map((dataset: any) => (
              <div key={dataset._id} className=" text-sm mt-3">
                <div className="flex justify-between items-center border-b pb-3 gap-2">
                  <div className="flex flex-col gap-2 ">
                    {/* Header */}
                    <div className="flex gap-2 items-center w-full">
                      <p className="text-lg font-semibold text-gray-700 line-clamp-1">
                        {dataset.title}
                      </p>
                      {dataset.isWorldDataset && (
                        <button
                          className="flex bg-[#F4F5F6] items-center gap-2 rounded-md px-2 py-1 
                        text-[#3B4154]"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth="1.5"
                            stroke="currentColor"
                            className="size-4"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="m19.5 4.5-15 15m0 0h11.25m-11.25 0V8.25"
                            />
                          </svg>
                          <div className="flex gap-1">
                            <span className="text-md">Imported</span>
                            <span className="text-md">Data</span>
                            <span className="text-md">(World)</span>
                          </div>
                        </button>
                      )}
                    </div>
                    {/* Truncated Description */}
                    <p className=" text-slate-500 line-clamp-2">
                      {dataset.description}
                    </p>
                    {/* Tags */}
                    <div className="flex justify-between items-center">
                      <span className="flex flex-wrap gap-2">
                        {dataset.fileTypes?.map(
                          (tag: string, index: number) => (
                            <span
                              key={index}
                              className="px-3 py-1 text-sm bg-gray-300 text-gray-700 rounded-md"
                            >
                              {tag}
                            </span>
                          )
                        )}
                      </span>
                    </div>
                  </div>
                  <div className=" flex justify-end items-center gap-2">
                    <button
                      className="flex gap-1 px-3 py-[10px] border-[1px] border-teal-500 text-teal-500 rounded-[4px] text-md"
                      onClick={() =>
                        handleViewDataset(dataset._id, dataset.isInProject)
                      }
                    >
                      <span>View</span>
                      <span>Data</span>
                      <span>Asset</span>
                    </button>
                    {projectDetails && (
                      <AddToProject
                        projectId={projectDetails._id}
                        datasetId={dataset._id}
                        isInProject={dataset.isInProject}
                        fetchAllAssets={fetchAllAssets}
                      />
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="flex justify-between items-center mt-3 pb-5">
            <div className="text-gray-600 text-sm">
              Showing {allAssets?.datasets?.length || 0} of{" "}
              {allAssets?.pagination?.total || 0}
            </div>
            {(allAssets?.pagination?.totalPages ?? 0) > 0 && (
              <Pagination
                totalPages={allAssets?.pagination?.totalPages ?? 1}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
              />
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default AllAssetsTab;
