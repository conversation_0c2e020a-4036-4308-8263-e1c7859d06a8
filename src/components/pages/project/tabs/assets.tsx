import React, { useState } from "react";
import Image from "next/image";
import AIAvatarToggle from "./ai_tab";
import CollaboratorTab from "./collaborator_tab";
import { ProjectDetails } from "@/service/types/types";
import GeneralTab from "./general_tab";
import AllAssetsTab from "./all_assets_tab";
import ProjectAssetsTab from "./project_assets_tab";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { setActiveAssetTab } from "@/lib/store/features/project/projectAssetsSlice";

const AssetsTab = ({
  projectDetails,
}: {
  projectDetails: ProjectDetails | null;
}) => {
  const dispatch = useDispatch();
  const activeTab = useSelector(
    (state: RootState) => state.assetTab.activeAssetTab
  );
  // const [activeTab, setActiveTab] = useState(
  //   "All Assets"
  // );

  const sidebarOptions = [
    { name: "All Assets" },
    {
      name: "Project Assets",
      desc: `The files in this folder will be used for this project`,
    },
  ];

  return (
    <div className="flex min-h-screen">
      {/* Sidebar */}
      <div className="w-64 bg-[#F4F5F6] py-4 border-r">
        {sidebarOptions.map((tab) => (
          <div
            key={tab.name}
            className={`flex flex-col cursor-pointer text-sm px-2 py-4 text-[#3B4154] ${activeTab === tab.name
              ? "bg-[#e0e0e0] font-semibold border-l-[3px] border-l-[#00B2A1]"
              : ""
              }`}
            onClick={() => dispatch(setActiveAssetTab(tab.name))}
          >
            {tab.name == "Collaborators"
              ? `Collaborators (${projectDetails?.collaborators.length})`
              : tab.name}
            <div className=" text-gray-500 break-words leading-tight pt-1">
              {tab.desc}
            </div>
          </div>
        ))}
      </div>

      {/* Content area */}
      <div className="flex-1">
        {activeTab === "All Assets" && (
          <AllAssetsTab projectDetails={projectDetails} />
        )}
        {activeTab === "Project Assets" && (
          <ProjectAssetsTab projectDetails={projectDetails} />
        )}
      </div>
    </div>
  );
};

export default AssetsTab;
