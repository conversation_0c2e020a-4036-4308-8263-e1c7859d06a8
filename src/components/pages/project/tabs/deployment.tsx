import { ProjectDetails } from "@/service/types/types";
import React, { useEffect } from "react";
import { Info, Edit, X } from "lucide-react";
import { useState } from "react";
import { getProjectURL, updateProject } from "@/service/projects";
import ProgressBarTabs from "@/components/ui/progress-bar/ProgressBar";
import { toast } from "react-toastify";
export interface DeploymentUrlFormData {
  url: string;
}

const DeploymentTab = ({ projectDetails }: { projectDetails: ProjectDetails | null }) => {
  const [formData, setFormData] = useState<DeploymentUrlFormData>({
    url: "",
  });
  const [isUrlValid, setIsUrlValid] = useState(true);
  const [readOnly, setReadOnly] = useState(false);
  const [appUrl, setAppUrl] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchProjectURl = async () => {
      try {
        const response = await getProjectURL(projectDetails?._id || "");
        const data = await response.data;
        // console.log(data);
        if (data.appUrl) {
          setAppUrl(data.appUrl);
          setFormData({ ...formData, url: data.appUrl });
          setReadOnly(true);
        }
      } catch (error) {
        console.error("Error fetching project details:", error);
      }
    };
    fetchProjectURl();
  }, [projectDetails]);

  const deployProjectHandler = async () => {
    try {
      new URL(formData.url);
    } catch {
      setIsUrlValid(false);
      return;
    }
    setLoading(true);
    try {
      const res = await updateProject({ appUrl: formData.url, isDeployed: true }, projectDetails!._id);

      if (res.status === 200 || res.status === 201) {
        toast.success(
          <div className="flex flex-col gap-1">
            <div className="whitespace-nowrap">Project deployed successfully!</div>
            <div className="text-[14px]">
              To launch the project, go to <b>Deployed project</b>
            </div>
          </div>
        );
        setReadOnly(true);
        setIsUrlValid(true);
      }
    } catch (error: any) {
      console.error("API error:", error);
      const err = error?.message || "Something went wrong!";
      toast.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <style jsx>{`
        .tooltip {
          position: relative;
          display: inline-block;
        }

        .tooltip .tooltiptext {
          visibility: hidden;
          width: 250px;
          background-color: #fff;
          color: #555;
          text-align: center;
          border-radius: 6px;
          padding: 5px;
          position: absolute;
          box-shadow: 0px 0px 12px #00000029;
          z-index: 100;
          bottom: 125%;
          left: 50%;
          margin-left: -60px;
          opacity: 0;
          transition: opacity 0.3s;
        }

        .tooltip:hover .tooltiptext {
          visibility: visible;
          opacity: 1;
        }
      `}</style>
      <div className="px-6 py-4 flex justify-between items-center">
        <h2 className="text-xl font-semibold">Deployment</h2>
      </div>
      <div className="px-6">
        <label className="mb-2 flex items-center">
          {projectDetails?.appType} URL <span className="text-red-500">*</span>
          <div className="tooltip ml-1">
            <Info className="w-4 h-4 text-gray-400" />
            <span className="tooltiptext">URL of deployed {projectDetails?.appType}</span>
          </div>
        </label>
        <div className="flex gap-3">
          <input
            type="text"
            readOnly={readOnly}
            className={`w-2/3 p-2 border rounded focus:outline-none ${readOnly ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "focus:border-teal-500"
              }`}
            value={formData.url}
            onChange={(e) => setFormData({ ...formData, url: e.target.value })}
            placeholder="Eg. https://www.notion.so/"
          />
          {readOnly ? (
            <button onClick={() => setReadOnly(false)}>
              <Edit className="w-5 h-5 text-teal-500" />
            </button>
          ) : (
            <button onClick={() => setReadOnly(true)}>
              <X className="w-6 h-6 text-teal-500" />
            </button>
          )}
        </div>

        {!isUrlValid && <div className="text-sm text-red-500 pt-2">Enter a valid URL *</div>}
        <button
          onClick={() => deployProjectHandler()}
          disabled={readOnly}
          className={`rounded-[4px] px-8 py-2 mt-4 ${readOnly ? "bg-gray-100 text-gray-500 cursor-not-allowed" : "bg-teal-500 text-white cursor-pointer"
            }`}
        >
          {loading ? (
            <div className="animate-spin border-2 border-white border-t-transparent rounded-full h-5 w-5"></div>
          ) : formData.url !== "" && readOnly ? (
            "Deployed"
          ) : (
            "Deploy"
          )}
        </button>
      </div>
    </>
  );
};

export default DeploymentTab;
