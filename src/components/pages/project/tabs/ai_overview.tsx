import React from "react";

function AiOverview({ aiProjectDetails }: { aiProjectDetails: any }) {
  // console.log(aiProjectDetails);
  const parseMarkdown = (text: string | undefined) => {
    if (!text) return "";
    return text
      .replace(/^# (.*$)/gim, "<h1>$1</h1>") // H1
      .replace(/^## (.*$)/gim, "<h2>$1</h2>") // H2
      .replace(/^### (.*$)/gim, "<h3>$1</h3>") // H3
      .replace(/\*\*(.*?)\*\*/gim, "<b>$1</b>") // Bold
      .replace(/\*(.*?)\*/gim, "<i>$1</i>") // Italic
      .replace(/`([^`]+)`/g, "<code>$1</code>") // Inline Code
      .replace(/\n/g, "<br>") // Convert remaining newlines to <br>
      .replace(/\n\n+/g, "\n"); // Convert multiple newlines to a single newline
  };
  const formatDate = (dateString: string | undefined): string => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const month = date.toLocaleString("en-US", { month: "short" }); // "Feb"
    const year = date.getFullYear(); // 2025
    return `Created in ${month} ${year}`;
  };

  const getTimeDifference = (dateString: string | undefined): string => {
    if (!dateString) return "";
    const updatedDate = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor(
      (now.getTime() - updatedDate.getTime()) / 1000
    );

    const secondsInMinute = 60;
    const secondsInHour = 60 * secondsInMinute;
    const secondsInDay = 24 * secondsInHour;
    const secondsInMonth = 30 * secondsInDay;
    const secondsInYear = 12 * secondsInMonth;

    if (diffInSeconds < secondsInMinute) {
      return `${diffInSeconds} second${diffInSeconds !== 1 ? "s" : ""} ago`;
    } else if (diffInSeconds < secondsInHour) {
      const minutes = Math.floor(diffInSeconds / secondsInMinute);
      return `${minutes} minute${minutes !== 1 ? "s" : ""} ago`;
    } else if (diffInSeconds < secondsInDay) {
      const hours = Math.floor(diffInSeconds / secondsInHour);
      return `${hours} hour${hours !== 1 ? "s" : ""} ago`;
    } else if (diffInSeconds < secondsInMonth) {
      const days = Math.floor(diffInSeconds / secondsInDay);
      return `${days} day${days !== 1 ? "s" : ""} ago`;
    } else if (diffInSeconds < secondsInYear) {
      const months = Math.floor(diffInSeconds / secondsInMonth);
      return `${months} month${months !== 1 ? "s" : ""} ago`;
    } else {
      const years = Math.floor(diffInSeconds / secondsInYear);
      return `${years} year${years !== 1 ? "s" : ""} ago`;
    }
  };

  return (
    <div>
      <div className="flex gap-20 justify-between">
        <div className="w-3/4">
          <div className="h-full">
            {/* {activeTab === "README" && ( */}
            <div
              className="prose max-w-full mt-5 text-[#3B4154]"
              dangerouslySetInnerHTML={{
                __html: parseMarkdown(aiProjectDetails?.install?.readme || ""),
              }}
            />
          </div>
        </div>
        <div className="w-1/4 flex flex-col gap-4 mt-5 text-[#3B4154]">
          <h1 className="text-[#3B4154] font-semibold">Project Metrices</h1>
          <div className="flex gap-4">
            <img src="/assets/Group_7093.svg" alt="" width={30} />
            <p>{formatDate(aiProjectDetails?.createdAt)}</p>
          </div>
          <div className="flex gap-4">
            <img src="/assets/Group_7093.svg" alt="" width={30} />
            <p>Modified {getTimeDifference(aiProjectDetails?.updatedAt)}</p>
          </div>
          <h1 className="text-[#3B4154] font-semibold">Category</h1>
          <div className="flex justify-between items-center">
            <span className="flex flex-wrap gap-2">
              {aiProjectDetails?.tags?.map((tag: any, index: number) => (
                <span
                  key={index}
                  className="px-3 py-1 text-sm bg-[#f4f5f6] text-gray-700 rounded-md"
                >
                  {tag}
                </span>
              ))}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AiOverview;
