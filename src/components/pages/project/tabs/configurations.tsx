import { ConfigurationSection, ProjectDetails } from "@/service/types/types";
import { Edit, Info } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { ToastState } from "./manage";
import { Toast } from "@/components/ui/toast/toast";
import { updateConfig, updateConfigBody } from "@/service/aiServices";

export default function Configurations(
  { projectDetails, fetchProjectDetails }:
  { projectDetails: ProjectDetails | null; 
    fetchProjectDetails:()=>void;
  }
) {
  const [isReadOnly, setIsReadOnly] = useState(true);
  const [loadingUpdate, setLoadingUpdate] = useState(false);
  const [formData, setFormData] = useState<any>({});
  const [toast, setToast] = useState<ToastState>(null);

  const cancelEdit = () => {
    setIsReadOnly(true);
  };

  const checkAccess = () => {
    const userInfo = useSelector((state: any) => state.user.userInfo);
    return projectDetails?.creator._id === userInfo.user._id;
  };

  useEffect(() => {
    if (projectDetails?.install?.configuration) {
      const initialFormData: Record<string, string> = {};

      Object.entries(projectDetails.install.configuration).forEach(([_, sectionValue]) => {
        Object.entries(sectionValue).forEach(([key, field]) => {
          if (field.value !== undefined) {
            initialFormData[key] = String(field.value);
          }
        });
      });

      setFormData(initialFormData);
    }
  }, [projectDetails]);

  const renderInputs = (settings: ConfigurationSection) => {
    return Object.keys(settings).map((key) => {
      const field = settings[key];
      const isArray = field.type === 'array';
      const inputType = field.type === 'integer' ? 'number' : 'text';

      const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        setFormData((prevData: any) => ({
          ...prevData,
          [key]: e.target.value,
        }));
      };

      return (
        <div key={key}>
          <style jsx>{`
          .tooltip {
            position: relative;
            display: inline-block;
          }

          .tooltip .tooltiptext {
            visibility: hidden;
            minwidth: 200px;
            background-color: #fff;
            color: #555;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            box-shadow: 0px 0px 12px #00000029;
            z-index: 100;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            white-space: nowrap;
          }

          .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
          }
        `}</style>
          <label className="mb-2 text-md flex items-center gap-1 text-[#666F8F] text-[14px]">
            {key.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase())} <span className="text-red-500">*</span>
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">{field.description}</span>
            </div>
          </label>

          {field.allowed_values ? (
            <select
              disabled={isReadOnly}
              className={`w-full p-2 rounded border focus:outline-none ${isReadOnly ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "focus:border-teal-500"}`}
              value={formData[key] ?? field.value ?? ''}
              onChange={handleChange}
            >
              <option value="" disabled>Select {key.replace(/_/g, ' ')}</option>
              {field.allowed_values.map((value) => (
                <option key={value} value={value}>
                  {value}
                </option>
              ))}
            </select>
          ) : isArray ? (
            <input
              type={inputType}
              readOnly={isReadOnly}
              className={`w-full p-2 rounded border focus:outline-none ${isReadOnly ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "focus:border-teal-500"}`}
              placeholder={`Enter ${key}`}
              value={formData[key] ?? field.value ?? ''}
              onChange={handleChange}
            />
          ) : (
            <input
              type={inputType}
              readOnly={isReadOnly}
              className={`w-full p-2 rounded border focus:outline-none ${isReadOnly ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "focus:border-teal-500"}`}
              placeholder={field.description}
              value={formData[key] ?? field.value ?? ''}
              onChange={handleChange}
            />
          )}
        </div>
      );
    });
  };


  const updateConfigHandler = (updatedData: { key: string; value: string }[]) => {
    const data = {
      formData: updatedData,
      id: projectDetails?._id,
    };
    updateConfig(data as updateConfigBody)
      .then(() => {
        setToast({
          message: "Configuration updated successfully.",
          type: "success",
        });
        setLoadingUpdate(false);
        setIsReadOnly(true);
        fetchProjectDetails();
      })
      .catch((error) => {
        setToast({
          message: error.message,
          type: "error",
        });
        setLoadingUpdate(false);
      });
  };

  const handleUpdate = () => {
    let allFieldsValid = true;
    const updatedData: { key: string; value: string }[] = [];

    // Validate all required fields
    if (projectDetails?.install?.configuration) {
      for (const sectionKey in projectDetails?.install?.configuration) {
        const section = projectDetails?.install?.configuration[sectionKey as keyof typeof projectDetails.install.configuration];

        for (const key in section) {
          const field = section[key];

          if (field.required && (!formData[key] || String(formData[key]).trim() === "")) {
            // console.log("Error: A required field is empty.");
            allFieldsValid = false;
            break;
          }

          if (formData[key] && String(formData[key]).trim() !== "") {
            updatedData.push({
              key: key,
              value: String(formData[key]).trim(),
            });
          }
        }

        if (!allFieldsValid) break;
      }
    }

    if (!allFieldsValid) {
      setToast({
        message: "Please fill in all required fields.",
        type: "error",
      });
    } else {
      setLoadingUpdate(true);
      updateConfigHandler(updatedData);
    }
  };

  // Check if the entire configuration is empty
  const isConfigurationEmpty = !projectDetails?.install?.configuration || Object.keys(projectDetails?.install?.configuration).length === 0;

  return (
    <div className="font-[Lato]">
      {toast && <Toast message={toast.message} type={toast.type} onClose={() => setToast(null)} dismissTime={4000} />}
      <div className="flex text-medium text-xl font-base p-4 items-center gap-2">
        Configuration
        {!isConfigurationEmpty && checkAccess() && (
          <div onClick={() => (isReadOnly ? setIsReadOnly(false) : cancelEdit())}>
            <Edit className="w-4 h-4 text-teal-500 cursor-pointer" />
          </div>
        )}
      </div>
      <hr />
      <div className="p-4 flex flex-col gap-5">
        {isConfigurationEmpty ? (
          <div className="text-center text-gray-500">
            No configuration needed
          </div>
        ) : (
          Object.entries(projectDetails?.install?.configuration || {}).map(([sectionKey, sectionValue]) => (
            <div key={sectionKey}>
              <h1 className="text-[16px] font-bold capitalize text-[#3B4154]">{sectionKey.replace(/_/g, ' ')}</h1>
              <div className="grid grid-cols-2 gap-4 mt-3">
                {renderInputs(sectionValue as ConfigurationSection)}
              </div>
            </div>
          ))
        )}
      </div>

      {!isReadOnly && (
        <div className="flex gap-4 items-center justify-end py-6">
          <button className=" text-[#00B2A1]" onClick={() => cancelEdit()}>
            Cancel
          </button>
          <button
            className="text-white bg-[#00B2A1] rounded-md px-10 py-1.5 text-center flex items-center justify-center"
            onClick={handleUpdate}
            disabled={loadingUpdate}
          >
            {loadingUpdate ? (
              <div className="animate-spin border-2 border-white border-t-transparent rounded-full h-5 w-5"></div>
            ) : (
              "Update"
            )}
          </button>
        </div>
      )}
    </div>
  );
}
