"use client";

import { useState, useRef, useEffect } from "react";
import type { Comment } from "@/components/pages/project/Comments/comments-types";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/pages/project/Comments/comments-component/comment-UI/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/pages/project/Comments/comments-component/comment-UI/dropdown-menu";
import { Button } from "@/components/pages/project/Comments/comments-component/comment-UI/button";
import { MoreHorizontal, Trash2, Loader2 } from "lucide-react";
import { ProjectDetails } from "@/service/types/types";
import { useDispatch, useSelector } from 'react-redux';
import { valueAnalyticsService } from '@/service/api';
import type { RootState, AppDispatch } from '@/lib/store';
import { fetchValuePillars } from '@/lib/store/features/va/valuePillarsSlice';

interface CommentListProps {
  comments: Comment[];
  projectData: ProjectDetails | null;
  isLoading?: boolean;
  onDelete: (commentId: string) => Promise<void>;
}

const currentUser = localStorage.getItem("user");
const userId = currentUser ? JSON.parse(currentUser).user._id : null;

export function CommentList({ comments, isLoading, onDelete }: CommentListProps) {
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const dropdownRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (openDropdown) {
        const currentDropdown = dropdownRefs.current[openDropdown];
        if (currentDropdown && !currentDropdown.contains(event.target as Node)) {
          setOpenDropdown(null);
        }
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [openDropdown]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[200px]">
        <Loader2 className="h-8 w-6 animate-spin text-[#00B2A1]" />
      </div>
    );
  }

  if (comments.length === 0) {
    return (
      <div className="text-center py-28">
        <div className="w-16 h-16 mx-auto mb-4">
          <img src="/assets/commentLogo.svg" alt="No comments" className="w-full h-full" />
        </div>
        <h3 className="font-semibold mb-1 text-[#1b1d21]">No comments yet</h3>
        <p className="text-sm text-muted-foreground text-[#666f8f]">
          Give feedback, ask questions, or start a <br /> discussion in the comments.
        </p>
      </div>
    );
  }

  return (
    <div>
      {comments.map((comment) => (
        <div key={comment._id} className="p-4 border-b border-gray-300">
          <div className="flex items-start justify-between">
            <div className="flex gap-3">
              <Avatar>
                <AvatarImage src={`${process.env.NEXT_PUBLIC_API_BASE_URL?.replace("/api", "")}${comment.userId.thumbnail}`} alt="User Avatar" />
                <AvatarFallback>{comment.userId.name.charAt(0).toUpperCase()}</AvatarFallback>
              </Avatar>
              <div>
                <div className="flex items-center gap-2 text-sm text-gray-800">
                  <span className="text-[#3B4154] text-[15px]">{comment.userId.name}</span>
                  <span className="text-[#666f8f] text-[13px]">{comment.userRole === "Creator" ? "Executive" : comment.userRole}</span>
                </div>
                <p className="text-[13px] text-muted-foreground text-gray-500">
                  {new Date(comment.updatedOn).toLocaleString("en-GB", {
                    day: "2-digit",
                    month: "long",
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: true,
                  }).replace(",", "")}
                </p>
              </div>
            </div>
            {userId === comment.userId._id && (
              <div ref={(el) => { dropdownRefs.current[comment._id] = el; }}>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      className="p-3"
                      variant="ghost"
                      size="lg"
                      onClick={() =>
                        setOpenDropdown(openDropdown === comment._id ? null : comment._id)
                      }
                    >
                      <MoreHorizontal className="h-5 w-5 bg-[#CFD2DE] rounded-full" />
                    </Button>
                  </DropdownMenuTrigger>
                  {openDropdown === comment._id && (
                    <DropdownMenuContent align="end" className="hover:cursor-pointer">
                      <DropdownMenuItem
                        className="text-destructive text-red-600"
                        onClick={() => onDelete(comment._id)}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  )}
                </DropdownMenu>
              </div>
            )}
          </div>
          <p className="mt-2 pl-2 text-[15px] text-[#3b4154]">{comment.comment}</p>
        </div>
      ))}
    </div>
  );
}
