"use client";
import Activity from "@/components/common/activity/activity";
import Loader from "@/components/common/loader/loader";
import { Button } from "@/components/ui/button/button";
import { dataService } from "@/service/dataService";
import { getDepartmentHistory, getDepartmentHistoryByDepartmentIds } from "@/service/history";
import { X } from "lucide-react";
import React, { useEffect, useState } from "react";

interface aboutIntercept {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  department: any;
}

interface ProjectHistoryResponse {
  data: {
    data: any[];
    currentPage: number;
    totalPages: number;
    totalRecords: number;
  };
}

const About: React.FC<aboutIntercept> = ({ isOpen, setIsOpen, department }) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"; // Prevent background scrolling
    } else {
      document.body.style.overflow = ""; // Restore background scrolling
    }
    return () => {
      document.body.style.overflow = ""; // Cleanup on unmount
    };
  }, [isOpen]);

  const [totalImportedData, setTotalImportedData] = useState<number>(0);
  const [projectHistoryResponse, setProjectHistoryResponse] = useState<ProjectHistoryResponse | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const rowsPerPage = 10; // Since you're fetching 10 items per page

  useEffect(() => {
    const fetchImportedData = async () => {
      const res: any = await dataService.getImportedData({});

      setTotalImportedData(res.data?.datasets?.length || 0);
    };
    const fetchHistory = async () => {
      setIsLoading(true);
      const res: any = await getDepartmentHistoryByDepartmentIds(department.departmentIds, currentPage, rowsPerPage);
      setProjectHistoryResponse(res);
      // console.log("history rr", res);
    };
    department.departmentIds && fetchHistory().then(() => setIsLoading(false));
    fetchImportedData();
    // console.log("dep", department);
  }, [department, currentPage]);

  // Add pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePrev = () => {
    if (currentPage > 1) setCurrentPage((prev) => prev - 1);
  };

  const handleNext = () => {
    if (projectHistoryResponse && currentPage < projectHistoryResponse.data.totalPages) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const getPaginationItems = (current: number, total: number): (number | string)[] => {
    if (total <= 5) {
      return Array.from({ length: total }, (_, i) => i + 1);
    }

    const pages: (number | string)[] = [];
    pages.push(1);

    let start = Math.max(current - 1, 2);
    let end = Math.min(current + 1, total - 1);

    if (current <= 3) {
      start = 2;
      end = 4;
    }
    if (current >= total - 2) {
      start = total - 3;
      end = total - 1;
    }

    if (start > 2) {
      pages.push("...");
    }

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    if (end < total - 1) {
      pages.push("...");
    }

    pages.push(total);
    return pages;
  };

  return (
    <div
      className={`z-50 fixed top-0 right-0 h-screen w-[calc(100%-210px)] bg-white shadow-lg transform ${isOpen ? "translate-x-0" : "translate-x-full"
        } transition-transform duration-300 ease-in-out flex flex-col`}
    >
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 flex h-14 items-center justify-between bg-[#3B4154] px-6">
        <h2 className="text-lg font-medium text-white">About {department.name}</h2>
        <Button variant="ghost" size="icon" className="text-white hover:bg-white/20" onClick={() => setIsOpen(false)}>
          <X className="h-5 w-5" />
        </Button>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto p-5">
        <div className="flex gap-5">
          <div className="flex flex-none w-[188px] h-[135px] justify-center py-6 px-8 border-2 border-[#CFD2DE]">
            <img
              src={department.thumbnailUrl}
              alt="#"
              onError={(e) => {
                e.currentTarget.src = "/assets/Group 4355.svg";
              }}
            />
          </div>
          <div className="flex flex-col flex-1">
            <div className="flex justify-between items-start mb-2">
              <h1 className="text-[20px]  text-[#1B1D21] font-[400]">About {department.name}</h1>
              {/* <button className="px-8 py-2 border-2 border-teal-500 text-teal-500 rounded-md">
                Manage
              </button> */}
            </div>
            <p className="w-[50%] text-[#3B4154] text-[15px]">{department.description}</p>
            <div className="mt-5 flex gap-10">
              <div className="bg-[#F4F5F6] py-4 px-4 rounded-[4px]">
                <p className="text-sm text-[#666F8F]">World wide data</p>
                <p className="text-[30px] text-[#333333] font-bold">50,000+</p>
              </div>
              <div className="bg-[#F4F5F6] py-[15px] px-[16px] rounded-[4px]">
                <p className="text-sm text-[#666F8F]">Imported - World data</p>
                <p className="text-[30px] text-[#333333] font-bold">{totalImportedData}</p>
              </div>
            </div>
            <div className="mt-5 w-full">
              <h1 className=" text-[20px] text-[#3B4154] font-bold">Imported Data activities</h1>
              <div className="border border-b-[#F4f5f6] my-[20px] mb-[40px]"></div>
              <div className="flex flex-col gap-5 mt-5">
                {isLoading && <Loader />}
                {!isLoading &&
                  projectHistoryResponse !== null &&
                  projectHistoryResponse?.data.data.map((i: any, ind: number) => (
                    <Activity total={projectHistoryResponse?.data.data.length} index={ind} data={i} key={ind} />
                  ))}

                {/* Add Pagination UI */}
                {projectHistoryResponse && projectHistoryResponse.data.totalPages > 1 && (
                  <div className="flex items-center justify-center gap-3 p-4">
                    <p className="mr-auto text-gray-500">
                      Showing {(currentPage - 1) * rowsPerPage + 1} -{" "}
                      {Math.min(currentPage * rowsPerPage, projectHistoryResponse.data.totalRecords)} of{" "}
                      {projectHistoryResponse.data.totalRecords} results
                    </p>

                    <button
                      onClick={handlePrev}
                      disabled={currentPage === 1}
                      className="text-gray-500 disabled:cursor-not-allowed disabled:text-gray-300"
                    >
                      Back
                    </button>

                    {getPaginationItems(currentPage, projectHistoryResponse.data.totalPages).map((item, idx) =>
                      typeof item === "number" ? (
                        <button
                          key={item}
                          onClick={() => handlePageChange(item)}
                          className={`h-8 w-8 rounded-md text-sm font-medium flex items-center justify-center ${item === currentPage ? "bg-teal-500 text-white" : "text-gray-500 hover:text-gray-700"
                            }`}
                        >
                          {item}
                        </button>
                      ) : (
                        <span key={`ellipsis-${idx}`} className="text-gray-500">
                          {item}
                        </span>
                      )
                    )}

                    <button
                      onClick={handleNext}
                      disabled={currentPage === projectHistoryResponse.data.totalPages}
                      className="text-teal-500 disabled:cursor-not-allowed disabled:text-gray-300"
                    >
                      Next
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
