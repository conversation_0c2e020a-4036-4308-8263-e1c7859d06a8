import { combineReducers } from "@reduxjs/toolkit";
import userReducer from "@/lib/store/features/user/userSlice";
import projectReducer from "@/lib/store/features/project/projectSlice";
import tabSlice from "@/lib/store/features/project/projectTabSlice";
import projectComment from "@/lib/store/features/project/projectCommentSlice";
import assetTabSlice from "@/lib/store/features/project/projectAssetsSlice";
import manageTabSlice from "@/lib/store/features/project/projectManageSlice";
import favoritesReducer from "@/lib/store/features/favorites/favoritesSlice";
import intercomReducer from "@/lib/store/features/intercom/intercomSlice";
import runInBgReducer from "@/lib/store/features/dataset/runInBgSlice";
import importSlice from "./features/dataset/importSlice";
import sidebarReducer from "@/lib/store/features/sidebar/sidebarSlice";
import searchReducer from "@/lib/store/features/search/searchSlice";
import valueAnalyticsReducer from "@/lib/store/features/va/valueAnalyticsSlice";
import activeLinkReducer from "@/lib/store/features/activeModule/activeModuleSlice";
import valuePillarsReducer from "@/lib/store/features/va/valuePillarsSlice";

const rootReducer = combineReducers({
  user: userReducer,
  projectAi: projectReducer,
  tab: tabSlice,
  projectCommentCount: projectComment,
  assetTab: assetTabSlice,
  manageTab: manageTabSlice,
  favorites: favoritesReducer,
  intercom: intercomReducer,
  runInBg: runInBgReducer,
  importTab: importSlice,
  sidebar: sidebarReducer,
  search: searchReducer,
  valueAnalytics: valueAnalyticsReducer,
  valuePillars: valuePillarsReducer,
  activeLink:activeLinkReducer,
  // Add other feature reducers here
});

export default rootReducer;

