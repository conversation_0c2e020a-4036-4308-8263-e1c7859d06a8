import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const importTab = ["Explore world data", "Imported Data"];

interface ImportSlice {
  importActiveTab: string;
}

// Create a slice for tab management
const importSlice = createSlice({
  name: "importTab",
  initialState: {
    importActiveTab: "Explore world data",
  } as ImportSlice,
  reducers: {
    setImportActiveTab: (state, action: PayloadAction<string>) => {
      if (importTab.includes(action.payload)) {
        state.importActiveTab = action.payload;
      }
    },
  },
});

// Export actions
export const { setImportActiveTab } = importSlice.actions;
export default importSlice.reducer;
