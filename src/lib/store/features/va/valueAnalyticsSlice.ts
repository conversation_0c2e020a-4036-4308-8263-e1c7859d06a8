import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import getValueAnalyticsData from "@/service/valueAnalytics";

interface MonthlyAggregation {
  year: number;
  month: number;
  totalDollarValue: number;
  softDollarValue: number;
  hardDollarValue: number;
  projectsCount: number;
}

interface ValueAnalyticsState {
  projects: any[];
  monthlyAggregation: MonthlyAggregation[];
  filteredData: {
    projects: any[];
    monthlyAggregation: MonthlyAggregation[];
  };
  status: "idle" | "loading" | "succeeded" | "failed";
  error: string | null;
  hasNoData?: boolean;
}

const initialState: ValueAnalyticsState = {
  projects: [],
  monthlyAggregation: [],
  filteredData: {
    projects: [],
    monthlyAggregation: [],
  },
  status: "idle",
  error: null,
  hasNoData: false,
};

export const fetchAllValueAnalytics = createAsyncThunk(
  "valueAnalytics/fetchAll",
  async () => {
    const response = await getValueAnalyticsData({});
    return response.data;
  }
);

export const fetchFilteredValueAnalytics = createAsyncThunk(
  "valueAnalytics/fetchFiltered",
  async ({
    startDate,
    endDate,
    projectId,
  }: {
    startDate?: string;
    endDate?: string;
    projectId?: string;
  }) => {
    const response = await getValueAnalyticsData({
      startDate,
      endDate,
      projectId,
    });
    return response.data;
  }
);

const valueAnalyticsSlice = createSlice({
  name: "valueAnalytics",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchAllValueAnalytics.pending, (state) => {
        state.status = "loading";
        state.hasNoData = false;
      })
      .addCase(fetchAllValueAnalytics.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.projects = action.payload.projects;
        state.monthlyAggregation = action.payload.monthlyAggregation;
        state.filteredData = {
          projects: action.payload.projects,
          monthlyAggregation: action.payload.monthlyAggregation,
        };
        state.hasNoData =
          state.projects.length === 0 && state.monthlyAggregation.length === 0;
      })
      .addCase(fetchAllValueAnalytics.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message || null;
        state.hasNoData = true;
      })
      .addCase(fetchFilteredValueAnalytics.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchFilteredValueAnalytics.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.filteredData = {
          projects: action.payload.projects,
          monthlyAggregation: action.payload.monthlyAggregation,
        };
      });
  },
});

export const selectValueAnalyticsHasNoData = (state: any) =>
  state.valueAnalytics.hasNoData;

export default valueAnalyticsSlice.reducer;
