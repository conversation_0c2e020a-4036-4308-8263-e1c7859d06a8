import { ProjectDetails } from "@/service/types/types";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface ProjectAi {
  isAvatarEnable: boolean;
}

const initialState: ProjectAi = {
  isAvatarEnable: false,
};

const projectSlice = createSlice({
  name: "projectAi",
  initialState,
  reducers: {
    setAiAvatarStatus: (state, action: PayloadAction<{ status: boolean }>) => {
      state.isAvatarEnable = action.payload.status;
    },
  },
});

export const { setAiAvatarStatus } = projectSlice.actions;
export default projectSlice.reducer;
