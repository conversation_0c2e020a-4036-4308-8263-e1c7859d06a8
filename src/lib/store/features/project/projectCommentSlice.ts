import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface ProjectComment {
  projectCommentCount: number;
}

const initialState: ProjectComment = {
    projectCommentCount: 0,
};

const projectCommentSlice = createSlice({
  name: "projectCommentCount",
  initialState,
  reducers: {
    setCommentCount: (state, action: PayloadAction<{ commentCount: number }>) => {
      state.projectCommentCount = action.payload.commentCount;
    },
  },
});

export const { setCommentCount } = projectCommentSlice.actions;
export default projectCommentSlice.reducer;
