import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const tabs = ["Overview", "Assets", "Manage", "Deployment"];

interface TabState {
  activeTab: string;
}

// Create a slice for tab management
const tabSlice = createSlice({
  name: "tab",
  initialState: { activeTab: "Overview" } as TabState,
  reducers: {
    setProjectActiveTab: (state, action: PayloadAction<string>) => {
      if (tabs.includes(action.payload)) {
        state.activeTab = action.payload;
      }
    },
  },
});

// Export actions
export const { setProjectActiveTab } = tabSlice.actions;
export default tabSlice.reducer;
