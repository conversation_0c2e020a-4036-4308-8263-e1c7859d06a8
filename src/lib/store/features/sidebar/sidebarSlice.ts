import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SidebarState {
    isOpen: boolean;
    activeSection: string;
    isSlimExpanded: boolean;
}

const initialState: SidebarState = {
    isOpen: false,
    activeSection: 'value',
    isSlimExpanded: true,
};

const sidebarSlice = createSlice({
    name: 'sidebar',
    initialState,
    reducers: {
        toggleSidebar(state, action: PayloadAction<boolean>) {
            state.isOpen = action.payload;
        },
        setActiveSection(state, action: PayloadAction<string>) {
            state.activeSection = action.payload;
        },
        toggleSlimSidebar(state, action: PayloadAction<boolean>) {
            state.isSlimExpanded = action.payload;
        },
    },
});

export const { toggleSidebar, setActiveSection, toggleSlimSidebar } = sidebarSlice.actions;

export default sidebarSlice.reducer;