import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SearchState {
    currentQuery:string
}

const initialState: SearchState = {
   currentQuery :""
};

const searchSlice = createSlice({
    name: 'search',
    initialState,
    reducers: {
       
        setCurrentQuery(state, action: PayloadAction<string>) {
            state.currentQuery = action.payload;
        },

    },
});

export const { setCurrentQuery } = searchSlice.actions;

export default searchSlice.reducer;