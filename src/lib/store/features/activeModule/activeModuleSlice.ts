// features/activeLinkSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ActiveLinkState {
  value: string;
}

const initialState: ActiveLinkState = {
  value: '',
};

const activeLinkSlice = createSlice({
  name: 'activeLink',
  initialState,
  reducers: {
    setActiveLink: (state, action: PayloadAction<string>) => {
      state.value = action.payload;
    },
  },
});

export const { setActiveLink } = activeLinkSlice.actions;
export default activeLinkSlice.reducer;
