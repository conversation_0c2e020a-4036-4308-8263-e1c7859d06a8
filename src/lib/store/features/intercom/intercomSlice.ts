import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface IntercomState {
  isOpen: boolean;
}

const initialState: IntercomState = {
  isOpen: false,
};

const intercomSlice = createSlice({
  name: 'intercom',
  initialState,
  reducers: {
    openIntercom: (state) => {
      state.isOpen = true;
    },
    closeIntercom: (state) => {
      state.isOpen = false;
    },
    toggleIntercom: (state) => {
      state.isOpen = !state.isOpen;
    },
  },
});

export const { openIntercom, closeIntercom, toggleIntercom } = intercomSlice.actions;
export default intercomSlice.reducer;