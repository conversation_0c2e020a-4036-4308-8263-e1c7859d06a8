"use client";
import Footer from "@/components/ui/footer/footer";
import Header from "@/components/ui/header/header";
import MainContent from "@/components/ui/MainContent/MainContent";
import Sidebar from "@/components/ui/Sidebar/Sidebar";
import { SidebarProvider } from "@/context/sidebar-context";
import AuthWrapper from "./(root)/AuthWrapper";
import IntercomWrapper from "@/components/common/intercom/IntercomProvider";
import { NotificationProvider } from "@/context/notification-context";
import ValueAnalytics from "@/components/pages/ValueAnalytics";
import { Sidebar as SidebarV2 } from "@/components/common/sidebar";
import SearchBar from "@/components/ui/search-bar";
import TopBar from "@/components/ui/TopBar";
import { HomePage } from "@/components/home/<USER>";

export default function Home() {
  return (
    <div>
      <SidebarProvider>
        <AuthWrapper>
          <NotificationProvider>
            <IntercomWrapper>
              {/* TopBar shown on both desktop and mobile */}
              <TopBar />
              <SidebarV2 />
              <MainContent>
                <HomePage />
              </MainContent>
              {/* <Footer /> */}
            </IntercomWrapper>
          </NotificationProvider>
        </AuthWrapper>
      </SidebarProvider>
    </div>
  );
}
