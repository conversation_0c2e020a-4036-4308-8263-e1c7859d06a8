import type { <PERSON>ada<PERSON> } from "next";
import { Lato } from "next/font/google";
import "./globals.css";
import StoreProvider from "./StoreProvider";

const lato = Lato({
  variable: "--font-lato",
  subsets: ["latin"],
  weight: ["100", "300", "400", "700", "900"], // Specify weights you need
});

export const metadata: Metadata = {
  title: "Sutra.AI",
  description: "Sutra.AI",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${lato.variable} antialiased bg-white`}><StoreProvider>{children}</StoreProvider></body>
    </html>
  );
}
