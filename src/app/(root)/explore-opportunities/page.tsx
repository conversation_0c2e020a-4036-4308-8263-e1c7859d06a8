"use client";

import React, { useState, useEffect, useRef } from "react";
import OpportunitiesList from "./components/OpportunitiesList";
import OpportunityDetail from "./components/OpportunityDetail";
import { OpportunityData, QuadrantFilter } from "./utils/types";
import RefreshButton from "./components/RefreshButton";
import OppsList from "./components/OppsList";
import { getOpportunies } from "@/service/agentEOService";
import Loader from "@/components/common/loader/loader";
import { useDispatch } from "react-redux";
import { fetchValuePillars } from "@/lib/store/features/va/valuePillarsSlice";
import { FaSpinner } from "react-icons/fa";


// Transform API data to match our OpportunityData type
const transformJsonToOpportunities = (data: any): OpportunityData[] => {
  return data.items.map((item: any, index: number) => {
    // Calculate total matched entity attributes (all attributes, regardless of confidence score)
    const totalMatchedAttributes = item.entities?.reduce((total: number, entity: any) => {
      return total + (entity.matched_entity_attributes?.length || 0);
    }, 0) || 0;
    // Extract department information from usecase data
    const getDepartmentFromUsecase = (item: any) => {
      const departments = [];

      // Add industry type as primary department
      if (item.usecase_industry_type && item.usecase_industry_type.trim() !== '') {
        departments.push(item.usecase_industry_type);
      }

      // Add actual department if different from industry type
      if (item.usecase_department &&
        item.usecase_department.trim() !== '' &&
        item.usecase_department !== item.usecase_industry_type) {
        departments.push(item.usecase_department);
      }

      // Add model type if exists
      if (item.usecase_model_type && item.usecase_model_type.trim() !== '') {
        departments.push(item.usecase_model_type);
      }

      return departments.slice(0, 2); // Limit to 2 departments max for UI
    };
    return {
      id: item._id,
      name: item.name,
      valueScore: Math.round((item.score || 0) * 10) / 10,
      readinessScore: (item.completion_score || 0) / 10, // Convert to 0-10 range and round to nearest whole number
      description: item.description,
      category: getCategoryFromName(item.name),
      fullDescription: item.description,
      dataSources: `${totalMatchedAttributes} matched data attributes`,
      lastUpdated: new Date(),
      valuePillars: getValuePillarsFromDescription(item.description),
      reason: item.reason, // Add the reason field from JSON data
      entities: item.entities, // Add the entities array from JSON data
      departments: getDepartmentFromUsecase(item), // Add departments array

      // Individual usecase fields - these are the key ones you mentioned
      usecaseIndustryType: item.usecase_industry_type || null,
      usecaseDepartment: item.usecase_department || null,
      usecaseModelType: item.usecase_model_type || null, // This is the main one you want to add
      usecaseCompanySegment: item.usecase_company_segment || null,
      usecaseDomain: item.usecase_domain || null,
      usecaseCompanyDatabaseType: item.usecase_company_database_type || null,
      add_to_project: item.add_to_project || undefined,
    };
  });
};

// Helper function to determine category from name
const getCategoryFromName = (name: string): string => {
  if (name.toLowerCase().includes('supplier') || name.toLowerCase().includes('supply')) return 'Supply Chain';
  if (name.toLowerCase().includes('logistics') || name.toLowerCase().includes('cost')) return 'Operations';
  if (name.toLowerCase().includes('demand') || name.toLowerCase().includes('forecast')) return 'Inventory';
  if (name.toLowerCase().includes('efficiency') || name.toLowerCase().includes('process')) return 'Manufacturing';
  if (name.toLowerCase().includes('customer') || name.toLowerCase().includes('behavior')) return 'Sales';
  if (name.toLowerCase().includes('pricing') || name.toLowerCase().includes('dynamic')) return 'Pricing';
  if (name.toLowerCase().includes('risk') || name.toLowerCase().includes('assessment')) return 'Risk Management';
  if (name.toLowerCase().includes('sustainability') || name.toLowerCase().includes('impact')) return 'Sustainability';
  if (name.toLowerCase().includes('sales') || name.toLowerCase().includes('prediction')) return 'Sales';
  if (name.toLowerCase().includes('maintenance') || name.toLowerCase().includes('predictive')) return 'Maintenance';
  return 'General';
};

// Helper function to get value pillars based on description keywords
const getValuePillarsFromDescription = (description: string): any[] => {
  const pillars = [];
  const lowerDesc = description.toLowerCase();

  if (lowerDesc.includes('cost') || lowerDesc.includes('saving') || lowerDesc.includes('minimize')) {
    pillars.push('Cost Reduction');
  }
  if (lowerDesc.includes('revenue') || lowerDesc.includes('sales') || lowerDesc.includes('growth')) {
    pillars.push('Revenue Growth');
  }
  if (lowerDesc.includes('customer') || lowerDesc.includes('experience') || lowerDesc.includes('retention')) {
    pillars.push('Customer Experience');
  }
  if (lowerDesc.includes('efficiency') || lowerDesc.includes('optimize') || lowerDesc.includes('streamline')) {
    pillars.push('Operational Efficiency');
  }

  return pillars.length > 0 ? pillars : ['Operational Efficiency'];
};

// Helper function to generate related opportunities
const getRelatedOpportunities = (currentIndex: number, totalItems: number): number[] => {
  const related: number[] = [];
  const numRelated = Math.floor(Math.random() * 3); // 0-2 related opportunities

  for (let i = 0; i < numRelated; i++) {
    let relatedId;
    do {
      relatedId = Math.floor(Math.random() * totalItems) + 1;
    } while (relatedId === currentIndex + 1 || related.includes(relatedId));
    related.push(relatedId);
  }

  return related;
};

export default function OpportunitiesPage() {
  const [opportunities, setOpportunities] = useState<OpportunityData[]>([]);
  const [selectedOpportunity, setSelectedOpportunity] =
    useState<OpportunityData | null>(null);
  const [selectedFilter, setSelectedFilter] = useState<QuadrantFilter>('All');
  const [lastRefreshed, setLastRefreshed] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasLoadedOnce, setHasLoadedOnce] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const itemsPerPage = 10;
  const [searchInput, setSearchInput] = useState<string>("");
  const [sortOption, setSortOption] = useState<string>("all");
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);
  const pendingPageRef = useRef<number | null>(null);
  const dispatch = useDispatch();
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [isSkeleton, setIsSkeleton] = useState<boolean>(false);

  const handleOpportunitySelect = (opportunity: OpportunityData) => {
    setSelectedOpportunity(opportunity);
  };

  const fetchOpportunities = async (
    page: number = 1,
    search: string = "",
    preference?: string,
    sortOptionOverride?: string
  ) => {
    // Only show full loader if it's the very first load (All, empty search, not loaded before)
    if (!hasLoadedOnce && selectedFilter === 'All' && searchInput === "") {
      setIsLoading(true);
      setIsSkeleton(false);
    } else {
      setIsLoading(false);
      setIsSkeleton(true);
    }
    try {
      let sortBy = "";
      let sortOrder = "";
      const sortToUse = sortOptionOverride ?? sortOption;

      if (sortToUse !== "all") {
        const [field, order] = sortToUse.split("-");
        sortBy = field === "value" ? "score" : "completion_score";
        sortOrder = order;
      }

      const response = await getOpportunies(
        page,
        itemsPerPage,
        search,
        sortBy,
        sortOrder,
        preference && preference !== "All" ? preference : undefined
      );
      if (response.success) {
        const transformed = transformJsonToOpportunities(response.data);
        setOpportunities(transformed);
        setTotalPages(response.data.total_pages);
        setLastRefreshed(new Date());
        if (!hasLoadedOnce && selectedFilter === 'All' && searchInput === "") {
          setHasLoadedOnce(true);
        }
        setIsSkeleton(false); // Remove skeleton only after data is ready
      }
    } catch (error) {
      console.error('Error fetching opportunities:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Update chip click handler to always call API with correct chip value
  const handleChipClick = (chip: QuadrantFilter) => {
    if (chip === selectedFilter) return; // Prevent API call if already selected
    setSelectedFilter(chip);
    setCurrentPage(1);
    fetchOpportunities(1, searchInput, chip === 'All' ? undefined : chip);
  };

  // Update search handler to call API with current chip
  const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
    setCurrentPage(1);
    // No API call here; debounce will handle it
  };

  // Update sort handler to call API with current chip
  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSortOption = e.target.value;
    setSortOption(newSortOption);
    setCurrentPage(1);
    fetchOpportunities(1, searchInput, selectedFilter === 'All' ? undefined : selectedFilter, newSortOption);
  };

  // Update pagination handler to call API with current chip
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    fetchOpportunities(newPage, searchInput, selectedFilter === 'All' ? undefined : selectedFilter);
  };

  // Add back handleRefresh for the RefreshButton
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchOpportunities(currentPage, searchInput, selectedFilter === 'All' ? undefined : selectedFilter);
    setIsRefreshing(false);
  };

  // Only reset page to 1 when searchInput changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchInput]);

  // Pre-select the first opportunity when component mounts or opportunities change
  useEffect(() => {
    if (opportunities.length > 0 && !selectedOpportunity) {
      setSelectedOpportunity(opportunities[0]);
    }
  }, [opportunities, selectedOpportunity]);

  // Function to determine which quadrant an opportunity belongs to
  const getQuadrant = (opportunity: OpportunityData): QuadrantFilter => {
    const value = opportunity.valueScore;
    const readiness = opportunity.readinessScore;

    if (value >= 5 && readiness >= 5) return 'Invest';
    if (value >= 5 && readiness < 5) return 'Investigate Further';
    if (value < 5 && readiness >= 5) return 'Entry Strategy';
    return 'Avoid';
  };

  // Remove client-side filtering by chip
  // Remove or comment out allFilteredOpportunities and paginatedOpportunities logic
  // Use only the 'opportunities' array from API for rendering

  const filters: QuadrantFilter[] = ['All', 'Invest', 'Investigate Further', 'Entry Strategy', 'Avoid'];

  useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedFilter, searchInput, totalPages]);

  // Add this effect to auto-select the first opportunity on page change
  useEffect(() => {
    // Calculate filtered opportunities for the current page
    const opportunitiesPerPage = 5;
    const filteredOpportunities = selectedFilter === 'All'
      ? opportunities
      : opportunities.filter(opp => {
        const value = opp.valueScore;
        const readiness = opp.readinessScore;
        let quadrant: QuadrantFilter;
        if (value >= 5 && readiness >= 5) quadrant = 'Invest';
        else if (value >= 5 && readiness < 5) quadrant = 'Investigate Further';
        else if (value < 5 && readiness >= 5) quadrant = 'Entry Strategy';
        else quadrant = 'Avoid';
        return quadrant === selectedFilter;
      });
    const startIndex = (currentPage - 1) * opportunitiesPerPage;
    const endIndex = startIndex + opportunitiesPerPage;
    const currentPageOpportunities = filteredOpportunities.slice(startIndex, endIndex);
    if (currentPageOpportunities.length > 0) {
      setSelectedOpportunity(currentPageOpportunities[0]);
    } else {
      setSelectedOpportunity(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, selectedFilter, opportunities]);

  // On initial mount, fetch 'All' opportunities
  React.useEffect(() => {
    fetchOpportunities(1, "");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Debounce search effect
  useEffect(() => {
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }
    if (searchInput === "") {
      // If search is cleared, fetch immediately
      setIsSearching(false);
      fetchOpportunities(1, "", selectedFilter === 'All' ? undefined : selectedFilter);
      return;
    }
    if (searchInput.length < 3) {
      setIsSearching(false);
      // Do not trigger API call for less than 3 characters
      return;
    }
    setIsSearching(true);
    debounceTimeout.current = setTimeout(() => {
      fetchOpportunities(1, searchInput, selectedFilter === 'All' ? undefined : selectedFilter)
        .finally(() => setIsSearching(false));
    }, 1000);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchInput, selectedFilter]);

  return (
    <div className="min-h-screen p-8 pt-4">
      <div className="mx-auto">
        {isLoading ? (
          <div className="flex items-center justify-center min-h-[60vh]">
            <Loader />
          </div>
        ) : opportunities.length === 0 && selectedFilter === 'All' && searchInput === "" ? (
          <>
            <header className="mb-8 mt-4">
              <h1 className="text-3xl font-normal text-gray-800" style={{ marginBottom: '1.5rem' }}>
                Explore Opportunities - Coming soon
              </h1>
              <p
                className="text-base font-normal text-gray-500"
                style={{
                  maxWidth: "900px",
                  margin: 0
                }}
              >
                The strategic tool designed to help you unearth use-cases in different areas in your business (across varying time horizons). You can use this as a strategic tool to align your organization on the roadmap for transformation, by driving agreement on both the areas to transform as well as the order of attack.
              </p>
            </header>
            <div className="flex items-center justify-center w-full h-[480px]">
              <div className="w-full h-full flex items-center justify-center">
                <img
                  src="/exploreProject.png"
                  alt="No Opportunities"
                  className="object-contain w-full h-full"
                  style={{ display: 'block' }}
                />
              </div>
            </div>
          </>
        ) : (
          <div className="mx-auto">
            <header className="flex flex-row items-center mb-8">
              <div className="flex flex-col w-2/3 justify-between items-start mt-6">
                <h1 className="flex text-3xl items-center font-semibold text-[#111827]">
                  Explore Opportunities
                  <span className="text-sm ml-8 text-gray-400">
                    Last refreshed: {lastRefreshed.toLocaleString()}
                  </span>
                </h1>
                <p className="text-[#4b5563] mt-2">
                  The strategic tool designed to help you unearth use-cases in
                  different areas in your business (across varying time horizons).
                  You can use this as a strategic tool to align your organization on
                  the roadmap for transformation.
                </p>
              </div>

              <div className="flex w-1/3 justify-between items-center mt-6">
                <div></div>
                <div className="flex items-center gap-4">
                  <RefreshButton
                    onRefresh={handleRefresh}
                    isRefreshing={isRefreshing}
                  />
                </div>
              </div>
            </header>

            <div className="flex flex-col gap-8">
              {/* Filter buttons - full width */}
              <div className="bg-white">
                <div className="flex flex-wrap items-center justify-between gap-4">
                  <div className="flex flex-wrap gap-4">
                    {filters.map((filter) => (
                      <button
                        key={filter}
                        onClick={() => handleChipClick(filter)}
                        className={`
                        px-4 py-2 text-sm rounded-full transition-colors font-medium
                        ${selectedFilter === filter
                            ? 'bg-[#00B2A1] text-white'
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                          }
                        `}
                      // Do NOT disable the button when selected
                      >
                        {filter}
                      </button>
                    ))}
                  </div>

                  {/* Search bar and Sort dropdown */}
                  <div className="flex items-center gap-4">
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Search opportunities..."
                        value={searchInput}
                        onChange={handleSearchInput}
                        className="w-64 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#00B2A1] focus:border-transparent pr-10"
                      />
                      {isSearching && (
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#00B2A1] animate-spin">
                          <FaSpinner size={16} />
                        </span>
                      )}
                    </div>
                    <select
                      value={sortOption}
                      onChange={handleSortChange}
                      className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#00B2A1] focus:border-transparent"
                    >
                      <option value="all">Sort by</option>
                      <option value="value-asc">▲ Value - Ascending</option>
                      <option value="value-desc">▼ Value - Descending</option>
                      <option value="readiness-asc">▲ Readiness - Ascending</option>
                      <option value="readiness-desc">▼ Readiness - Descending</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Content area */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Opportunities List */}
                <div className="lg:col-span-1">
                  <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                    {opportunities.length === 0 && !isSkeleton ? (
                      <div className="flex items-center justify-center h-40 text-gray-400 text-lg">
                        No opportunities
                      </div>
                    ) : (
                      <OppsList
                        opportunities={opportunities}
                        onSelectOpportunity={handleOpportunitySelect}
                        selectedOpportunityId={selectedOpportunity?.id}
                        selectedFilter={selectedFilter}
                        isSkeleton={isSkeleton}
                      />
                    )}
                  </div>
                  {/* Pagination - only Previous/Next, bottom right, with padding and hover */}
                  <div className="flex flex-col items-end gap-2 pt-6 pr-2">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1 || totalPages <= 1}
                        className={`px-4 py-1 rounded-md border transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-[#00B2A1] active:scale-95
                          ${currentPage === 1 || totalPages <= 1 ? 'bg-gray-200 text-gray-400 border-gray-200 cursor-not-allowed' : 'bg-[#00B2A1] text-white border-[#00B2A1] hover:bg-[#009688] cursor-pointer'}`}
                      >
                        Previous
                      </button>
                      <button
                        onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages || totalPages <= 1}
                        className={`px-4 py-1 rounded-md border transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-[#00B2A1] active:scale-95
                          ${currentPage === totalPages || totalPages <= 1 ? 'bg-gray-200 text-gray-400 border-gray-200 cursor-not-allowed' : 'bg-[#00B2A1] text-white border-[#00B2A1] hover:bg-[#009688] cursor-pointer'}`}
                      >
                        Next
                      </button>
                    </div>
                    <div className="text-xs text-gray-500">
                      Showing page {currentPage} of {totalPages} pages
                    </div>
                  </div>
                </div>

                {/* Opportunity Detail */}
                <div className="lg:col-span-2">
                  {isSkeleton ? (
                    <OpportunityDetail isSkeleton opportunity={{
                      id: 0,
                      name: '',
                      valueScore: 0,
                      readinessScore: 0,
                      description: '',
                      category: '',
                      fullDescription: '',
                      dataSources: '',
                      lastUpdated: new Date(),
                      valuePillars: [],
                      reason: '',
                      entities: [],
                      departments: [],
                      usecaseIndustryType: undefined,
                      usecaseDepartment: undefined,
                      usecaseModelType: undefined,
                      usecaseCompanySegment: undefined,
                      usecaseDomain: undefined,
                      usecaseCompanyDatabaseType: undefined,
                      add_to_project: undefined,
                      relatedOpportunities: [],
                    }} />
                  ) : selectedOpportunity ? (
                    <OpportunityDetail opportunity={selectedOpportunity} />
                  ) : (
                    <div className="flex items-center justify-center h-full text-gray-500">
                      Select an opportunity to view details
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

