"use client";

import React from 'react';
import { OpportunityData, QuadrantFilter } from '../utils/types';


interface OppsListProps {
  opportunities: OpportunityData[];
  onSelectOpportunity: (opportunity: OpportunityData) => void;
  selectedOpportunityId?: number;
  selectedFilter: QuadrantFilter;
  isSkeleton?: boolean;
}

interface DepartmentChip {
  text: string;
  type: 'model' | 'department';
}

// Skeleton card for loading state
const OppListSkeleton: React.FC = () => (
  <div className="p-2 my-3 rounded border border-gray-200 bg-gray-50 animate-pulse">
    <div className="flex items-center gap-2 mb-2">
      <div className="w-2 h-2 rounded-full bg-gray-300" />
      <div className="h-5 w-32 bg-gray-200 rounded" />
    </div>
    <div className="flex gap-8 mb-2">
      <div className="h-4 w-16 bg-gray-200 rounded" />
      <div className="h-4 w-20 bg-gray-200 rounded" />
    </div>
    <div className="flex gap-2 mb-2">
      <div className="h-4 w-20 bg-gray-100 rounded" />
      <div className="h-4 w-16 bg-gray-100 rounded" />
    </div>
    <div className="flex gap-2">
      <div className="h-4 w-12 bg-gray-100 rounded" />
      <div className="h-4 w-10 bg-gray-100 rounded" />
    </div>
  </div>
);

const OppsList: React.FC<OppsListProps> = ({
  opportunities,
  onSelectOpportunity,
  selectedOpportunityId,
  selectedFilter,
  isSkeleton
}) => {
  // Add pagination state
  const [currentPage, setCurrentPage] = React.useState(1);
  const opportunitiesPerPage = 5;

  // Determine which quadrant an opportunity belongs to
  const getQuadrant = (valueScore: number, readinessScore: number): QuadrantFilter => {
    if (valueScore >= 5 && readinessScore >= 5) return 'Invest';
    if (valueScore >= 5 && readinessScore < 5) return 'Investigate Further';
    if (valueScore < 5 && readinessScore >= 5) return 'Entry Strategy';
    return 'Avoid';
  };

  // Filter opportunities based on selectedFilter
  const getFilteredOpportunities = () => {
    if (selectedFilter === 'All') {
      return opportunities;
    }
    return opportunities.filter(opp => {
      const quadrant = getQuadrant(opp.valueScore, opp.readinessScore);
      return quadrant === selectedFilter;
    });
  };

  const filteredOpportunities = getFilteredOpportunities();
  const totalPages = Math.ceil(filteredOpportunities.length / opportunitiesPerPage);

  // Get current page opportunities
  const getCurrentPageOpportunities = () => {
    const startIndex = (currentPage - 1) * opportunitiesPerPage;
    const endIndex = startIndex + opportunitiesPerPage;
    return filteredOpportunities.slice(startIndex, endIndex);
  };

  const formatEntityText = (text: string) => {
    return text
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Function to get department chips for an opportunity
  const getDepartmentChips = (opportunity: OpportunityData): DepartmentChip[] => {
    let chips: DepartmentChip[] = [];

    // Add usecaseModelType if it exists and is not empty
    if (opportunity.usecaseModelType && opportunity.usecaseModelType.trim() !== '') {
      chips.push({
        text: formatEntityText(opportunity.usecaseModelType),
        type: 'model' as const
      });
    }

    // Add usecaseDepartment if it exists and is not empty
    if (opportunity.usecaseDepartment && opportunity.usecaseDepartment.trim() !== '') {
      chips.push({
        text: formatEntityText(opportunity.usecaseDepartment),
        type: 'department' as const
      });
    }

    return chips;
  };

  const opportunitiesToShow = filteredOpportunities;

  return (
    <div className="flex flex-col gap-4">
      <div className="max-h-[70vh] overflow-y-auto">
        {isSkeleton ? (
          Array.from({ length: 5 }).map((_, idx) => <OppListSkeleton key={idx} />)
        ) : opportunitiesToShow.length === 0 ? (
          <div className="text-center text-gray-400 py-8">
            No opportunities available.
          </div>
        ) : (
          opportunitiesToShow.map(opportunity => {
            const quadrant = getQuadrant(opportunity.valueScore, opportunity.readinessScore);
            const isInSelectedQuadrant = quadrant === selectedFilter;

            let dotColor;
            if (opportunity.id === selectedOpportunityId) {
              dotColor = 'bg-[#00B2A1]'; // Teal for selected
            } else if (isInSelectedQuadrant) {
              dotColor = 'bg-[#3b82f6]'; // Blue for selected quadrant
            } else {
              dotColor = 'bg-gray-500'; // Gray for others
            }

            return (
              <div
                key={opportunity.id}
                className={`
                  p-2 my-3 rounded border text-sm cursor-pointer
                  ${opportunity.id === selectedOpportunityId
                    ? 'border-[#00B2A1] bg-[#00B2A1]/5'
                    : 'border-gray-200 hover:bg-gray-50'}
                `}
                onClick={(e) => {
                  e.stopPropagation();
                  onSelectOpportunity(opportunity);
                }}
              >
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full flex items-center justify-center ${dotColor}`}>
                    {/* <span className="text-white text-xs">{opportunity.id}</span> */}
                  </div>
                  <span className="font-medium text-lg">{opportunity.name}</span>
                </div>
                <div className="mt-1 flex gap-8 text-md font-bold text-gray-900">
                  <span>Value: {opportunity.valueScore.toFixed(1)}</span>
                  <span>Readiness: {opportunity.readinessScore.toFixed(1)}</span>
                </div>

                {/* Department Chips - Added between scores and entity tags */}
                {getDepartmentChips(opportunity).length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {getDepartmentChips(opportunity).map((chip: DepartmentChip, index: number) => (
                      <span
                        key={`dept-${index}`}
                        className={`px-2 py-1 rounded-full text-xs font-medium ${chip.type === 'model'
                          ? 'bg-teal-100 text-teal-700 border border-blue-200'
                          : 'bg-teal-100 text-teal-700 border border-purple-200'
                          }`}
                        onClick={(e) => e.stopPropagation()}
                      >
                        {chip.text}
                      </span>
                    ))}
                  </div>
                )}
                {opportunity.entities && opportunity.entities.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {opportunity.entities.slice(0, 3).map((entity, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs"
                        onClick={(e) => e.stopPropagation()}
                      >
                        {formatEntityText(entity.entity)}
                      </span>
                    ))}
                    {opportunity.entities.length > 3 && (
                      <span
                        className="px-2 py-1 bg-gray-200 text-gray-600 rounded-full text-xs"
                        onClick={(e) => e.stopPropagation()}
                      >
                        +{opportunity.entities.length - 3}
                      </span>
                    )}
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default OppsList;