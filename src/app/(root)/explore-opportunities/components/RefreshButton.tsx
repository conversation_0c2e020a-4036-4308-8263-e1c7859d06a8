"use client";

import React, { useState } from 'react';

interface RefreshButtonProps {
  onRefresh: () => void;
  isRefreshing: boolean;
}

const RefreshButton: React.FC<RefreshButtonProps> = ({ onRefresh, isRefreshing }) => {
  const [showConfirmation, setShowConfirmation] = useState(false);
  
  const handleRefreshClick = () => {
    setShowConfirmation(true);
  };
  
  const handleConfirmRefresh = () => {
    setShowConfirmation(false);
    onRefresh();
  };
  
  const handleCancelRefresh = () => {
    setShowConfirmation(false);
  };
  
  return (
    <div className="relative">
      <button
        className={`
          bg-white border border-[#00B2A1] text-[#00B2A1] px-4 py-2 rounded-md
          ${isRefreshing ? 'opacity-70 cursor-not-allowed' : 'hover:bg-[#00A090] hover:text-white'}
          flex items-center gap-2
        `}
        onClick={handleRefreshClick}
        disabled={isRefreshing}
      >
        {isRefreshing ? (
          <>
            <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Refreshing...
          </>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh Opportunities
          </>
        )}
      </button>
      
      {/* Refresh confirmation modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-lg max-w-md w-full p-6">
            <div className="text-xl font-semibold text-gray-900 mb-2">Confirm Refresh</div>
            
            <div className="mb-6">
              <div className="text-sm text-gray-700 mb-3">
                Refreshing opportunities will analyze your latest data to generate new insights. This is a paid feature that consumes credits.
              </div>
              
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 text-sm text-amber-700 flex items-start gap-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span>This operation will use approximately 10-15 credits depending on the amount of data processed. Are you sure you want to continue?</span>
              </div>
            </div>
            
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                onClick={handleCancelRefresh}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-[#00B2A1] text-white rounded-md hover:bg-[#00A090]"
                onClick={handleConfirmRefresh}
              >
                Confirm Refresh
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RefreshButton;