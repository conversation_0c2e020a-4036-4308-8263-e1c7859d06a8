"use client";

import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, ResponsiveContainer, ReferenceLine, Cell } from 'recharts';
import { OpportunityData, QuadrantFilter } from '../utils/types';
import OppsList from './OppsList';

interface QuadrantViewProps {
  opportunities: OpportunityData[];
  onSelectOpportunity: (opportunity: OpportunityData) => void;
  selectedOpportunityId?: number;
  selectedFilter: QuadrantFilter;
  onFilterChange: (filter: QuadrantFilter) => void;
}

const QuadrantView: React.FC<QuadrantViewProps> = ({
  opportunities,
  onSelectOpportunity,
  selectedOpportunityId,
  selectedFilter,
  onFilterChange
}) => {
  // Determine which quadrant an opportunity belongs to
  const getQuadrant = (valueScore: number, readinessScore: number): QuadrantFilter => {
    if (valueScore >= 5 && readinessScore >= 5) return 'Invest';
    if (valueScore >= 5 && readinessScore < 5) return 'Investigate Further';
    if (valueScore < 5 && readinessScore >= 5) return 'Entry Strategy';
    return 'Avoid';
  };

  // Transform data for recharts
  const chartData = opportunities.map(opportunity => {
    const quadrant = getQuadrant(opportunity.valueScore, opportunity.readinessScore);
    return {
      x: opportunity.readinessScore,
      y: opportunity.valueScore,
      id: opportunity.id,
      name: opportunity.name,
      isSelected: opportunity.id === selectedOpportunityId,
      quadrant: quadrant,
      isInSelectedQuadrant: quadrant === selectedFilter
    };
  });

  const handleDotClick = (data: any, index: number, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event propagation
    const opportunity = opportunities.find(opp => opp.id === data.id);
    if (opportunity) {
      const opportunityQuadrant = getQuadrant(opportunity.valueScore, opportunity.readinessScore);

      // If the clicked opportunity is in a different quadrant than the current filter, switch to that quadrant
      if (opportunityQuadrant !== selectedFilter) {
        onFilterChange(opportunityQuadrant);
      }

      onSelectOpportunity(opportunity);
    }
  };

  const isAllSelected = selectedFilter === 'All';
  const opportunitiesToShow = isAllSelected ? opportunities : opportunities.filter(opportunity => getQuadrant(opportunity.valueScore, opportunity.readinessScore) === selectedFilter);

  return (
    <div className="bg-white overflow-hidden">
      <div className="">
        {/* Quadrant visualization */}
        <div className="w-full h-[35vh] relative bg-gray-50 rounded-lg">
          <ResponsiveContainer width="100%" height="100%">
            <ScatterChart margin={{ top: 24, right: 24, bottom: 24, left: 0 }}>
              <CartesianGrid stroke="none" />
              <XAxis
                type="number"
                dataKey="x"
                domain={[0, 10]}
                label={{ value: 'Readiness Score', position: 'middle', offset: 0 }}
                tick={{ fontSize: 12 }}
              />
              <YAxis
                type="number"
                dataKey="y"
                domain={[0, 10]}
                label={{ value: 'Value Score', angle: -90, position: 'middle' }}
                tick={{ fontSize: 12 }}
              />
              <ReferenceLine x={5} stroke="#e5e5e5" strokeWidth={4} />
              <ReferenceLine y={5} stroke="#e5e5e5" strokeWidth={4} />

              <Scatter data={chartData} onClick={handleDotClick}>
                {chartData.map((entry, index) => {
                  let fillColor;
                  if (entry.isSelected) {
                    fillColor = "#00B2A1"; // Teal for selected
                  } else if (entry.isInSelectedQuadrant) {
                    fillColor = "#3b82f6"; // Blue for selected quadrant
                  } else {
                    fillColor = "#6b7280"; // Gray-500 for others
                  }

                  return (
                    <Cell
                      key={`cell-${index}`}
                      fill={fillColor}
                      stroke={fillColor}
                      strokeWidth={entry.isSelected ? 16 : 8}
                      r={entry.isSelected ? 16 : 14}
                      style={{ cursor: 'pointer' }}
                    />
                  );
                })}
              </Scatter>

              {/* Add labels for opportunity numbers */}
              {/* {chartData.map((entry, index) => (
                <text
                  key={`label-${index}`}
                  x={`${(entry.x / 10) * 100}%`}
                  y={`${100 - (entry.y / 10) * 100}%`}
                  textAnchor="middle"
                  dominantBaseline="central"
                  fontSize="12"
                  fill="white"
                  fontWeight="bold"
                  style={{ pointerEvents: 'none' }}
                >
                  {entry.id}
                </text>
              ))} */}
            </ScatterChart>
          </ResponsiveContainer>

          {/* Quadrant labels */}
          <div className="absolute inset-0 pointer-events-none">
            {/* Top-left quadrant: Investigate further */}
            <div className="absolute top-1/4 left-1/4 transform -translate-x-1/2 -translate-y-1/2 text-gray-400 font-bold">
              Investigate Further
            </div>
            {/* Top-right quadrant: Invest */}
            <div className="absolute top-1/4 right-1/4 transform translate-x-1/2 -translate-y-1/2 text-gray-400 font-bold">
              Invest
            </div>
            {/* Bottom-left quadrant: Avoid */}
            <div className="absolute bottom-1/4 left-1/4 transform -translate-x-1/2 translate-y-1/2 text-gray-400 font-bold">
              Avoid
            </div>
            {/* Bottom-right quadrant: Entry strategy */}
            <div className="absolute bottom-1/4 right-1/4 transform translate-x-1/2 translate-y-1/2 text-gray-400 font-bold">
              Entry Strategy
            </div>
          </div>
        </div>

      </div>
    </div>
  );
};

export default QuadrantView;