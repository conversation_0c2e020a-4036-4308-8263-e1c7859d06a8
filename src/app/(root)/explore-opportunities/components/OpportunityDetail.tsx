"use client";

import React from "react";
import { OpportunityData, ValuePillar } from "../utils/types";
import {
  TbCurrentLocation,
  TbUsers,
  TbTrendingUp,
  TbPigMoney,
} from "react-icons/tb";
import EntityRecommendations from "./EntityRecommendations";
import { useRouter } from "next/navigation";

// Component has been streamlined, with score displays moved to the header

interface OpportunityDetailProps {
  opportunity: OpportunityData;
  isSkeleton?: boolean;
}

// Skeleton for Opportunity Detail
const OpportunityDetailSkeleton: React.FC = () => (
  <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden animate-pulse">
    <div className="p-4 bg-gray-50 border-b border-gray-200">
      <div className="flex justify-between items-center">
        <div>
          <div className="h-6 w-40 bg-gray-200 rounded mb-2" />
          <div className="h-4 w-24 bg-gray-100 rounded" />
        </div>
        <div className="flex items-center gap-8">
          <div className="flex flex-row items-center gap-12">
            <div className="text-center">
              <div className="h-8 w-16 bg-gray-200 rounded mb-1" />
              <div className="h-3 w-10 bg-gray-100 rounded" />
            </div>
            <div className="text-center">
              <div className="h-8 w-16 bg-gray-200 rounded mb-1" />
              <div className="h-3 w-10 bg-gray-100 rounded" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div className="p-6">
      <div className="flex w-full items-center gap-8 mb-8">
        <div className="h-10 w-40 bg-gray-200 rounded" />
      </div>
      <div className="space-y-6">
        <div>
          <div className="h-5 w-32 bg-gray-200 rounded mb-2" />
          <div className="h-4 w-full bg-gray-100 rounded mb-1" />
          <div className="h-4 w-3/4 bg-gray-100 rounded" />
        </div>
        <div>
          <div className="h-5 w-32 bg-gray-200 rounded mb-2" />
          <div className="h-4 w-full bg-gray-100 rounded mb-1" />
        </div>
      </div>
    </div>
  </div>
);

const OpportunityDetail: React.FC<OpportunityDetailProps> = ({
  opportunity,
  isSkeleton
}) => {
  const router = useRouter();
  // Add pagination state
  const [currentPage, setCurrentPage] = React.useState(1);
  const totalPages = opportunity.entities ? opportunity.entities.length : 1;

  const formattedDate = new Date(opportunity.lastUpdated).toLocaleDateString(
    "en-US",
    {
      year: "numeric",
      month: "long",
      day: "numeric",
    }
  );

  // Get pillar icon
  const getPillarIcon = (pillar: ValuePillar) => {
    switch (pillar) {
      case "Operational Efficiency":
        return <TbCurrentLocation className="w-4 h-4" />;
      case "Customer Experience":
        return <TbUsers className="w-4 h-4" />;
      case "Revenue Growth":
        return <TbTrendingUp className="w-4 h-4" />;
      case "Cost Reduction":
        return <TbPigMoney className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const handleStartProject = () => {
    const params = new URLSearchParams({
      opportunity_id: opportunity.id.toString(),
      name: opportunity.name,
      description: opportunity.description,
      tags: opportunity.entities?.map(e => e.entity).join(",") ?? "",
    });
    router.push(`/projects/add?${params.toString()}`);
  };

  if (isSkeleton) return <OpportunityDetailSkeleton />;

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-medium text-[#111827]">
              {opportunity.name}
            </h2>
            <div className="text-sm text-gray-500 mt-1">
              Last updated: {formattedDate}
            </div>
          </div>

          <div className="flex items-center gap-8">
            <div className="flex flex-row items-center gap-12">
              <div className="text-center">
                <div className="text-4xl font-semibold text-gray-800">
                  {opportunity.valueScore.toFixed(1)}
                </div>
                <div className="text-xs text-gray-500">Value</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-semibold text-gray-800">
                  {opportunity.readinessScore.toFixed(1)}
                </div>
                <div className="text-xs text-gray-500">Readiness</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="flex w-full items-center gap-8 mb-8">
          {opportunity.add_to_project ? (
            <div className="bg-[#00B2A1]/10 border border-[#00B2A1] w-full flex flex-col rounded-lg p-4 gap-2">
              <div className="flex flex-row gap-2 justify-between items-center">
                <div className="flex flex-row text-lg text-[#00B2A1] gap-2">
                  Project:
                  <a
                    href={`/projects/dashboard/${opportunity.add_to_project}`}
                    className="inline-block text-md text-[#00B2A1] underline"
                  >
                    {opportunity.name}
                  </a>
                </div>
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-md font-medium text-[#00B2A1]">
                    Active
                  </span>
                </div>
              </div>
              <div className="text-[#00B2A1]">
                You can manage this opportunity through the project linked above.
              </div>
            </div>
          ) : (
            <button
              className="bg-[#00B2A1] text-white px-4 py-2 rounded-md hover:bg-[#00A090]"
              onClick={handleStartProject}
            >
              Start a Project
            </button>
          )}
        </div>

        {/* Description and details */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Description
            </h3>
            <p className="text-gray-700">{opportunity.fullDescription}</p>
          </div>

          {/* Value Rationale */}
          {opportunity.reason && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Value Rationale
              </h3>
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <p className="text-gray-700">{opportunity.reason}</p>
              </div>
            </div>
          )}

          {/* Value Pillars */}
          {/* {opportunity.valuePillars && opportunity.valuePillars.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Value Pillars
              </h3>
              <div className="flex flex-wrap gap-2">
                {opportunity.valuePillars.map((pillar, index) => (
                  <span
                    key={index}
                    className="px-3 py-1.5 rounded-full text-sm font-medium bg-gray-100 text-gray-700 flex items-center gap-1.5"
                  >
                    <span className="text-gray-500">
                      {getPillarIcon(pillar)}
                    </span>
                    {pillar}
                  </span>
                ))}
              </div>
            </div>
          )} */}

          {/* Entity Categories & Data Sources */}
          {opportunity.entities && opportunity.entities.length > 0 && (
            <EntityRecommendations
              entities={opportunity.entities}
              currentPage={currentPage}
              totalPages={totalPages}
              setCurrentPage={setCurrentPage}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default OpportunityDetail;