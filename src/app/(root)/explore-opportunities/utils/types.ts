export type ValuePillar =
  | "Cost Reduction"
  | "Revenue Growth"
  | "Customer Experience"
  | "Operational Efficiency";

export type ProjectInfo = {
  projectId: string;
  projectName: string;
  projectReference: string;
  projectUrl: string;
  createdDate: Date;
  status: "active" | "completed" | "on-hold";
};

export type AddToProjectInfo = {
  id: string;
  name: string;
  dashboardUrl: string;
};

export type OpportunityData = {
  id: number;
  name: string;
  valueScore: number;
  readinessScore: number;
  description: string;
  category: string;
  fullDescription: string;
  dataSources: string;
  lastUpdated: Date;
  relatedOpportunities: number[];
  valuePillars?: ValuePillar[];
  // Additional fields from JSON data
  _id?: string;
  unique_id?: string;
  status?: string;
  score?: number;
  reason?: string;
  completion_score?: number;
  employee_size?: number;
  location?: string;
  domain?: string;
  turnover_in_millions?: number;
  company_segment?: string;
  company_description?: string;
  entities?: any[];
  // Project information if opportunity has been converted
  project?: ProjectInfo;

   // New department-related fields
  departments?: string[];
  usecaseIndustryType?: string;
  usecaseDepartment?: string;
  usecaseCompanySegment?: string;
  usecaseDomain?: string;
  usecaseModelType?: string;
  usecaseCompanyDatabaseType?: string;
  add_to_project?: string; // project ID
};

export type QuadrantFilter = 'All' | 'Invest' | 'Investigate Further' | 'Entry Strategy' | 'Avoid';

