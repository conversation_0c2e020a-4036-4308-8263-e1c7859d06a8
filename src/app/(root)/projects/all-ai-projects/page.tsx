"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { AIBoxModel, AIProject } from "@/service/types/types";
import { getAiBoxes, getAiBoxResponse } from "@/service/aiServices";
import Loader from "@/components/common/loader/loader";

export default function AllAIProjects() {
  const [searchQuery, setSearchQuery] = useState("");
  const router = useRouter();
  const [aiBox, setAiBox] = useState<getAiBoxResponse | null>(null);
  const [loading, setLoading] = useState(true);

  // Filter cards based on search query
  const filteredCards = aiBox?.data.filter((card) =>
    card.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    const fetchAIProjects = async () => {
      try {
        const aiData = await getAiBoxes();
        setAiBox(aiData);
      } catch (err: any) {
        //setToast({message:"Failed to fetch projects",type:"error"})
        //console.log(err);
      } finally {
        setLoading(false);
      }
    };
    fetchAIProjects();
  }, []);

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {loading ? (
        <Loader />
      ) : (
        <>
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">All AI Projects</h1>
          </div>

          {/* Search Bar */}
          <div className="mb-6">
            <input
              type="text"
              placeholder="Search AI Projects..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400"
            />
          </div>

          {/* Grid Layout */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {(filteredCards ?? []).length > 0 ? (
              filteredCards?.map((card, index) => (
                <div
                  key={index}
                  onClick={() =>
                    router.push(
                      `/projects/all-ai-projects/ai-project/${card._id}`
                    )
                  }
                  className="min-w-[260px] bg-white p-4 rounded-[15px] border border-[#CFD2DE] cursor-pointer hover:border-[#00B2A1]"
                >
                  <img
                    className="object-cover"
                    src={card.thumbnailUrl}
                    height={50}
                    width={50}
                    alt="Thumbnail"
                  />
                  <h3 className="font-semibold text-lg mt-2 mb-1">
                    {card.name}
                  </h3>
                  <p className="text-gray-500 text-sm mt-1">
                    {card.description}
                  </p>
                </div>
              ))
            ) : (
              <p className="text-center col-span-full text-gray-500">
                No projects found.
              </p>
            )}
          </div>
        </>
      )}
    </div>
  );
}
