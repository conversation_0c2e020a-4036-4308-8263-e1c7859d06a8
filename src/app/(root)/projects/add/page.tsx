"use client";

import { FormE<PERSON>, useCallback, useEffect, useRef, useState } from "react";
import { datasetService } from "@/service/datasetService";
import { createProject } from "@/service/projects";
import { Info, ChevronDown, X } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";

import Image from "next/image";
import AIAvatarDisableModal from "@/components/common/avatarDialog/avatar_dialog";
import { ToastState } from "@/components/pages/project/tabs/manage";
import { Toast } from "@/components/ui/toast/toast";
import { useDispatch } from "react-redux";
import { setProjectActiveTab } from "@/lib/store/features/project/projectTabSlice";
import {
  getUseCaseRecommendations,
  UseCaseRecommendationsResponse,
} from "@/service/agentXService";
import { PiInfoBold } from "react-icons/pi";
import Checkbox from "@/components/ui/checkbox/page";

import { debounce } from "lodash";
import Tooltip from "@/components/ui/tooltip/Tooltip";
import { setActiveAssetTab } from "@/lib/store/features/project/projectAssetsSlice";
import { setActiveManageTab } from "@/lib/store/features/project/projectManageSlice";
import { submitOpportunityAction } from "@/service/agentEOService";

export interface FormDataProject {
  name: string;
  description: string;
  tags: string[];
  appType: string;
  expectedReturn: string;
  department: string[];
  experimentalProject: boolean;
  sensitive: boolean;
  aiAvatar: boolean;
}

export default function AddProject() {
  const [formData, setFormData] = useState<FormDataProject>({
    name: "",
    description: "",
    tags: [],
    appType: "",
    expectedReturn: "",
    experimentalProject: false,
    sensitive: false,
    department: [],
    aiAvatar: true,
  });

  const [isDepartmentDropdownOpen, setIsDepartmentDropdownOpen] =
    useState(false);
  const toggleBackground = formData.aiAvatar
    ? "bg-gradient-to-r from-teal-300/30 to-transparent"
    : "bg-gradient-to-r from-gray-300/30 to-transparent";
  const [isNameValid, setIsNameValid] = useState(true);
  const [isDescriptionValid, setIsDescriptionValid] = useState(true);
  const [isDepartmentValid, setisDepartmentValid] = useState(true);
  const [isAppTypeValid, setisAppTypeValid] = useState(true);
  const [departmentList, setdepartmentList] = useState<any>(null);
  const [searchTag, setSearchTag] = useState(""); // Track search input
  const [isTagDropdownOpen, setIsTagDropdownOpen] = useState(false);
  const [tagList, setTagList] = useState<any>(null);
  const [isAppTypeDropdownOpen, setAppTypeDropdownOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(false);
  const [toast, setToast] = useState<ToastState>(null);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isOpen, setIsOpen] = useState(true);
  const [suggestions, setSuggestions] =
    useState<UseCaseRecommendationsResponse | null>(null);
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const opportunityId = searchParams.get("opportunity_id");

  useEffect(() => {
    const name = searchParams.get("name");
    const description = searchParams.get("description");
    const tags = searchParams.get("tags");
    if (name || description || tags) {
      setFormData((prev) => ({
        ...prev,
        name: name || prev.name,
        description: description || prev.description,
        tags: tags ? tags.split(",") : prev.tags,
      }));
      // Trigger use case recommendation API if name is prefilled and valid
      if (name && name.trim().length >= 3 && formData.aiAvatar) {
        setLoadingSuggestions(true);
        getSuggestions(name);
      }
    }
  }, [searchParams]);

  useEffect(() => {
    const dontShowAgainSetting = localStorage.getItem(
      "aiAvatarDontShowAgaincreate"
    );
    if (dontShowAgainSetting === "true") {
      setDontShowAgain(true);
    }
  }, []);

  useEffect(() => {
    localStorage.removeItem("aiAvatarDontShowAgaincreate"); // Clear it on page reload (for development only)
  }, []);
  const router = useRouter();

  const handleToggleAIAvatar = async () => {
    if (formData.aiAvatar && !dontShowAgain) {
      setShowModal(true);
    } else {
      setFormData({ ...formData, aiAvatar: !formData.aiAvatar });
      setShowSuggestions(false);
    }
  };

  const handleConfirmDisable = async () => {
    setShowModal(false);
    if (dontShowAgain) {
      localStorage.setItem("aiAvatarDontShowAgaincreate", "true");
    }
    setFormData({ ...formData, aiAvatar: false });
    setShowSuggestions(false);
  };

  const appTypeOptions = [
    {
      type: "Dashboard",
      desc: "A data visualization project designed to display key metrics and insights in real-time.",
      icon: "/assets/icons/dash_option.svg",
    },
    {
      type: "Application",
      desc: "A full-featured software application that provides interactive functionality and user-driven processes.",
      icon: "/assets/icons/app_option.svg",
    },
    {
      type: "APIs",
      desc: "A backend service providing data and operations that can be consumed by otherapplications or services.",
      icon: "/assets/icons/api_option.svg",
    },
  ];

  const selectAppType = (appType: string) => {
    setFormData({ ...formData, appType: appType });
    setAppTypeDropdownOpen(false);
  };

  const [exploreUrlQuery, setExploreUrlQuery] = useState({
    page: 1,
    limit: 10,
    search: "",
  });

  const fetchExploreData = async () => {
    try {
      const result = await datasetService.getDepartmentList(exploreUrlQuery);
      setdepartmentList(result.data);
    } catch (err) {
      setToast({ message: "Failed to fetch data.", type: "error" });
    }
  };
  useEffect(() => {
    fetchExploreData();
  }, [exploreUrlQuery]);

  const toggleDepartment = (dept: string) => {
    setFormData((prevData) => ({
      ...prevData,
      department: prevData.department.includes(dept)
        ? prevData.department.filter((d) => d !== dept)
        : [...prevData.department, dept],
    }));
    setIsDepartmentDropdownOpen(false);
  };
  const toggleTag = (tagName: string) => {
    setFormData((prevData) => ({
      ...prevData,
      tags: prevData.tags.includes(tagName)
        ? prevData.tags.filter((tag) => tag !== tagName)
        : [...prevData.tags, tagName],
    }));
    setIsTagDropdownOpen(false); // Close dropdown after selection
  };

  const fetchTagsData = async (search: string) => {
    if (!search) {
      setTagList(null);
      return;
    }

    try {
      const result = await datasetService.getTagList({
        search,
        type: "tag", // Ensuring we only fetch tags
        page: 1,
        limit: 10,
      });
      setTagList(result.data);
      setIsTagDropdownOpen(true);
    } catch (err) {
      setToast({ message: "Failed to fetch tag data.", type: "error" });
    }
  };
  const handleTagSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTag(value);
    fetchTagsData(value);
  };

  const handleTagSubmit = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      const value = searchTag.trim();
      if (!value) return;

      if (
        !tagList?.tags?.some(
          (tag: any) => tag.name.toLowerCase() === value.toLowerCase()
        )
      ) {
        toggleTag(value);
      }
      setSearchTag("");
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (!event.target.closest(".tag-dropdown")) {
        setIsTagDropdownOpen(false);
      }
      if (!event.target.closest(".department-dropdown")) {
        setIsDepartmentDropdownOpen(false);
      }
      if (!event.target.closest(".project-dropdown")) {
        setAppTypeDropdownOpen(false);
      }
    };
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  const handleProjectCreation = async () => {
    if (formData.name.trim() === "") {
      setIsNameValid(false);
      return;
    } else {
      setIsNameValid(true);
    }
    if (formData.description.trim() === "") {
      setIsDescriptionValid(false);
      return;
    } else {
      setIsDescriptionValid(true);
    }
    if (formData.appType === "") {
      setisAppTypeValid(false);
      return;
    } else {
      setisAppTypeValid(true);
    }
    if (formData.department.length === 0) {
      setisDepartmentValid(false);
      return;
    } else {
      setisDepartmentValid(true);
    }

    setLoading(true);
    try {
      const response = await createProject(formData);
      if (typeof opportunityId === "string" && response.data?.project?._id) {
        submitOpportunityAction({
          mongo_id: opportunityId,
          add_to_project: response.data.project._id,
        }).catch((err) => {
          console.error("Failed to submit opportunity action", err);
        });
        dispatch(setProjectActiveTab("Overview"));
        dispatch(setActiveAssetTab("All Assets"));
        dispatch(setActiveManageTab("General"));
      }
      router.push(`/projects/dashboard/${response.data.project._id}`);
    } catch (error: any) {
      setToast({
        message: error?.data?.message || "Failed to create project",
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const getDepartmentNames = (allDepts: any[], selectedDepts: String[]) => {
    let departments = "";
    selectedDepts.forEach((deptId) => {
      const dept = allDepts.find((d) => d._id === deptId);
      if (dept) {
        departments += dept.name + ", ";
      }
    });
    departments = departments.slice(0, -2);
    return departments;
  };

  const getSuggestions = async (query: string) => {
    if (query.trim().length < 3 || !formData.aiAvatar) {
      setSuggestions(null);
      setShowSuggestions(false);
      setLoadingSuggestions(false);
      return;
    }
    try {
      const res = await getUseCaseRecommendations(query);
      if (res) {
        setSuggestions(res);
        setShowSuggestions(true);
      }
    } catch (error) {
      //setToast({message:"Failed to fetch suggestions",type:"error"})
      setShowSuggestions(false);
    } finally {
      setLoadingSuggestions(false);
    }
  };

  const debouncedFetchRef = useRef(
    debounce((text: string) => getSuggestions(text), 1000)
  );

  const handleNameChange = async (e: any) => {
    setFormData({ ...formData, name: e.target.value });
    if (formData.aiAvatar) debouncedFetchRef.current(e.target.value);
  };

  const skipEffect = useRef(false);

  useEffect(() => {
    if (skipEffect.current) {
      skipEffect.current = false; // Reset the flag after skipping once
      return;
    }
    if (formData.name.trim().length >= 3 && formData.aiAvatar) {
      setLoadingSuggestions(true);
      setShowSuggestions(true);
      setIsOpen(true);
    }
  }, [formData.name]);

  const handlePopulateProject = (
    name: string,
    desc: string,
    tags: string[]
  ) => {
    skipEffect.current = true;
    setFormData({ ...formData, name: name, description: desc, tags: tags });
    setShowSuggestions(false);
    setLoadingSuggestions(false);
  };

  function formatString(input: string): string {
    if (!input) return "";
    return input
      .split("_") // Split by underscore
      .join(" "); // Join with space
  }

  useEffect(() => {
    return () => debouncedFetchRef.current.cancel();
  }, []);

  return (
    <div>
      <style jsx>{`
        .tooltip {
          position: relative;
          display: inline-block;
        }

        .tooltip .tooltiptext {
          visibility: hidden;
          minwidth: 200px;
          background-color: #fff;
          color: #555;
          text-align: center;
          border-radius: 6px;
          padding: 5px;
          position: absolute;
          box-shadow: 0px 0px 12px #00000029;
          z-index: 100;
          bottom: 125%;
          left: 50%;
          margin-left: -60px;
          opacity: 0;
          transition: opacity 0.3s;
          white-space: nowrap;
        }

        .tooltip:hover .tooltiptext {
          visibility: visible;
          opacity: 1;
        }
      `}</style>
      <div className="overflow-auto px-8">
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
            dismissTime={5000}
          />
        )}
        <div className="flex gap-8">
          <div className="tx-6 text-2xl mt-6">Create a Project</div>
          <div className={`p-4 text-center ${toggleBackground}`}>
            <div className="flex items-center gap-2 text-lg font-medium mb-2">
              <img src="/assets/icons/ai-avatar-icon.svg" alt="AI Avatar" />
              <span>AI Avatar</span>
              <label className="relative inline-block w-9 h-4">
                <input
                  type="checkbox"
                  className="opacity-0 w-0 h-0"
                  checked={formData.aiAvatar}
                  onChange={() => handleToggleAIAvatar()}
                />
                <span
                  className={`absolute cursor-pointer top-[-1px] left-0 right-0 bottom-0 transition rounded-full ${formData.aiAvatar ? "bg-teal-500" : "bg-gray-300"
                    }`}
                >
                  <span
                    className={`absolute h-2.5 w-2.5 left-1 bottom-1 bg-white transition rounded-full ${formData.aiAvatar ? "translate-x-5" : "translate-x-0"
                      }`}
                  />
                </span>
              </label>
            </div>
            <div className="text-sm text-gray-600">
              This feature suggests relevant data assets and supports project development.
            </div>
          </div>
        </div>
        <hr />
        <div className="tx-6 text-xl my-6">Details</div>
        {/* Name input */}
        <div className="">
          <label className="mb-2 flex items-center">
            Name <span className="text-red-500">*</span>
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">Enter a clear project name</span>
            </div>
          </label>
          <div>
            <input
              type="text"
              className="w-full p-2 border rounded focus:outline-none focus:border-teal-500"
              value={formData.name}
              onChange={(e) => {
                if (e.target.value.length <= 50) {
                  handleNameChange(e);
                }
              }}
              maxLength={50}
              placeholder="Eg. Sales Report"
            />
            <div className="flex justify-end mt-1">
              <span className={`text-xs ${formData.name.length >= 50 ? 'text-red-500' : 'text-gray-500'}`}>
                {formData.name.length}/50
              </span>
            </div>
          </div>
          {!isNameValid && (
            <div className="text-sm text-red-500 pt-2">Name is mandatory *</div>
          )}
        </div>

        {/* Suggestions */}
        {showSuggestions && (
          <div className="shadow-md rounded-md mt-2">
            <div
              className="flex justify-between items-center p-3 bg-slate-100 cursor-pointer"
              onClick={() => setIsOpen(!isOpen)}
            >
              <div className="flex gap-2 items-center">
                <img src="/assets/icons/ai-avatar-icon.svg" alt="#" />
                <h1 className="text-md">
                  Pre-fill your project with one of these suggestions, to have
                  the best success...
                </h1>
              </div>

              <span>
                <ChevronDown
                  className={`w-6 h-6 cursor-pointer transition-transform duration-300 ${isOpen ? "rotate-180" : "rotate-0"
                    }`}
                  onClick={() => setIsOpen(!isOpen)}
                />
              </span>
            </div>

            <div
              className={`transition-all duration-500 overflow-hidden ${isOpen ? "opacity-100 mt-2" : "max-h-0 opacity-0"
                }`}
            >
              {loadingSuggestions ? (
                <div className="w-full h-24 rounded-lg animate-pulse p-3 mt-2">
                  <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                </div>
              ) : (
                suggestions?.data.use_cases.map((item, index) => (
                  <div key={index}>
                    <div className="flex justify-between items-center p-5">
                      <div className="w-3/4">
                        <div className="flex gap-2">
                          <img src="/assets/icons/ai-avatar-icon.svg" alt="#" />
                          <h2 className="text-[18px] text-[#3B4154] font-semibold">
                            {item?.Usecase?.match(/\*\*(.*?)\*\*:(.*)/)?.[1] ??
                              ""}
                          </h2>
                          <div className="flex gap-1 items-center mt-1">
                            <button className=" px-2 border-2 border-teal-500 text-teal-500 rounded-md text-sm">
                              {Math.ceil(item.Semantic_Score * 100)}% Confidence
                            </button>
                            <div>
                              <Tooltip text="Relevance score for your search results" />
                            </div>
                          </div>
                        </div>
                        <p className="mt-2 text-slate-500 line-clamp-2">
                          {item?.Usecase?.match(
                            /\*\*(.*?)\*\*:(.*)/
                          )?.[2].trim() ?? ""}
                        </p>
                        <div className="mt-4 flex flex-wrap gap-2">
                          {item?.Expected_Example_Output &&
                            Array.isArray(item.Expected_Example_Output) &&
                            item.Expected_Example_Output.length > 0 &&
                            item.Expected_Example_Output.map((tag, idx) => (
                              <span
                                key={idx}
                                className="px-3 py-1 text-sm bg-gray-300 text-gray-700 rounded-md"
                              >
                                {formatString(tag.name)}
                              </span>
                            ))}
                        </div>
                      </div>
                      <div className="w-1/4 flex justify-end">
                        <button
                          className="px-3 py-1 border-2 border-teal-500 text-teal-500 rounded-md hover:bg-teal-500 hover:text-white"
                          onClick={() =>
                            handlePopulateProject(
                              item?.Usecase?.match(/\*\*(.*?)\*\*:(.*)/)?.[1] ??
                              "",
                              item.Usecase.match(
                                /\*\*(.*?)\*\*:(.*)/
                              )?.[2].trim() ?? "",
                              Array.isArray(item?.Expected_Example_Output)
                                ? item.Expected_Example_Output.map((tag) => tag.name)
                                : []
                            )
                          }
                        >
                          Populate Project
                        </button>
                      </div>
                    </div>
                    {index !== suggestions?.data.use_cases.length - 1 && (
                      <div className="border-b-2 mx-5"></div>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>
        )}

        {/* Description input */}
        <div className="mt-6 mb-6">
          <label className=" mb-2 flex items-center">
            Description <span className="text-red-500">*</span>
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">
                Briefly describe the purpose of this project
              </span>
            </div>
          </label>
          <textarea
            className="w-full p-2 border rounded focus:outline-none focus:border-teal-500"
            value={formData.description}
            onChange={(e) =>
              setFormData({ ...formData, description: e.target.value })
            }
            placeholder="Eg. What's the purpose of this project?"
            rows={4}
          />
          {!isDescriptionValid && (
            <div className="text-sm text-red-500 pt-2">
              Description is mandatory *
            </div>
          )}
        </div>

        <div className="flex gap-4">
          {/* Project type input */}
          <div className="w-1/2 relative project-dropdown">
            <label className=" mb-2 flex items-center">
              Project Type <span className="text-red-500">*</span>
              <div className="tooltip ml-1">
                <Info className="w-4 h-4 text-gray-400" />
                <span className="tooltiptext">Select the type of project</span>
              </div>
            </label>
            <div
              className="w-full p-2 border rounded focus:outline-none focus:border-teal-500 cursor-pointer flex justify-between items-center"
              onClick={() => setAppTypeDropdownOpen(!isAppTypeDropdownOpen)}
            >
              <span>{formData.appType || "Select Project Type"}</span>
              <ChevronDown className="w-4 h-4" />
            </div>
            {isAppTypeDropdownOpen && (
              <div className="absolute z-10 w-full mt-1 bg-white border rounded shadow-lg">
                {appTypeOptions.map((appType) => (
                  <div
                    className="flex items-center gap-2 px-2 py-1 hover:bg-gray-100"
                    key={appType.type}
                  >
                    <Image
                      src={appType.icon}
                      alt="Icon"
                      width={18}
                      height={18}
                    />
                    <div
                      className="p-2  cursor-pointer"
                      onClick={() => selectAppType(appType.type)}
                    >
                      {appType.type}
                      <div className="text-sm text-gray-500 pt-1">
                        {appType.desc}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
            {!isAppTypeValid && (
              <div className="text-sm text-red-500 pt-2">
                Project Type is mandatory *
              </div>
            )}
          </div>
          {/* expected retrun input */}
          {/* <div className="mb-6 flex-1">
            <label className="mb-2 flex items-center">
              Expected Return
              <div className="tooltip ml-1">
                <Info className="w-4 h-4 text-gray-400" />
                <span className="tooltiptext">
                  Specify the expected return percentage
                </span>
              </div>
            </label>
            <input
              type="number"
              className="w-full p-2 border rounded focus:outline-none focus:border-teal-500 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
              value={formData.expectedReturn}
              onChange={(e) => {
                const value = e.target.value;
                const numericValue = parseFloat(value);

                if (
                  value === "" ||
                  (!isNaN(numericValue) && numericValue <= 100)
                ) {
                  setFormData({ ...formData, expectedReturn: value });
                }
              }}
              placeholder="Eg. 10%"
              max={100}
            />
          </div> */}
          {/* department input */}
          <div className="mb-6 flex-1 relative department-dropdown">
            <label className="mb-2 flex items-center">
              Department Type<span className="text-red-500">*</span>
              <div className="tooltip ml-1">
                <Info className="w-4 h-4 text-gray-400" />
                <span className="tooltiptext">
                  Select the relevant departments for this project
                </span>
              </div>
            </label>
            <div
              className="w-full p-2 border rounded focus:outline-none focus:border-teal-500 cursor-pointer flex justify-between items-center"
              onClick={() =>
                setIsDepartmentDropdownOpen(!isDepartmentDropdownOpen)
              }
            >
              <span>
                {formData.department.length
                  ? getDepartmentNames(
                    departmentList?.departments,
                    formData.department
                  )
                  : "Select Departments"}
              </span>
              <ChevronDown className="w-4 h-4" />
            </div>
            {isDepartmentDropdownOpen && (
              <div className="absolute z-10 w-full mt-1 bg-white border rounded shadow-lg">
                {departmentList?.departments?.map((dept: any) => (
                  <div
                    key={dept._id}
                    className="p-2 hover:bg-gray-100 cursor-pointer flex items-center"
                    onClick={() => toggleDepartment(dept._id)}
                  >
                    <Checkbox
                      id="Department"
                      label={dept.name}
                      checked={formData.department.includes(dept._id)}
                      onChange={() => { }}
                    />
                  </div>
                ))}
              </div>
            )}
            {!isDepartmentValid && (
              <div className="text-sm text-red-500 pt-2">
                Department is mandatory *
              </div>
            )}
          </div>
        </div>
        {/* Tags input */}
        <div className="mb-6 relative">
          <label className="mb-2 flex items-center gap-1">
            Tags
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">
                Enter relevant keywords to categorize the project
              </span>
            </div>
          </label>

          <div
            className="flex flex-wrap items-center gap-2 p-2 border rounded focus-within:border-teal-500 min-h-[42px]"
            onClick={() => document.getElementById("tagInput")?.focus()}
          >
            {formData.tags.map((tag: string) => (
              <span
                key={tag}
                className="bg-gray-200 px-2 py-1 rounded flex items-center gap-1"
              >
                {formatString(tag)}
                <button
                  onClick={() => toggleTag(tag)}
                  className="ml-1 text-gray-500 hover:text-gray-700"
                >
                  ×
                </button>
              </span>
            ))}

            <input
              id="tagInput"
              type="text"
              value={searchTag}
              onChange={(e) => {
                setSearchTag(e.target.value);
                fetchTagsData(e.target.value);
              }}
              onKeyDown={(e) => {
                if (
                  e.key === "Backspace" &&
                  searchTag === "" &&
                  formData.tags.length
                ) {
                  toggleTag(formData.tags[formData.tags.length - 1]);
                }
                handleTagSubmit(e);
              }}
              className="flex-1 min-w-[120px] outline-none"
              placeholder="Search tags"
            />
          </div>

          {isTagDropdownOpen && (
            <div className="absolute z-10 w-full mt-1 bg-white border rounded shadow-lg">
              {searchTag.trim().length > 3 && (
                <div
                  className="p-2 hover:bg-gray-100 cursor-pointer flex items-center"
                  onClick={() => {
                    toggleTag(searchTag.trim());
                    setSearchTag("");
                  }}
                >
                  {searchTag.trim()}
                </div>
              )}
              {tagList?.tags?.length > 0 &&
                tagList.tags.map((tag: any) => (
                  <div
                    key={tag.name}
                    className="p-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => {
                      toggleTag(tag.name);
                      setSearchTag("");
                    }}
                  >
                    {tag.name}
                  </div>
                ))}
            </div>
          )}
        </div>

        {/* Experimental input */}
        <div className="flex gap-4 items-start">
          <label className="mb-2 flex items-center gap-1">
            Experimental Project
            <div className="tooltip ml-1">
              <Info className="w-4 h-4 text-gray-400" />
              <span className="tooltiptext">
                Toggle to make this as an experimental project
              </span>
            </div>
          </label>
          <label className="relative inline-block w-9 h-4 mt-1">
            <input
              type="checkbox"
              className="opacity-0 w-0 h-0"
              checked={formData.experimentalProject}
              onChange={() =>
                setFormData({
                  ...formData,
                  experimentalProject: !formData.experimentalProject,
                })
              }
            />
            <span
              className={`absolute cursor-pointer top-[-1px] left-0 right-0 bottom-0 transition rounded-full ${formData.experimentalProject ? "bg-teal-500" : "bg-gray-300"
                }`}
            >
              <span
                className={`absolute h-2.5 w-2.5 left-1 bottom-1 bg-white transition rounded-full ${formData.experimentalProject
                  ? "translate-x-5"
                  : "translate-x-0"
                  }`}
              />
            </span>
          </label>
        </div>
        <hr className="my-5" />
        {/* sensative input */}
        <div className="mb-2">Advanced settings</div>
        <div className="flex gap-4 items-start">
          <label className="relative inline-block w-9 h-4 mt-1">
            <input
              type="checkbox"
              className="opacity-0 w-0 h-0"
              checked={formData.sensitive}
              onChange={() =>
                setFormData({ ...formData, sensitive: !formData.sensitive })
              }
            />
            <span
              className={`absolute cursor-pointer top-[-1px] left-0 right-0 bottom-0 transition rounded-full ${formData.sensitive ? "bg-teal-500" : "bg-gray-300"
                }`}
            >
              <span
                className={`absolute h-2.5 w-2.5 left-1 bottom-1 bg-white transition rounded-full ${formData.sensitive ? "translate-x-5" : "translate-x-0"
                  }`}
              />
            </span>
          </label>
          <label className="mb-2 flex items-center gap-1 text-[15px]">
            Mark as sensitive
          </label>
        </div>
        <div className="flex gap-4 items-center justify-end mb-6">
          <button
            className=" text-[#00B2A1]"
            onClick={() => window.history.back()}
          >
            Cancel
          </button>
          <button
            className="text-white bg-[#00B2A1] rounded-md px-10 py-1.5 text-center flex items-center justify-center"
            onClick={handleProjectCreation}
            disabled={loading} // Disable button while loading
          >
            {loading ? (
              <div className="animate-spin border-2 border-white border-t-transparent rounded-full h-5 w-5"></div>
            ) : (
              "Create"
            )}
          </button>
        </div>
        {/* Modal component */}
        <AIAvatarDisableModal
          showModal={showModal}
          setShowModal={setShowModal}
          dontShowAgain={dontShowAgain}
          setDontShowAgain={setDontShowAgain}
          handleConfirmDisable={handleConfirmDisable}
        />
      </div>
    </div>
  );
}
