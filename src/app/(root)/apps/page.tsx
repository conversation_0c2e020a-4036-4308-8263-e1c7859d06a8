"use client";

import { useState, useEffect, useMemo, useRef } from "react";
import { Button } from "@/components/ui/deployed-projects/button";
import { Input } from "@/components/ui/deployed-projects/input";
import { usePathname, useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/lib/store";
import { toggleFavorite } from "@/lib/store/features/favorites/favoritesSlice";
import { Toast } from "@/components/ui/toast/toast";
import { getDeployedProjects } from "@/service/deployedProjects";
import { FavoriteToggleProject } from "@/service/types/types";
import { useSidebar } from "@/context/sidebar-context";
import { getToken } from "@/service/methods";

interface Project {
  id: string;
  name: string;
  app_type: "Dashboard" | "API" | "App";
  performance: "Good" | "Fair" | "Poor";
  app_url: string;
  is_fav?: boolean;
  isAiBox?: boolean;
}

function mapApiDataToProject(pd: any): Project {
  return {
    id: pd._id,
    name: pd.name,
    app_type: pd.appType === "API" ? "API" : pd.appType === "Dashboard" ? "Dashboard" : "App",
    performance:
      pd.performance === "Optimal" || pd.performance === "Good" ? "Good" : pd.performance === "Fair" ? "Fair" : "Poor",
    app_url: pd.appUrl || "",
    isAiBox: pd.isAiBox || false,
  };
}

export default function DeployedProjects() {
  const { isOpen } = useSidebar();

  const [baseProjects, setBaseProjects] = useState<Project[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [toast, setToast] = useState<{ message: string; type: "success" | "error" } | null>(null);
  const [favoriteLoading, setFavoriteLoading] = useState<string | null>(null);

  // Tooltip state: which project is hovered for API "Favorites" tooltip
  const [tooltipProjectId, setTooltipProjectId] = useState<string | null>(null);

  const favorites = useSelector((state: RootState) => state.favorites.items);
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const pathname = usePathname();
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  const valuePillars = JSON.parse(localStorage.getItem("valuePillars") || "[]");

  useEffect(() => {
    (async () => {
      try {
        const deployedProjects = await getDeployedProjects();
        const mapped = deployedProjects.map(mapApiDataToProject);
        setBaseProjects(mapped);
      } catch {
        // handle error if needed
      }
    })();
  }, []);

  const mergedProjects = useMemo(() => {
    return baseProjects.map((proj) => {
      const isFav = favorites.some((f) => f.id === proj.id);
      return { ...proj, is_fav: isFav };
    });
  }, [baseProjects, favorites]);

  const filteredProjects = useMemo(() => {
    if (searchQuery.length > 1) {
      return mergedProjects.filter((p) => p.name.toLowerCase().includes(searchQuery.toLowerCase()));
    }
    return mergedProjects;
  }, [mergedProjects, searchQuery]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleFavoriteClick = async (projectId: string) => {
    const project = mergedProjects.find((p) => p.id === projectId);
    if (!project) return;
    if (!project.is_fav && favorites.length >= 3) {
      setToast({ message: "Not allowed more than 3 favorite items", type: "error" });
      return;
    }
    if (project.app_type === "API") return; // ignore favorites for API

    try {
      setFavoriteLoading(projectId);
      const payload: FavoriteToggleProject = {
        id: project.id,
        name: project.name,
        appUrl: project.app_url,
        appType: project.app_type,
        performance: project.performance,
        isFav: project.is_fav || false,
        isAiBox: project.isAiBox || false,
      };
      await dispatch(toggleFavorite({ project: payload, isFavorite: project.is_fav })).unwrap();
      setToast({
        message: project.is_fav ? `${project.name} removed from favorites` : `${project.name} added to favorites`,
        type: "success",
      });
    } catch {
      setToast({ message: "Failed to update favorites", type: "error" });
    } finally {
      setFavoriteLoading(null);
    }
  };

  const handleOpenProject = (projectId: string, projectName: string, projectUrl: string, isAiBox: boolean) => {
    router.push(`/apps/deployed-projects/${projectId}?name=${projectName}&isAiBox=${isAiBox}&appUrl=${projectUrl}`);
  };

  const getProjectIcon = (appType: string, isAiBox: boolean) => {
    if (isAiBox) {
      return "/assets/ai_app_icon.svg";
    }
    switch (appType) {
      case "Dashboard":
        return "/assets/dashboard-icon.svg";
      case "API":
        return "/assets/api-icon.svg";
      default:
        return "/assets/app-icon.svg";
    }
  };

  const handlePerformanceClick = (projectName: string) => {
    router.push("/apps/deployed-projects/ai-health?projectName=" + projectName);
  };

  const getPerformanceImage = (performance: string) => `/performing-${performance.toLowerCase()}.png`;

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo(0, 0);
    }
  }, [searchQuery]);

  return (
    <div
      ref={scrollContainerRef}
      className=" rounded-xl"
      style={{
        backgroundColor: "var(--background)",
        color: "var(--foreground)",
        height: "calc(100vh - 6rem)",
        overflowY: "auto",
      }}
    >
      <div className="container mx-auto p-4">
        {/* Heading */}
        <div className="mb-4">
          <h1
            className="text-2xl"
            style={{
              font: "normal normal medium 24px/29px Lato",
              letterSpacing: "0px",
              color: "#3B4154",
              textAlign: "left",
              opacity: 1,
            }}
          >
            Apps
          </h1>
        </div>

        {/* Horizontal line */}
        <div className="relative w-full border-b border-[1.2px] border-gray-300 mb-6 right-0"></div>

        {/* Search bar */}
        <div className="mb-4 mt-10">
          <Input
            type="text"
            placeholder="Search Projects..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full"
          />
        </div>
        <div
          className={
            "grid grid-cols-2 my-8 " +
            (isOpen ? "md:grid-cols-3 lg:grid-cols-3 gap-5 gap-x-5 pr-2" : "md:grid-cols-4 lg:grid-cols-4 gap-5")
          }
        >
          {filteredProjects.map((project) => {
            // console.log(project, " this is project data");
            const isApi = project.app_type === "API";
            const isLoading = favoriteLoading === project.id;
            const favText = isApi ? "Add to Favorites" : project.is_fav ? "Remove from Favorites" : "Add to Favorites";
            const textColor = isApi ? "gray" : "#00B2A1";

            return (
              <div key={project.id} className="border border-[#CFD2DE] rounded-lg p-4 flex flex-col justify-between">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex flex-col justify-center">
                    <img src={getProjectIcon(project.app_type, project.isAiBox ?? false)} alt={project.app_type} className="w-14 h-14 mb-1" />
                    <h2
                      style={{
                        font: "normal normal medium 16px/19px Lato",
                        letterSpacing: "0px",
                        color: "#1B1D21",
                        textAlign: "left",
                        opacity: 1,
                      }}
                    >
                      {project.name}
                    </h2>
                  </div>
                  {/* <div
                    className="flex flex-col items-center text-center cursor-pointer"
                    onClick={() => handlePerformanceClick(project.name)}
                  >
                    <img
                      src={getPerformanceImage(project.performance)}
                      alt={project.performance}
                      className="w-auto h-auto mb-1"
                    />
                    <span
                      style={{
                        font: "normal normal normal 14px/19px Lato",
                        letterSpacing: "0px",
                        color: "#666F8F",
                        textAlign: "center",
                        opacity: 1,
                      }}
                    >
                      AI Health
                    </span>
                  </div> */}
                </div>

                {/* Bottom section: Favorite button + Open button */}
                <div className="flex justify-between items-center mt-4">
                  <div
                    className="relative cursor-pointer flex items-center mr-2"
                    style={{ cursor: isApi ? "not-allowed" : "pointer" }}
                    onMouseEnter={() => {
                      if (isApi) setTooltipProjectId(project.id);
                    }}
                    onMouseLeave={() => {
                      if (isApi) setTooltipProjectId(null);
                    }}
                    onClick={() => !isApi && !isLoading && handleFavoriteClick(project.id)}
                  >
                    {isLoading ? (
                      <div className="flex items-center">
                        <DeployedPageLoader />
                      </div>
                    ) : isApi ? (
                      <img
                        src="/assets/favourite-icon.png"
                        alt="Add to Favorites"
                        style={{ filter: "grayscale(100%)" }}
                        className="w-3 h-3 mr-2"
                      />
                    ) : (
                      <img
                        src={project.is_fav ? "/assets/un-favourite-icon.png" : "/assets/favourite-icon.png"}
                        alt={favText}
                        className="w-3 h-3 mr-2"
                      />
                    )}

                    {!isLoading && (
                      <span
                        style={{
                          font: "normal normal normal 16px/17px Lato",
                          letterSpacing: "0px",
                          color: textColor,
                          textAlign: "left",
                          opacity: 1,
                        }}
                      >
                        {favText}
                      </span>
                    )}

                    {isApi && tooltipProjectId === project.id && (
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50">
                        <div
                          className="
                            relative bg-white text-black text-sm py-2 px-3 rounded-md
                            shadow-lg whitespace-nowrap border border-gray-200 
                          "
                        >
                          Favorites for API Projects Coming Soon!
                          <div
                            className="
                              relative -bottom-2 z-4 transform -translate-x-1/2
                              w-0 h-0 border-l-4 border-r-4 border-t-4
                              border-l-transparent border-r-transparent border-t-white
                            "
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <Button onClick={() => handleOpenProject(project.id, project.name, project.app_url, project.isAiBox ?? false)}>Open</Button>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {toast && <Toast message={toast.message} type={toast.type} onClose={() => setToast(null)} />}
    </div>
  );
}

// Loader component (unchanged)
const DeployedPageLoader = () => {
  return (
    <div className="flex justify-center items-center">
      <div
        style={{
          width: "25px",
          height: "25px",
          border: "2px solid white",
          borderRadius: "50%",
          animation: "spin 1s linear infinite",
          borderTopColor: "#00B2A1",
        }}
      ></div>
      <style jsx>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
};
