"use client";

import InventoryAbout from "@/components/common/InventoryAbout/inventory_about";
import SiteGPTAbout from "@/components/common/siteGPTAbout/sitegptabout";
import FlowchartAIAbout from "@/components/common/FlowchartAIAbout/flowchart_ai_about";
import DashboardAIAbout from "@/components/common/DashboardAIAbout/dashboard_ai_about";
import ResumeAnalyzerAIAbout from "@/components/common/ResumeAnalyzerAIAbout/resume_analyzer_ai_about";
import { getToken } from "@/service/methods";
import { getProjectUrl } from "@/service/projects";
import { Info, Loader2 } from "lucide-react";
import { useSearchParams, useParams } from "next/navigation";
import { useEffect, useState, useRef } from "react";
import { RiExpandDiagonalSLine } from "react-icons/ri";

export default function ProjectDetailsPage() {
  const searchParams = useSearchParams();
  const [showAboutModal, setShowAboutModal] = useState(false);
  const [projectUrl, setProjectUrl] = useState<string>('');
  const projectName = searchParams.get("name") ?? "Unknown Project";
  const { id: projectId } = useParams() as { id: string };
  const [isLoading, setIsLoading] = useState(true);
  const [isFullScreen, setIsFullScreen] = useState<boolean>(false);
  const appUrl = searchParams.get("appUrl") ?? "#";
  const isAiBox = searchParams.get("isAiBox") ?? false;
  const token = getToken();
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const handleFullScreen = () => {
    if (iframeRef.current) {
      if (!document.fullscreenElement) {
        iframeRef.current.requestFullscreen();
      } else {
        document.exitFullscreen();
      }
    }
  };

  useEffect(() => {
    const onFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", onFullScreenChange);

    return () => {
      document.removeEventListener("fullscreenchange", onFullScreenChange);
    };
  }, []);

  useEffect(() => {
    const fetchProjectUrl = async () => {
      try {
        setIsLoading(true);
        const response = await getProjectUrl(projectId);
        const urlWithToken = `${response.data.url}${response.data.url.includes('?') ? '&' : '?'}authToken=${token}`;
        setProjectUrl(urlWithToken);
      } catch (error) {
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjectUrl();
  }, [projectId, token]);

  useEffect(() => {
    const onFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", onFullScreenChange);

    return () => {
      document.removeEventListener("fullscreenchange", onFullScreenChange);
    };
  }, []);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [projectId]);

  const getIframeUrl = (url: string) => {
    return `${url}${url.includes('?') ? '&' : '?'}authToken=${token}`;
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: "var(--background)", color: "var(--foreground)" }}>
      <div className="container mx-auto p-4">
        <div className=" flex justify-between mb-2 pb-2 text-2xl">
          <h1
            style={{
              font: "normal normal medium 24px/29px Lato",
              letterSpacing: "0px",
              color: "#3B4154",
              textAlign: "left",
              opacity: 1,
            }}
          >
            {projectName}
          </h1>
          <div className="flex items-center gap-4">
            {isAiBox === "true" && (
              <div
                className="flex items-center gap-2 cursor-pointer hover:opacity-80"
                onClick={() => setShowAboutModal(true)}
              >
                <Info className="w-4 h-4 text-gray-400" />
                <p className="text-gray-500 text-sm" >About App</p>
              </div>
            )}
            {!isFullScreen && (
              <div className="flex items-center gap-2 cursor-pointer" onClick={handleFullScreen}>
                <RiExpandDiagonalSLine className="text-[#00B2A1] font-[300] opacity-70" />
                <div className="text-[14px] text-[#00B2A1]"> Full Screen</div>
              </div>
            )}
          </div>
        </div>
        <div className="mt-4 border rounded-sm overflow-hidden" style={{ height: "80vh" }}>
          {isLoading ? (
            <div className="flex flex-col justify-center items-center h-full">
              <Loader2 className=" h-8 w-6 animate-spin text-[#00B2A1]" />
              <p className="text-gray-500 text-sm">Getting Project...</p>
            </div>
          ) : (
            <iframe
              src={projectUrl ? getIframeUrl(projectUrl) : getIframeUrl(appUrl)}
              title="Project App"
              className="w-full h-full"
              allowFullScreen
              ref={iframeRef}
            />
          )}
        </div>

        {/* About App Modal */}
        {showAboutModal && (
          <>
            {projectName === "Site GPT" ? (
              <SiteGPTAbout onClose={() => setShowAboutModal(false)} />
            ) : projectName === "FlowchartAI" ? (
              <FlowchartAIAbout onClose={() => setShowAboutModal(false)} />
            ) : projectName === "Data dashboard" ? (
              <DashboardAIAbout onClose={() => setShowAboutModal(false)} />
            ) : projectName === "Resume Analyser" ? (
              <ResumeAnalyzerAIAbout onClose={() => setShowAboutModal(false)} />
            ) : (
              <InventoryAbout onClose={() => setShowAboutModal(false)} />
            )}
          </>
        )}


      </div>
    </div >
  );
}
