"use client";
import React, { useState, useRef } from "react";
import { useSelector } from "react-redux";
import { userService } from "@/service/api";
import { toast } from "react-toastify";
import { useDispatch } from "react-redux";
import { setUserInfo } from "@/lib/store/features/user/userSlice";
import filesService from "@/service/files";
import { EyeIcon, Loader2 } from "lucide-react";
import { EyeSlashIcon } from "@heroicons/react/24/solid";

const ProfilePage = () => {
  const userInfo = useSelector((state: any) => state.user.userInfo);
  const imgBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [profilePicUrl, setProfilePicUrl] = useState(
    userInfo?.user?.thumbnail || "/person_FILL0_wght300_GRAD0_opsz24 (2).svg"
  );
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isImageLoading, setIsImageLoading] = useState(false);
  // Combined state for all form data
  const [formData, setFormData] = useState({
    username: userInfo?.user?.username || "",
    fullName: userInfo?.user?.name || "",
    email: userInfo?.user?.email || "",
    about: userInfo?.user?.about || "",
    thumbnail: userInfo?.user?.thumbnail || "",
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  // Track what fields have been modified
  const [modifiedFields, setModifiedFields] = useState({
    profile: false,
    password: false,
  });

  // Add this to your existing state
  const [showPasswords, setShowPasswords] = useState({
    oldPassword: false,
    newPassword: false,
    confirmPassword: false,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id]: value,
    }));

    // Track which section was modified
    if (["oldPassword", "newPassword", "confirmPassword"].includes(id)) {
      setModifiedFields((prev) => ({ ...prev, password: true }));
    } else {
      setModifiedFields((prev) => ({ ...prev, profile: true }));
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setIsImageLoading(true);
      filesService.uploadFile(file, "Profile Picture", userInfo?.user?.username || userInfo?.user?._id).then((res) => {
        formData.thumbnail = res.data.url;
        setProfilePicUrl(res.data.url);
        setIsImageLoading(false);
      });
      const imageUrl = URL.createObjectURL(file);
      setModifiedFields((prev) => ({ ...prev, profile: true }));
    }
  };

  // Add this function to toggle password visibility
  const togglePasswordVisibility = (field: "oldPassword" | "newPassword" | "confirmPassword") => {
    setShowPasswords((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const handleUpdateAll = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Update profile if profile fields were modified
      if (modifiedFields.profile) {
        const profileResponse = await userService.updateUserInfo({
          name: formData.fullName,
          email: formData.email,
          about: formData.about,
          thumbnail: profilePicUrl,
        });
        dispatch(setUserInfo(profileResponse.data));
      }

      // Update password if password fields were modified
      if (modifiedFields.password) {
        if (formData.newPassword !== formData.confirmPassword) {
          toast.error("Passwords do not match!");
          setIsLoading(false);
          return;
        }
        if (formData.newPassword && formData.oldPassword) {
          await userService.resetPassword({
            email: formData.email,
            oldPassword: formData.oldPassword,
            newPassword: formData.newPassword,
          });
          // Clear password fields after successful update
          setFormData((prev) => ({
            ...prev,
            oldPassword: "",
            newPassword: "",
            confirmPassword: "",
          }));
        }
      }

      // Show success message
      toast.success("Profile updated successfully!");
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollTo(0, 0);
      }

      // Reset modified fields tracking
      setModifiedFields({ profile: false, password: false });
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="p-6 bg-gray-50 rounded-xl"
      ref={scrollContainerRef}
      style={{
        height: "calc(100vh - 6rem)",
        overflowY: "auto",
      }}
    >
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">Profile Settings</h1>

        <form onSubmit={handleUpdateAll}>
          <div className="bg-white rounded-lg border border-[#E0E0E0] p-6 mb-6">
            <div className="mb-4">
              <label htmlFor="username" className="block text-sm font-bold text-gray-700 mb-1">
                Username
              </label>
              <input
                type="text"
                id="username"
                value={formData.username}
                onChange={handleInputChange}
                disabled
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="fullName" className="block text-sm font-bold text-gray-700 mb-1">
                Full name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="fullName"
                value={formData.fullName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-[#CFD2DE] rounded-md focus:outline-none focus:ring-1 focus:ring-[#00B2A1]"
                placeholder="Enter your full name"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="email" className="block text-sm font-bold text-gray-700 mb-1">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                id="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-[#CFD2DE] rounded-md focus:outline-none focus:ring-1 focus:ring-[#00B2A1]"
                placeholder="Enter your email"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="about" className="block text-sm font-bold text-gray-700 mb-1">
                About
              </label>
              <textarea
                id="about"
                value={formData.about}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-[#CFD2DE] rounded-md min-h-[100px] focus:outline-none focus:ring-1 focus:ring-[#00B2A1]"
                placeholder="A little information about yourself"
              />
              <p className="text-sm text-gray-500 mt-1">You can use Markdown formatting here</p>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-bold text-gray-700 mb-1">
                Profile picture <span className="text-red-500">*</span>
              </label>
              <div className="flex items-center gap-4">
                <img
                  src={imgBaseUrl?.replace("/api", "") + profilePicUrl}
                  alt="Profile"
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div className="flex-1 flex gap-2">
                  <input
                    type="text"
                    readOnly
                    value={profilePicUrl}
                    onChange={(e) => setProfilePicUrl(e.target.value)}
                    className="flex-1 px-3 py-2 border border-[#CFD2DE] rounded-md focus:outline-none focus:ring-1 focus:ring-[#00B2A1]"
                    placeholder="https://example.com/image.png"
                  />
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileUpload}
                    accept="image/*"
                    className="hidden"
                  />
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isImageLoading}
                    className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    {isImageLoading ? (
                      <div className="flex items-center gap-2">
                        <Loader2 className="w-4 h-4 animate-spin" /> Uploading...
                      </div>
                    ) : (
                      "Upload"
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={() => setProfilePicUrl("")}
                    className="px-4 py-2 border border-red-500 text-red-500 rounded-md hover:bg-red-50"
                  >
                    Remove
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-[#E0E0E0] p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Change password</h2>
            <div className="mb-4">
              <label htmlFor="oldPassword" className="block text-sm font-bold text-gray-700 mb-1">
                Old Password
              </label>
              <div className="relative">
                <input
                  type={showPasswords.oldPassword ? "text" : "password"}
                  id="oldPassword"
                  value={formData.oldPassword}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-[#CFD2DE] rounded-md focus:outline-none focus:ring-1 focus:ring-[#00B2A1]"
                  placeholder="Enter your current password"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility("oldPassword")}
                  className="absolute right-3 top-1/2 -translate-y-1/2"
                >
                  {showPasswords.oldPassword ? (
                    <EyeSlashIcon className="w-5 h-5" />
                  ) : (
                    <EyeIcon className="w-5 h-5" />
                  )}
                </button>
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="newPassword" className="block text-sm font-bold text-gray-700 mb-1">
                New Password
              </label>
              <div className="relative">
                <input
                  type={showPasswords.newPassword ? "text" : "password"}
                  id="newPassword"
                  value={formData.newPassword}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-[#CFD2DE] rounded-md focus:outline-none focus:ring-1 focus:ring-[#00B2A1]"
                  placeholder="Enter your new password"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility("newPassword")}
                  className="absolute right-3 top-1/2 -translate-y-1/2"
                >
                  {showPasswords.newPassword ? (
                    <EyeSlashIcon className="w-5 h-5" />
                  ) : (
                    <EyeIcon className="w-5 h-5" />
                  )}
                </button>
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="confirmPassword" className="block text-sm font-bold text-gray-700 mb-1">
                Confirm Password
              </label>
              <div className="relative">
                <input
                  type={showPasswords.confirmPassword ? "text" : "password"}
                  id="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-[#CFD2DE] rounded-md focus:outline-none focus:ring-1 focus:ring-[#00B2A1]"
                  placeholder="Confirm your new password"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility("confirmPassword")}
                  className="absolute right-3 top-1/2 -translate-y-1/2"
                >
                  {showPasswords.confirmPassword ? (
                    <EyeSlashIcon className="w-5 h-5" />
                  ) : (
                    <EyeIcon className="w-5 h-5" />
                  )}
                </button>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-4">
            <button type="button" className="px-4 py-2 border font-bold border-red-500 text-red-500 rounded-md hover:bg-red-50">
              Delete Account
            </button>
            <button
              type="submit"
              disabled={isLoading || (!modifiedFields.profile && !modifiedFields.password)}
              className="px-4 py-2 bg-[#00B2A1] hover:bg-[#00A090] font-bold text-white rounded-md disabled:opacity-50"
            >
              {isLoading ? "Updating..." : "Update Details"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProfilePage;
