"use client";
import MainContent from "@/components/ui/MainContent/MainContent";
import { SidebarProvider } from "@/context/sidebar-context";
import AuthWrapper from "./AuthWrapper";
import IntercomWrapper from "@/components/common/intercom/IntercomProvider";
import { Sidebar as SidebarV2 } from "@/components/common/sidebar";
import TopBar from "@/components/ui/TopBar";
import ScrollToTop from "@/components/common/ScrollToTop/ScrollToTop";
import { useEffect, useState } from "react";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [isMobile, setIsMobile] = useState(false);

  // useEffect(() => {
  //   initializeGA();
  //   trackPageView(pathname);
  // }, [pathname]);

  useEffect(() => {
    // Function to check if viewport is mobile
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768); // 768px is a common breakpoint for mobile
    };

    // Check on initial load
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener("resize", checkIfMobile);

    // Cleanup event listener on component unmount
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  return (
    <SidebarProvider>
      <AuthWrapper>
        <IntercomWrapper>
          {/* TopBar shown on both desktop and mobile */}
          <TopBar />
          {/* Only show sidebar on desktop */}
          {!isMobile && <SidebarV2 />}

          <ScrollToTop />
          <MainContent>{children}</MainContent>
        </IntercomWrapper>
      </AuthWrapper>
    </SidebarProvider>
  );
}
