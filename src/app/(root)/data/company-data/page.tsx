"use client";
import Filter from "@/components/common/filter/filter";
import Sort from "@/components/common/sort/sort";
import Tile from "@/components/common/tile/tile";
import { MagnifyingGlassIcon, PlusIcon, ChevronDownIcon } from "@heroicons/react/24/solid";
import { useEffect, useState } from "react";
import { dataService } from "@/service/dataService";
import { useRouter } from "next/navigation";
import Loader from "@/components/common/loader/loader";
import { Pagination } from "@/components/common/pagination/pagination";
import AddDataset from "@/components/pages/AddDataset/AddDataset";

interface Filters {
  tags: string[];
  fileTypes: string[];
  licenses: string[];
}

export default function Projects() {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [data, setData] = useState<any>(null); // Store API response
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [searchInput, setSearchInput] = useState<string>("");
  const [isClearOpen, setIsClearOpen] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortValue, setSortValue] = useState<string>("views");
  const [sortOrder, setSortOrder] = useState<number>(-1);
  const [selectedFilters, setSelectedFilters] = useState<Filters>({
    tags: [],
    fileTypes: [],
    licenses: [],
  });
  const [urlQuery, setUrlQuery] = useState({
    page: 1,
    limit: 10,
    search: "",
    tags: "",
    license: "",
    fileTypes: "",
    sortBy: sortValue,
    sortOrder: sortOrder,
  });

  const handleViewDataset = (id: string) => {
    router.push(`/data/company-data/dataset/${id}`);
  };

  useEffect(() => {
    setUrlQuery((prev) => ({
      ...prev,
      tags: selectedFilters.tags.join(","), // Convert array to comma-separated string
      license: selectedFilters.licenses.join(","),
      fileTypes: selectedFilters.fileTypes.join(","),
    }));
  }, [selectedFilters]);

  const hasFilters = Object.values(selectedFilters).some((arr) => arr.length > 0);

  const removeFilter = (category: keyof Filters, value: string) => {
    setSelectedFilters((prevFilters) => ({
      ...prevFilters,
      [category]: prevFilters[category].filter((item) => item !== value),
    }));
  };

  useEffect(() => {
    setUrlQuery((prev) => ({
      ...prev,
      sortBy: sortValue,
      sortOrder: sortOrder,
    }));
  }, [sortValue, sortOrder]);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      setUrlQuery((prev) => ({
        ...prev,
        search: searchInput,
      }));
      setIsClearOpen(true);
    }
  };

  const handleClearSearch = () => {
    setSearchInput("");
    setUrlQuery((prev) => ({
      ...prev,
      search: "",
    }));
    setIsClearOpen(false);
  };

  useEffect(() => {
    setUrlQuery((prev) => ({
      ...prev,
      page: currentPage,
    }));
  }, [currentPage]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const result = await dataService.getCompanyData(urlQuery);
        setData(result);
      } catch (err) {
        setError("Failed to fetch data.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [urlQuery]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="font-[Lato] p-8">
      <div className="flex justify-around">
        <h1 className="text-xl w-3/4 font-medium text-[#3B4154] items-start mt-2">Company Data</h1>
        <div className="w-1/4 flex justify-end">
          <button
            className="flex items-center rounded-[3px] px-4 py-2 bg-teal-500 text-white mt-2 text-sm"
            onClick={() => setIsOpen(true)}
          >
            <PlusIcon className="w-5 font-bold" /> Add Dataset
          </button>
        </div>
      </div>
      <div className="relative left-0 w-[100%] border-b mt-5"></div>
      <div className="flex gap-5 items-center mt-8">
        <h1 className=" font-semibold">{data?.pagination?.total} Dataset(s)</h1>
        {isClearOpen && (
          <button className="text-md text-teal-500 bg-white" onClick={handleClearSearch}>
            Clear Search
          </button>
        )}
      </div>

      <div className="flex justify-between items-center mt-2">
        <div
          className="
            flex items-center gap-1 justify-center 
            bg-[#F4F5F6] text-[#3B4154] 
            rounded-md overflow-hidden 
            border border-gray-300 
            focus-within:border-gray-500 
            transition-colors duration-300 ease-in-out
          "
        >
          <input
            type="text"
            placeholder="Search..."
            className="w-[306px] h-[34px] text-[#3B4154] outline-none pl-4"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            onKeyDown={handleKeyDown}
          />
        </div>

        <div className="flex gap-2">
          <Filter filters={data?.filters} selectedFilters={selectedFilters} setSelectedFilters={setSelectedFilters} />
          <Sort sortValue={sortValue} setSortValue={setSortValue} sortOrder={sortOrder} setSortOrder={setSortOrder} />
        </div>
      </div>

      {hasFilters && (
        <div className="flex flex-wrap gap-2 mt-6 rounded-lg items-center">
          {Object.entries(selectedFilters).map(([category, values]) => (
            <div key={category} className="flex gap-2 items-center">
              {values.length > 0 && <h1>{category.charAt(0).toLocaleUpperCase() + category.slice(1)}: </h1>}
              {values.map((value: any) => (
                <div
                  key={`${category}-${value}`}
                  className="flex items-center gap-2 bg-[#F4F5F6] text-[#3B4154] px-3 py-1 rounded-md text-sm"
                >
                  <div>{value}</div>
                  <button
                    onClick={() => removeFilter(category as keyof Filters, value)}
                    className="text-[#888FAA] text-sm"
                  >
                    ✖
                  </button>
                </div>
              ))}
            </div>
          ))}
          <button
            className="text-md text-teal-500 bg-white"
            onClick={() =>
              setSelectedFilters({
                tags: [],
                fileTypes: [],
                licenses: [],
              })
            }
          >
            Clear all
          </button>
        </div>
      )}

      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="flex flex-col gap-4 mt-4">
            <div>
              {data?.datasets.map((dataset: any) => (
                <div key={dataset._id}>
                  <Tile
                    title={dataset.title}
                    description={dataset.description}
                    tags={dataset.fileTypes}
                    button_text="View Dataset"
                    onButtonClick={() => handleViewDataset(dataset._id)}
                  />
                </div>
              ))}
            </div>
          </div>
          <div className="flex justify-between items-center mt-5">
            <div className="text-gray-600 text-sm">
              Showing {data?.datasets?.length || 0} of {data?.pagination?.total || 0}
            </div>
            {data?.pagination?.totalPages > 0 && (
              <Pagination
                totalPages={data?.pagination?.totalPages}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
              />
            )}
          </div>
        </>
      )}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity z-50"
          onClick={() => setIsOpen(false)}
        />
      )}
      <AddDataset isOpen={isOpen} setIsOpen={setIsOpen} title={"Add Company Data"} isWorldDataset={false} />
    </div>
  );
}
