"use client"

import React, { useState, useEffect } from "react"
import Image from "next/image"
import { X } from "lucide-react"
import Loader from "@/components/common/loader/loader"
import { getPlatformConnection, PlatformConnectionResponse } from "@/service/platform-connection"

interface ConnectionData {
  id: string
  title: string
  icon: string
  status: "Active" | "Down"
  lastStatusChange: string
}

interface PrivateNetworkTunnel {
  name: string
  status: "Active" | "Down"
  lastChange: string
}

interface DBConnection {
  name: string
  status: "Active" | "Down"
  dbms: string
  category: string
}

function mapStatus(isActive: boolean): "Active" | "Down" {
  return isActive ? "Active" : "Down"
}

export default function PlatformConnectionPage() {
  const [showPrivateNetworkPanel, setShowPrivateNetworkPanel] = useState(false)
  const [showDatabasePanel, setShowDatabasePanel] = useState(false)
  const [privateNetworkTunnels, setPrivateNetworkTunnels] = useState<PrivateNetworkTunnel[]>([])
  const [databaseConnections, setDatabaseConnections] = useState<DBConnection[]>([])
  const [connections, setConnections] = useState<ConnectionData[]>([
    {
      id: "database",
      title: "Database Connection",
      icon: "/platform-connection-database.svg",
      status: "Down",
      lastStatusChange: "No data available",
    },
  ])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    ; (async () => {
      try {
        const data: PlatformConnectionResponse = await getPlatformConnection()

        if (
          data.awsData &&
          data.awsData.vpnConnections &&
          data.awsData.vpnConnections.length > 0
        ) {
          setConnections(prev => [
            {
              id: "privateNetwork",
              title: "Private Network Connection",
              icon: "/platform-connection-network.svg",
              status: "Down",
              lastStatusChange: "No data available",
            },
            ...prev
          ])

          const tunnels = data.awsData.vpnConnections[0].tunnels
          const mappedTunnels: PrivateNetworkTunnel[] = tunnels.map((tunnel) => ({
            name: tunnel.tunnel_name,
            status: mapStatus(tunnel.tunnel_status === "UP"),
            lastChange: tunnel.last_status_change,
          }))
          setPrivateNetworkTunnels(mappedTunnels)

          const overallNetworkStatus = mappedTunnels.every((t) => t.status === "Active")
            ? "Active"
            : "Down"
          setConnections((prev) =>
            prev.map((conn) =>
              conn.id === "privateNetwork"
                ? {
                  ...conn,
                  status: overallNetworkStatus,
                  lastStatusChange: mappedTunnels[0]?.lastChange || "No data available",
                }
                : conn
            )
          )
        }

        if (data.companyData && data.companyData.length > 0) {
          const mappedDBConnections: DBConnection[] = data.companyData.map((db) => ({
            name: db.DB || "Unnamed Database",
            status: mapStatus(db.Sync),
            dbms: db.DBMS,
            category: db.Category || "General",
          }))
          setDatabaseConnections(mappedDBConnections)
          const overallDBStatus = mappedDBConnections.every((db) => db.status === "Active")
            ? "Active"
            : "Down"
          setConnections((prev) =>
            prev.map((conn) =>
              conn.id === "database" ? { ...conn, status: overallDBStatus } : conn
            )
          )
        }
      } catch (err: any) {
        setError("Failed to fetch platform connection data.")
      } finally {
        setIsLoading(false)
      }
    })()
  }, [])

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <Loader />
      </div>
    )
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-medium mb-6 text-gray-800">Platform Connection</h1>
      <hr className="mb-6 border-gray-300" />

      {error && <p className="text-red-600">{error}</p>}

      <div className="flex flex-wrap gap-6">
        {connections.map((connection) => (
          <div
            key={connection.id}
            className="border border-gray-300 rounded-lg p-4 w-full sm:w-64 flex flex-col items-center justify-between text-center"
          >
            <div className="w-full flex justify-end">
              <div
                className={`px-2 py-1 rounded-md flex items-center ${connection.status === "Active"
                  ? "bg-green-100 text-green-800"
                  : "bg-red-100 text-red-800"
                  }`}
                style={{ font: "normal normal medium 16px/24px Lato" }}
              >
                <div
                  className={`w-2 h-2 rounded-full mr-2 ${connection.status === "Active" ? "bg-green-500" : "bg-red-500"
                    }`}
                />
                {connection.status}
              </div>
            </div>
            <div className="flex flex-col items-center mt-2">
              <Image src={connection.icon} alt={connection.title} width={60} height={60} />
              <h2 className="font-semibold mt-2">{connection.title}</h2>
              {/* Only show "Last status change" for non-database connections */}
              {connection.id !== "database" && (
                <p className="text-sm text-gray-600 mt-1">
                  Last status change: {connection.lastStatusChange}
                </p>
              )}
            </div>
            <div className="mt-4">
              <button
                onClick={() =>
                  connection.id === "privateNetwork"
                    ? setShowPrivateNetworkPanel(true)
                    : setShowDatabasePanel(true)
                }
                style={{
                  background: "#00B2A1",
                  borderRadius: "4px",
                  opacity: 1,
                  font: "normal normal medium 16px/24px Lato",
                  letterSpacing: "0px",
                  color: "#FFFFFF",
                  textAlign: "right",
                  padding: "8px 16px",
                  cursor: "pointer",
                  transition: "opacity 0.2s ease-in-out",
                }}
                onMouseOver={(e) => {
                  (e.currentTarget as HTMLButtonElement).style.opacity = "0.9"
                }}
                onMouseOut={(e) => {
                  (e.currentTarget as HTMLButtonElement).style.opacity = "1"
                }}
              >
                View Details
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Always Rendered Private Network Panel */}
      <div className="fixed inset-0 z-40 pointer-events-none">
        {/* Overlay */}
        <div
          className={`absolute inset-0 bg-black bg-opacity-50 transition-opacity duration-300 ${showPrivateNetworkPanel ? "opacity-100 pointer-events-auto" : "opacity-0"
            }`}
          onClick={() => setShowPrivateNetworkPanel(false)}
        />
        {/* Side Panel */}
        <div
          className={`fixed top-0 right-0 z-50 w-full max-w-2xl h-full bg-white shadow-lg transform flex flex-col transition-transform duration-300 ease-in-out ${showPrivateNetworkPanel ? "translate-x-0" : "translate-x-full"
            }`}
        >
          <div className="bg-[#3B4154] h-12 w-full flex justify-between items-center px-4">
            <h2 className="text-lg font-semibold text-white">
              Private Network Connection (IPSeC Tunnel Details)
            </h2>
            <button onClick={() => setShowPrivateNetworkPanel(false)} className="rounded p-1">
              <X className="text-white" size={24} />
            </button>
          </div>
          <div className="p-6 mt-2">
            {privateNetworkTunnels.length > 0 ? (
              privateNetworkTunnels.map((tunnel, index) => (
                <div
                  key={index}
                  className="mb-6 p-4 border border-gray-200 rounded-lg"
                  style={{
                    font: "normal normal medium 20px/24px Lato",
                    letterSpacing: "0px",
                    color: "#3B4154",
                    textAlign: "left",
                  }}
                >
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-xl" style={{ margin: 0 }}>
                      {tunnel.name}
                    </h3>
                    <div
                      className={`px-2 py-1 rounded-md flex items-center ${tunnel.status === "Active"
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                        }`}
                      style={{
                        font: "normal normal medium 20px/24px Lato",
                        letterSpacing: "0px",
                        color: "#3B4154",
                      }}
                    >
                      <div
                        className={`w-2 h-2 rounded-full mr-2 ${tunnel.status === "Active" ? "bg-green-500" : "bg-red-500"
                          }`}
                      />
                      {tunnel.status}
                    </div>
                  </div>
                  <p style={{ margin: 0, marginTop: "8px" }}>
                    Last status change: {tunnel.lastChange}
                  </p>
                </div>
              ))
            ) : (
              <p>No data available</p>
            )}
          </div>
        </div>
      </div>

      {/* Always Rendered Database Panel */}
      <div className="fixed inset-0 z-40 pointer-events-none">
        {/* Overlay */}
        <div
          className={`absolute inset-0 bg-black bg-opacity-50 transition-opacity duration-300 ${showDatabasePanel ? "opacity-100 pointer-events-auto" : "opacity-0"
            }`}
          onClick={() => setShowDatabasePanel(false)}
        />
        {/* Side Panel */}
        <div
          className={`fixed top-0 right-0 z-50 w-full max-w-2xl h-full bg-white shadow-lg transform flex flex-col transition-transform duration-300 ease-in-out ${showDatabasePanel ? "translate-x-0" : "translate-x-full"
            }`}
        >
          <div className="bg-[#3B4154] h-12 w-full flex justify-between items-center px-4">
            <h2 className="text-lg font-semibold text-white">Database Connection</h2>
            <button onClick={() => setShowDatabasePanel(false)} className="rounded p-1">
              <X size={24} className="text-white" />
            </button>
          </div>
          <div className="p-6 mt-2">
            {databaseConnections.length > 0 ? (
              databaseConnections.map((db, index) => (
                <div key={index} className="mb-6 p-4 border border-gray-200 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex items-center">
                      <Image
                        src="/platform-connection-database.svg"
                        alt="DB Icon"
                        width={16}
                        height={16}
                        className="mr-2"
                      />
                      <h3
                        style={{
                          font: "Lato",
                          letterSpacing: "0px",
                          color: "#3B4154",
                          textAlign: "left",
                        }}
                        className="text-lg font-medium mx-2"
                      >
                        {db.name}
                      </h3>
                      <div className="ml-2 relative group">
                        <Image
                          src="/info.svg"
                          alt="Info"
                          width={14}
                          height={14}
                          className="cursor-pointer"
                        />
                        <span className="hidden group-hover:block absolute z-10 bg-white border border-gray-200 rounded p-2 text-sm w-48 -left-24 top-6">
                          Database connection name
                        </span>
                      </div>
                    </div>
                    <div
                      className={`px-2 py-1 rounded-md flex items-center ${db.status === "Active"
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                        }`}
                      style={{
                        font: "normal normal medium 16px/24px Lato",
                        letterSpacing: "0px",
                      }}
                    >
                      <div
                        className={`w-2 h-2 rounded-full mr-2 ${db.status === "Active" ? "bg-green-500" : "bg-red-500"
                          }`}
                      />
                      {db.status}
                    </div>
                  </div>
                  <hr className="my-2 border-gray-200" />
                  <p
                    style={{
                      font: "normal normal medium 20px/24px Lato",
                      letterSpacing: "0px",
                      color: "#3B4154",
                      textAlign: "left",
                      margin: 0,
                    }}
                  >
                    <span style={{ fontWeight: "bold" }}>DBMS:</span> {db.dbms}
                  </p>
                  <p
                    style={{
                      font: "normal normal medium 20px/24px Lato",
                      letterSpacing: "0px",
                      color: "#3B4154",
                      textAlign: "left",
                      marginTop: "4px",
                    }}
                  >
                    <span style={{ fontWeight: "bold" }}>Category:</span> {db.category}
                  </p>
                </div>
              ))
            ) : (
              <p>No data available</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
