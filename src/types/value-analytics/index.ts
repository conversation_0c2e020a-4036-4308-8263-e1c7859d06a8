export interface Metric {
  dollarValue: number;
  name: string;
  preAI: number;
  postAI: number;
  unit?: string;
}

export interface Benefit {
  name: string;
  metrics: Metric[];
}

export interface ValuePillar {
  name:
    | "Operational Efficiency"
    | "Customer Experience"
    | "Revenue Growth"
    | "Cost Reduction";
  benefits: Benefit[];
}

export interface Project {
  id: string;
  name: string;
  valuePillars: ValuePillar[];
  totalInvestment: number;
  roi: number;
}

export interface DashboardData {
  projects: Project[];
}
