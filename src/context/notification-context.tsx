"use client";

import type React from "react";
import { createContext, useContext, useState, useEffect } from "react";
import { io, type Socket } from "socket.io-client";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";

interface NotificationContextType {
  socket: Socket | null;
  connected: boolean;
}

const NotificationContext = createContext<NotificationContextType>({
  socket: null,
  connected: false,
});

export const useNotificationContext = () => useContext(NotificationContext);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const token = useSelector((state: RootState) => state.user.token);
  const isAuthenticated = useSelector(
    (state: RootState) => state.user.isAuthenticated
  );

  useEffect(() => {
    let socketInstance: Socket | null = null;

    if (isAuthenticated && token) {
      // Initialize socket connection
      socketInstance = io(
        process.env.NEXT_PUBLIC_SOCKET_BASE_URL || "http://18.234.247.164:5004",
        {
          auth: { token },
          transports: ["websocket"],
        }
      );

      socketInstance.on("connect", () => {
        // console.log("!!!!!!!!!!!!!!!!!!!!Socket connected");
        setConnected(true);
      });

      socketInstance.on("disconnect", () => {
        // console.log("!!!!!!!!!!!!!!Socket disconnected");
        setConnected(false);
      });

      socketInstance.on("connect_error", (err) => {
        console.error("Socket connection error:", err);
        setConnected(false);
      });

      setSocket(socketInstance);
    }

    // Cleanup function
    return () => {
      if (socketInstance) {
        socketInstance.disconnect();
        setSocket(null);
        setConnected(false);
      }
    };
  }, [isAuthenticated, token]);

  return (
    <NotificationContext.Provider value={{ socket, connected }}>
      {children}
    </NotificationContext.Provider>
  );
};
