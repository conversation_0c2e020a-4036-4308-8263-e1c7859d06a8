{"name": "sutraai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@cyntler/react-doc-viewer": "^1.17.0", "@heroicons/react": "^2.2.0", "@next/font": "^14.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@reduxjs/toolkit": "^2.5.1", "class-variance-authority": "^0.7.1", "crypto-js": "^4.2.0", "framer-motion": "^12.5.0", "install": "^0.13.0", "leaflet": "^1.9.4", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-ga4": "^2.1.0", "react-icons": "^5.4.0", "react-leaflet": "^5.0.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-select": "^5.10.0", "react-spinners": "^0.15.0", "react-toastify": "^11.0.3", "react-use-intercom": "^5.4.3", "recharts": "^2.15.1", "redux-persist": "^6.0.0", "remark-gfm": "^4.0.1", "sharp": "^0.33.5", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.0.1"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/leaflet": "^1.9.16", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "axios": "^1.7.9", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}