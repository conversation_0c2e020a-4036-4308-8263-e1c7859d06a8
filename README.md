## Sutra AI frontend project

### Run the project

**Prerequisites:**

- Node.js (v22 or higher)
- npm (v10 or higher)

**Steps:**

1. Clone the repository

```bash
git clone https://<your-username>@bitbucket.org/vttech987/sutra.ai-frontend.git
```

*Note: Replace `<your-username>` with your actual Bitbucket username.*  

2. Install dependencies

```bash
npm install
```

3. Environment variables

Copy the `.env.example` file to `.env` and add the following variables:

```bash
cp .env.example .env
```

And add update `NEXT_PUBLIC_API_BASE_URL` with your actual backend api url.

4. Run the project

```bash
npm run dev
```

5. Open your browser and navigate to `http://localhost:3000` to view the project.
