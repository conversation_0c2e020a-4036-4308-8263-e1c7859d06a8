import type { Config } from "tailwindcss";

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        sidebarBackground: "var(--sidebar-background)",
        sidebarBackgroundHover: "var(--sidebar-background-hover)",
        sidebarItemSelected: "var(--sidebar-item-selected)",
        sidebarFavText: "var(--sidebar-fav-text)",
        sidebarBorder: "var(--sidebar-border)",
        dataPreviewTabsSelectorBg: "var(--data-preview-tabs-selector-bg)",
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
      },
    },
  },
  plugins: [],
} satisfies Config;
